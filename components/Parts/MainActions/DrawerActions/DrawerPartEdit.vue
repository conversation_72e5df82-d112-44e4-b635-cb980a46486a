<template>
  <a-spin class="fc-spin-container" v-if="isLoadingPart"></a-spin>
  <a-form :form="form" @submit="handleSubmit" :colon="false" class="fc-v2-drawer-container" v-else>
    <div class="fc-v2-drawer-container__body">
      <div class="fc-checklist-container">
        <part-form :dataSource="part" />
      </div>
    </div>
    <div class="fc-global-drawer__footer fc-v2-drawer-footer">
      <div class="fc-global-drawer__footer--group-buttons">
        <a-button size="large" type="default" @click="onCancel">Cancel</a-button>
        <a-button size="large" type="primary" html-type="submit" :loading="isLoading">Complete</a-button>
      </div>
    </div>
  </a-form>
</template>

<script>
import { mapState } from 'vuex';

import FcStatusTag from '../../../Shared/FcStatusTag.vue';
import PartForm from '../Actions/PartForm.vue';

import { LABEL_TYPES } from '../../../../utils/consts';

export default {
  components: { FcStatusTag, PartForm },
  props: {
    data: Object,
  },
  computed: {
    ...mapState({
      selectedAssets: (state) => state.linked_items.selectedAssets,
      selectedVendors: (state) => state.linked_items.selectedVendors,
      selectedPart: (state) => state.parts.selectedPart,
      part: (state) => state.parts.part,
      isRemove: (state) => state.parts.isRemove,
      pageInformation: (state) => state.parts.page_information,
      selectedLabels: (state) => state.checklist_labels[LABEL_TYPES.part.key].selected_labels,
      labelOptions: (state) => state.checklist_labels[LABEL_TYPES.part.key].label_options,
    }),
    partData() {
      return this.data.id !== 0 ? this.data : this.selectedPart;
    },
    partId() {
      return this.partData.id || this.$route.query.part_id;
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      isLoading: false,
      isLoadingPart: false,
    };
  },
  watch: {
    selectedPart() {
      this.$store.dispatch('parts/setIsRemove', false);
    },
  },
  mounted() {
    this.isLoadingPart = true;
    this.$store
      .dispatch('parts/getPart', this.partId)
      .then((res) => {
        let selectedLabelIds = res.labels.map((labelObj) => labelObj.id);
        this.$store.dispatch('checklist_labels/setSelectedLabels', {
          labelType: LABEL_TYPES.part,
          selected_labels: selectedLabelIds,
        });
        let unauthorizedLabels = res.labels.filter((labelObj) => !this.includedInLabelOptions(labelObj));
        this.$store.commit('checklist_labels/SET_UNAUTHORIZED_LABEL_OPTIONS', {
          labelType: LABEL_TYPES.part,
          labels: unauthorizedLabels,
        });
      })
      .catch(() => (this.isLoadingPart = false))
      .finally(() => {
        this.isLoadingPart = false;
      });
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();
      this.isLoading = true;
      this.form.validateFields(async (err, values) => {
        if (!err) {
          this.isRemove && (await this.$store.dispatch('parts/removePartImage', this.part.attachments[0].id));

          let formData = new FormData();

          formData.append('name', values.name);
          formData.append('quantity', values.quantity);
          formData.append('asset_ids', JSON.stringify([...this.selectedAssets.map((asset) => asset.id)]));
          formData.append('label_ids', JSON.stringify(this.selectedLabels));
          formData.append('vendor_ids', JSON.stringify([...this.selectedVendors.map((vendor) => vendor.id)]));
          values.part_store_id && formData.append('part_store_id', values.part_store_id);

          if (values.images.length > 0 && values.images[0].originFileObj) {
            let imageForm = new FormData();
            imageForm.append('file', values.images[0].originFileObj);
            imageForm.append('attachment_ids', JSON.stringify([]));
            let res = await this.$store.dispatch('parts/uploadPartImage', imageForm);
            res && formData.append('attachment_ids', JSON.stringify([res.id]));
          }

          this.$store
            .dispatch('parts/updatePart', {
              id: this.partData.id,
              payload: formData,
            })
            .then((res) => {
              this.isLoading = false;
              this.$store.dispatch('parts/loadAll', {
                page: this.pageInformation.page,
                per_page: this.pageInformation.per_page,
              }),
                this.$message.success('Part Updated Successfully!');
              this.$store.dispatch('parts/setIsRemove', false);
              this.$store.dispatch('parts/setIsShowDrawer', false);
            })
            .catch((error) => {
              this.isLoading = false;
              this.$message.error(error.response.data.message);
            });
        } else {
          this.isLoading = false;
        }
      });
    },
    onCancel() {
      this.$store.dispatch('parts/setIsRemove', false);
      this.$store.dispatch('parts/setIsShowDrawer', false);
    },
    includedInLabelOptions(label) {
      return this.labelOptions.some((option) => option.id === label.id);
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  flex-basis: 30%;
}
</style>
