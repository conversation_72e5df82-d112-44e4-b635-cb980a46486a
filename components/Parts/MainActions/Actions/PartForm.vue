<template>
  <div class="fc-partial-form">
    <a-form-item label="Part Name">
      <a-input
        v-decorator="[
          'name',
          {
            rules: [{ required: true, message: 'Please enter part name' }],
            initialValue: dataSource.name,
          },
        ]"
        placeholder="Enter Part name"
        size="large"
      />
    </a-form-item>
    <a-form-item label="Starting Quantity" v-show="!dataSource.quantity">
      <a-input-number
        v-decorator="[
          'quantity',
          {
            rules: [{ required: true, message: 'Please enter quantity' }],
            initialValue: defaultQuantity,
          },
        ]"
        placeholder="Enter Quantity"
        size="large"
        style="width:100%"
      />
    </a-form-item>
    <a-form-item label="Store">
      <fcb-select-v-3
        size="large"
        style="width: 100%"
        allowClear
        placeholder="Select store"
        :showSearch="true"
        :getPopupContainer="(trigger) => trigger.parentNode"
        dispatchType="part_stores/loadAndAddStore"
        queryDispatchType="part_stores/loadPartStores"
        :payload="{
          data_sort: 'name',
          order_sort: 'asc',
        }"
        :vDecorator="['part_store_id', { initialValue: dataSource.part_store ? dataSource.part_store.id : undefined }]"
        type="STORES"
      >
        <a-select-option v-for="option in part_stores" :key="option.id">
          {{ option.name }}
        </a-select-option>
      </fcb-select-v-3>
    </a-form-item>

    <fc-link-to-assets
      tooltip_text="Link this part to asset(s) to be able to view quantities of parts associated with the asset(s)."
      :isVisible="isVisibleAsset"
    />

    <fc-link-to-vendors
      tooltip_text="Link this part to vendor(s) to be able to track parts associated with specific vendors."
    />

    <a-form-item label="Part Labels (Optional)">
      <fc-label-link
        :labelType="LABEL_TYPES.part"
        tooltip="Use Parts Labels to categorize Parts for filtering, reporting and permissioning."
      />
    </a-form-item>

    <fc-upload-files
      style="margin-top: 10px;"
      fieldName="images"
      :multiple="false"
      :fileLength="1"
      :fileSize="10"
      :fileListProp="imgArr"
      :block="{ compulsory: false, title: 'Image' }"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import FcLinkToAssetsModal from '~/components/Shared/FcLinkedItems/FcLinkToAssetsModal.vue';
import FcLinkToVendors from '~/components/Shared/FcLinkedItems/FcLinkToVendors.vue';
import FcbSelectV3 from '../../../Shared/FcbSelectV3.vue';
import FcLabelLink from '../../../Shared/Label/FcLabelLink.vue';
import { LABEL_TYPES } from '../../../../utils/consts';

export default {
  props: {
    dataSource: {
      type: Object,
      default: () => ({}),
    },
    isAdd: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    FcLinkToAssetsModal,
    FcLinkToVendors,
    FcbSelectV3,
    FcLabelLink,
  },
  data() {
    return {
      isVisibleAsset: false,
      LABEL_TYPES,
    };
  },
  computed: {
    ...mapState({
      selectedAssets: (state) => state.linked_items.selectedAssets,
      selectedVendors: (state) => state.linked_items.selectedVendors,
      part: (state) => state.parts.part,
      part_stores: (state) => state.part_stores.part_stores,
    }),
    defaultQuantity() {
      return this.dataSource?.quantity;
    },
    imgArr() {
      return !this.isAdd && Object.keys(this.part).length > 0 && this.part.attachments.length > 0
        ? [
            {
              uid: '-1',
              name: '',
              status: 'done',
              url: this.part.attachments[0].url,
            },
          ]
        : [];
    },
  },
  mounted() {
    let localSelectedAssets = [];
    let localSelectedVendors = [];

    if (this.dataSource && this.dataSource.assets) {
      localSelectedAssets = this.dataSource.assets;
    }
    if (this.dataSource && this.dataSource.vendors) {
      localSelectedVendors = this.dataSource.vendors;
    }

    this.$store.dispatch('linked_items/setSelectedAssets', localSelectedAssets);
    this.$store.dispatch('linked_items/setSelectedVendors', localSelectedVendors);
  },
};
</script>

<style lang="less">
.fcb-part-form-item-select-store {
  .ant-form-item-label {
    width: 100%;
  }
  .fcb-select-store-label {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .ant-form-item-no-colon {
    &::after {
      content: none !important;
    }
  }
}
</style>
