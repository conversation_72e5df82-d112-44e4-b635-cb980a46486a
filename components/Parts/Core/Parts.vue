<template>
  <div class="features-parts-container">
    <div class="fc-group-on-top" ref="fcGroupOnTop">
      <div class="fc-group__left">
        <fc-select-entries v-on:onShowSizeChange="handleSelectEntriesChange" :currentSize="partsFilter.per_page" />
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" :initSearchText="initSearchText" />
        <fc-clear-filter :filteredKey="PARTS" @onClearFilter="onClearFilter"></fc-clear-filter>
      </div>
      <div class="fc-group__right">
        <top-right-actions />
      </div>
    </div>
    <fc-page-tip
      content="Track the inventory of spare parts for asset maintenance"
      referenceLink="https://blog.facilitybot.co/blog/knowledge-base/manager-web-portal/parts/"
      ref="fcPageTip"
    />
    <div>
      <fc-table-v2
        :columns="columns"
        :dataSource="dataSource"
        v-on:onTableChange="onTableChange"
        v-on:onSelectedRowChange="onSelectedRowChange"
        :loading="loading"
        :pagination="pageInformation"
        ref="tableParts"
        :tableHeight="currentDynamicHeight"
        :rememberFilter="partsFilter"
      >
        <template slot="part_label" slot-scope="text, record">
          <div>
            {{ text }}
            <a-popover @mouseenter="handleLoadQrCode(record.id)" placement="right">
              <template slot="title">
                <div class="qr-code-title-container">
                  <a-tooltip>
                    <span slot="title">
                      Use the FacilityBot Mobile App to scan the Parts QR Code to update the quantity for this part
                    </span>
                    QR Code
                    <tip-icon class="tip" />
                  </a-tooltip>
                  <a :href="qrCodeImage" :download="imageName(text)">
                    <a-icon type="download"></a-icon>
                    Download
                  </a>
                </div>
              </template>
              <template slot="content">
                <img :src="qrCodeImage" width="200" />
                <a-skeleton v-if="loadingQRCode" active :title="false" rows="4" />
              </template>
              <a><a-icon type="qrcode"/></a>
            </a-popover>
            <div>
              <preview-image v-if="record" :multiple="false" :fileLength="1" :fileListProp="getImgArr(record.id)" />
            </div>
          </div>
          <fc-label-tags v-if="hasLabels(record)" :labels="record.labels" />
        </template>
        <template slot="part_label_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <fc-filter-labels-v2
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
            }"
            :labelType="LABEL_TYPES.part"
            ref="fcFilterLabels"
          />
        </template>
        <div slot="linked_item" slot-scope="text, record">
          <div class="fc-linked-text" v-if="record.assets.length">
            <span style="margin:0px">Linked Asset ID(s)</span>
            <span>
              <span v-for="(asset, index) in record.assets" :key="index">
                <fc-link-asset-detail :asset_id="asset.asset_id" />
                <span v-if="index !== record.assets.length - 1">, </span>
              </span>
            </span>
          </div>
          <div class="fc-linked-text" v-if="record.vendors && record.vendors.length">
            <span>Linked Vendor(s)</span>
            <span>
              <span v-for="(vendor, index) in record.vendors" :key="index">
                <fc-link-vendor-detail :vendor_id="vendor.id" :vendor_name="vendor.company_name" />
                <span v-if="index !== record.vendors.length - 1">, </span>
              </span>
            </span>
          </div>
        </div>
        <template
          slot="part_store_filter"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <fc-filter-part-store
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
              column,
            }"
            :searchable="true"
            :multipleChoice="true"
          />
        </template>

        <span slot="format_time" slot-scope="text">
          <decorator-time :time="text" />
        </span>
        <span slot="store" slot-scope="text, record">
          {{ record.part_store ? record.part_store.name : '' }}
        </span>
        <template slot="action" slot-scope="text, record">
          <table-actions :data="record" :actionVisible="record.id === chosenItem.id" />
        </template>
      </fc-table-v2>
    </div>
    <drawer-meter :data="detailData" />
  </div>
</template>

<script>
import FcTableV2 from '../../Shared/FcTableV2.js';
import FcAttachmentLink from '../../Shared/FcAttachmentLink.vue';
import FcSearch from '../../Shared/FcSearch.vue';
import FcSelectEntries from '../../Shared/FcSelectEntries.vue';
import FcStatusTag from '../../Shared/FcStatusTag.vue';
import FcCustomizeColumnsTable from '../../Shared/FcCustomizeColumnsTable.vue';
import FcPageTip from '../../Shared/FcPageTip.vue';
import DecoratorTime from '../../Shared/FcDecoratorTime.vue';
import TableActions from '../MainActions/TableActions.vue';
import { AccountIcon } from '../../Icons/index.js';
import TopRightActions from '../TopActions/TopRightActions.vue';
import DrawerMeter from '../MainActions/DrawerActions/DrawerPart.vue';
import PreviewImage from '../MainActions/Actions/PreviewImage.vue';
import FcFilterPartStore from '../../Shared/FcTableColumnFilters/FcFilterPartStore.vue';
import FcFilterLabelsV2 from '../../Shared/Label/FcFilterLabelsV2.vue';
import FcLabelTags from '../../Shared/Label/FcLabelTags.vue';
import FcLinkAssetDetail from '../../Shared/FcLinkAssetDetail.vue';
import FcLinkVendorDetail from '../../Shared/FcLinkVendorDetail.vue';

import _ from 'lodash';
import { mapState } from 'vuex';
import { convertToFilteredColumn } from '../../../utils/helper';
import { FILTER_REMEMBERED_PROPERTIES, LABEL_TYPES } from '@/utils/consts';
const { PARTS } = FILTER_REMEMBERED_PROPERTIES;

export default {
  props: {
    dataSource: {
      type: Array,
      default: [],
    },
    pageInformation: {
      type: Object,
    },
    partStores: {
      type: Array,
      default: [],
    },
  },
  components: {
    FcTableV2,
    TableActions,
    FcSearch,
    FcStatusTag,
    FcAttachmentLink,
    DecoratorTime,
    FcSelectEntries,
    FcCustomizeColumnsTable,
    AccountIcon,
    TopRightActions,
    DrawerMeter,
    FcPageTip,
    PreviewImage,
    FcFilterPartStore,
    FcFilterLabelsV2,
    FcLabelTags,
    FcLinkAssetDetail,
    FcLinkVendorDetail,
  },
  data() {
    return {
      columns: [
        {
          title: 'Part Name',
          dataIndex: 'name',
          sorter: true,
          width: 220,
          sorterKey: 'name',
          scopedSlots: {
            customRender: 'part_label',
            filterDropdown: 'part_label_filter',
          },
          filterKey: 'label_ids',
        },
        {
          title: 'Linked To',
          dataIndex: '',
          scopedSlots: { customRender: 'linked_item' },
          width: 150,
        },
        {
          title: 'Store',
          dataIndex: 'part_store_ids',
          scopedSlots: { filterDropdown: 'part_store_filter', customRender: 'store' },
          sorter: true,
          width: 110,
          filterKey: 'part_store_ids',
          sorterKey: 'part_store_name',
        },
        {
          title: 'Balance Quantity',
          dataIndex: 'quantity',
          sorter: true,
          width: 120,
        },
        {
          title: 'Last Updated By',
          dataIndex: 'updated_by_manager_name',
          sorter: true,
          width: 110,
        },
        {
          title: 'Last Updated At',
          dataIndex: 'updated_at',
          sorter: true,
          scopedSlots: { customRender: 'format_time' },
          width: 180,
        },
        {
          title: '',
          dataIndex: '',
          scopedSlots: { customRender: 'action' },
          className: 'fc-action-row',
          width: 0,
        },
      ],
      loading: false,
      chosenItem: -1,
      perPage: 10,
      dynamicColumns: [],
      detailData: {},
      search: undefined,
      dynamicHeight: '0',
      qrCodeImage: null,
      loadingQRCode: false,
      clearFilterVisible: false,
      page: 1,
      PARTS,
      LABEL_TYPES,
    };
  },
  mounted() {
    this.calDynamicHeight();
  },
  methods: {
    hasLabels(record) {
      return !(_.isNil(record.labels) || _.isEmpty(record.labels));
    },
    handleSelectEntriesChange(perPage) {
      this.perPage = perPage;
      this.fetch(
        {
          page: 1,
          per_page: perPage,
          q: this.search,
        },
        { isShowClearFilter: null },
      );
    },
    handleInputSearchSubmit(value) {
      this.search = value;
      this.fetch({
        page: 1,
        per_page: this.perPage,
        q: value,
      });
    },
    fetch(payload = {}, { isShowClearFilter = true } = {}) {
      this.loading = true;

      // Determine the sort key
      const data_sort =
        payload.data_sort === 'part_store_ids' ? 'part_store_name' : payload.data_sort || this.partsFilter.data_sort;

      // Merge payload with current filter settings
      let filteredPayload = { ...this.partsFilter, ...payload, data_sort };

      // Handle isShowClearFilter logic
      if (_.isNull(isShowClearFilter)) {
        isShowClearFilter = this.partsFilter.isShowClearFilter;
      } else if (!isShowClearFilter) {
        // If isShowClearFilter is false, use only the payload without merging with the current filter
        filteredPayload = payload;
      }
      // Dispatch the loadAll action with the filteredPayload
      this.$store
        .dispatch('parts/loadAll', { ...filteredPayload })
        .then(() => {
          // When promise resolves, set loading to false
          this.loading = false;
        })
        .catch((error) => {
          // If there's an error, display error message and set loading to false
          this.$message.error(error.response.message);
          this.loading = false;
        });
      // Store the filteredPayload along with isShowClearFilter
      this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
        property: PARTS,
        value: { ...filteredPayload, isShowClearFilter },
      });
    },
    onTableChange(value) {
      let filterPayload = this.decoratorFilter(value.filters);
      if (!_.isEmpty(filterPayload)) {
        this.columns = convertToFilteredColumn(this.columns, value.filters);
      }
      this.page = value.pagination.current;
      this.fetch({
        page: value.pagination.current,
        per_page: value.pagination.pageSize,
        data_sort: value?.sorter?.order ? value?.sorter?.field : undefined,
        order_sort: this.decoratorOrderSort(value?.sorter?.order),
        q: this.search,
        ...filterPayload,
      });
    },
    onClearFilter(payload) {
      this.search = '';
      this.fetch({ ...payload }, { isShowClearFilter: false });
    },
    onSelectedRowChange(value) {
      this.chosenItem = value;
      if (value.id) this.detailData = value;
    },
    decoratorOrderSort(data) {
      if (!data) return undefined;

      return data === 'ascend' ? 'asc' : 'desc';
    },
    decoratorFilter(data) {
      if (!data) return undefined;
      let filterPayload = {
        label_ids: undefined,
        part_store_ids: undefined,
      };

      if (!_.isNil(data.part_store_ids) && !_.isEmpty(data.part_store_ids)) {
        filterPayload.part_store_ids = JSON.stringify(data.part_store_ids.map((item) => Number(item)));
      }
      if (!_.isNil(data.name) && !_.isEmpty(data.name)) {
        filterPayload.label_ids = JSON.stringify(data.name);
      }
      return filterPayload;
    },
    renameObject(oldProp, newProp, { [oldProp]: old, ...others }) {
      return {
        [newProp]: old,
        ...others,
      };
    },
    decoratorCostCentre() {
      return this.costCentres.map(({ id, name }) => ({
        text: name,
        value: id.toString(),
      }));
    },
    decoratorAllStatus() {
      return this.allStatus.map(({ id, status }) => ({
        text: status,
        value: status,
      }));
    },
    handleLoadQrCode(id) {
      this.loadingQRCode = true;
      this.$store.dispatch('parts/getQrCodeImage', id).then((res) => {
        this.qrCodeImage = res.qr_code;
        this.loadingQRCode = false;
      });
    },
    getImgArr(record_id) {
      let record = this.dataSource.find((item) => item.id == record_id);
      let ret =
        Object.keys(record).length > 0 && record.attachments.length > 0
          ? [
              {
                uid: record.attachments[0].id,
                name: record.attachments[0].title,
                status: 'done',
                url: record.attachments[0].url,
              },
            ]
          : [];
      return ret;
    },
    calDynamicHeight() {
      this.$store.dispatch('dynamic_height/setIsCalculateDynamicHeight', true);
      this.$store.dispatch('dynamic_height/setCurrentSettings', {
        pageContainer: 'features-parts-container',
        isExistTabBar: false,
        isExistGroupOnTop: true,
        isExistPageTip: true,
      });
    },
    imageName(text) {
      return `${text}.png`;
    },
  },
  computed: {
    ...mapState({
      currentDynamicHeight: (state) => state.dynamic_height.currentDynamicHeight,
      partsFilter: (state) => state.filter_remembered[PARTS],
    }),
    initSearchText() {
      return this.$route.query.q || this.search;
    },
  },
};
</script>

<style lang="less">
.expenditure-form {
  .mb-16 {
    margin-bottom: 16px;
  }

  .fc-checklist-container {
    padding: 0 24px;
  }
}
.qr-code-title-container {
  display: flex;
  justify-content: space-between;
}
</style>
