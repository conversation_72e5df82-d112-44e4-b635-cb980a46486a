<template>
  <div class="workflow-select" @click="onDropdown">
    <a-popover placement="bottomLeft" v-model="visible" trigger="click" overlayClassName="popover-workflow">
      <div class="content" slot="content">
        <div class="searchbox" v-if="searchable">
          <a-input placeholder="Search" class="borderless" @change="onSearch" :allowClear="true">
            <a-icon slot="prefix" type="search" />
          </a-input>
        </div>

        <div class="search-result-container">
          <template v-if="isLoading">
            <div class="loading-container">
              <InlineLoading v-if="isLoading" />
            </div>
          </template>

          <template v-if="!isLoading">
            <nuxt-link
              v-if="remoteOptionsURL && managePath"
              class="blue link-manage"
              :to="managePath"
              target="_blank"
              rel="nofollow noopener"
            >
              <a-icon type="setting" />
              {{ manageText }}
            </nuxt-link>
            <p
              v-for="(option, index) in options"
              :key="`${index}-${option.value}`"
              :class="optionClass(option)"
              @click="onSelect(option, $event)"
            >
              {{ option.text }}
            </p>
            <infinite-loading @infinite="infiniteHandler" ref="infiniteLoading">
              <div slot="no-more"></div>
              <div slot="no-results"></div>
              <div class="fc-spin-container" slot="spinner"><a-spin /></div>
            </infinite-loading>
          </template>
        </div>
      </div>

      <span class="workflow-select-display" v-if="displaySelection">
        {{ displaySelection }}
      </span>
      <span class="workflow-select-placeholder" v-else>{{ placeholder }}</span>
    </a-popover>
  </div>
</template>

<script>
import _ from 'lodash';
import { InlineLoading } from './InlineLoading.vue';
import { getData } from '@/utils/store-utils';

export default {
  components: {
    InlineLoading,
  },
  props: {
    description: {
      type: String,
      default: 'N/A',
    },
    remote: {
      type: Boolean,
      default: false,
    },
    remoteOptionsURL: {
      type: String,
      required: false,
    },
    remoteParams: {
      type: Object,
      default: () => {},
    },
    searchable: {
      type: Boolean,
      default: false,
    },
    multipleChoice: {
      type: Boolean,
      default: false,
    },
    preselectedOptions: [],
    preOptions: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      title: '',
      visible: false,
      options: [],
      search: '',
      selectedOptions: [],
      searchedOptions: [],
      isLoading: false,
      page: 2,
    };
  },
  computed: {
    displaySelection() {
      // Return a label for either: the description of the select, or the selected values
      if (!this.selectedOptions || this.selectedOptions.length == 0) {
        return null;
      }

      if (typeof this.selectedOptions == 'string') {
        return this.options.find((item) => item.value == this.selectedOptions).text;
      }

      return this.selectedOptions.map((item) => item.text).join(', ');
    },
    placeholder() {
      return this.description;
    },
    manageText() {
      const type = this.remoteOptionsURL.substring(5, this.remoteOptionsURL.length);
      switch (type) {
        case 'facilities':
          return 'Manage Fault Types';
        case 'request_types':
          return 'Manage Request Types';
        case 'schedule_of_rates':
          return 'Manage Schedule of Rates';
        case 'fault_locations':
          return 'Manage Location Tags';
        case 'facility_settings':
          return 'Manage Facilities';
        case 'surveys':
          return 'Manage Surveys';
        default:
          return null;
      }
    },
    managePath() {
      const type = this.remoteOptionsURL.substring(5, this.remoteOptionsURL.length);
      switch (type) {
        case 'facilities':
          return '/settings/request_settings?tab=fault_types';
        case 'request_types':
          return '/settings/request_settings';
        case 'schedule_of_rates':
          return '/features/schedule_of_rates';
        case 'fault_locations':
          return '/settings/location_tag_settings';
        case 'facility_settings':
          return '/settings/facilities_settings';
        case 'surveys':
          return '/features/surveys';
        default:
          return null;
      }
    },
  },
  created() {
    this.options = this.preOptions;
    // PreselectedOptions are passed from parent.
    // We need to store it as a state to allow editing
    this.selectedOptions = this.preselectedOptions;
  },
  methods: {
    infiniteHandler($state) {
      if (this.remote && this.remoteOptionsURL) {
        this.getSuggestions(this.page).then((response) => {
          this.options.push(...this.buildOptionsFromRemote(response));

          if (response.data.length) {
            this.page += 1;
            $state.loaded();
          } else {
            $state.complete();
          }
        });
      } else {
        $state.complete();
      }
    },
    async onDropdown() {
      // There are 2 cases: remote options and preloaded options.

      // If remote options, load it.
      if (this.remote && this.remoteOptionsURL) {
        this.isLoading = true;
        this.visible = true;
        this.page = 2;
        let response = await this.getSuggestions();
        this.options = this.buildOptionsFromRemote(response);
        this.isLoading = false;
        return;
      }

      // Else if preloaded, show it
      this.visible = true;
    },

    onSelect(option, event) {
      // Singular choice
      if (!this.multipleChoice) {
        this.selectedOptions = [option];
        this.$emit('change', this.selectedOptions);
        this.visible = false;
        return;
      }

      // For multiple choice, we need to handle the add / remove selection.
      let existingOption = this.selectedOptions.find((item) => item.value == option.value);

      if (existingOption) {
        this.selectedOptions = this.selectedOptions.filter((item) => item.value != option.value);
      } else {
        this.selectedOptions = [...this.selectedOptions, option];
      }

      // Handle for Fault Sub-Types selection
      const containAllSubTypesOption = this.selectedOptions.some((e) => e.text === 'All Sub-Types' && e.value === 0);
      const selectAllSubTypesOption = option.text === 'All Sub-Types' && option.value === 0;

      if (containAllSubTypesOption || selectAllSubTypesOption) {
        this.selectedOptions = [option];
      }

      // This is dirty, but if ... else is dirtier
      this.$emit('change', this.selectedOptions);
    },

    optionClass(option) {
      return 'option' + (this.isSelected(option) ? ' option--selected' : '');
    },

    isSelected(option) {
      // If the selectedOptions is just a string
      if (typeof this.selectedOptions == 'string') {
        return this.selectedOptions == option.value;
      }

      // Else if selectedOptions is an array
      let temp = this.selectedOptions.findIndex((item) => {
        // If it's an object
        if (item instanceof Object) {
          return item.value == option.value;
        } else {
          // Else if it's a string
          return item == option.value;
        }
      });

      return temp > -1;
    },

    buildOptionsFromRemote(response) {
      let data = response.data;
      if (!data) {
        return [];
      }

      return data.map((item) => ({
        value: item.id,
        text: item.name,
      }));
    },

    async performSearch(event) {
      this.page = 2;
      this.search = event.target.value;
      let response = await this.getSuggestions();
      this.options = this.buildOptionsFromRemote(response);
    },

    // NOTE: This must use normal "function"
    onSearch: _.debounce(async function(event) {
      await this.performSearch(event);
    }, 1000),

    async getSuggestions(page = 1) {
      const baseParams = {
        ...this.remoteParams,
        q: this.search,
        page: page,
      };

      switch (this.remoteOptionsURL) {
        case '/api/fault_locations':
        case '/api/facilities/workflow':
        case '/api/fault_sub_type_tags/workflow':
        case '/api/request_types':
        case '/api/parts':
        case '/api/meters':
        case '/api/facilities':
        case '/api/facility_settings':
        case '/api/guests/workflow_accounts_suggestion':
        case '/api/zone_settings':
        case '/api/schedule_of_rates':
        case '/api/managers/list_assignment_requests':
        case '/api/cost_centres':
        case '/api/trigger_parameter_configs/all_trigger_parameter_configs':
          baseParams.data_sort = 'name';
          baseParams.order_sort = 'asc';
          break;
        case '/api/mqtt_settings':
          baseParams.data_sort = 'topic_name';
          baseParams.order_sort = 'asc';
          break;
        default:
          break;
      }

      return await getData(this.remoteOptionsURL, this.$axios, baseParams);
    },
  },
};
</script>

<style lang="less" scoped>
.workflow-select {
  display: inline;

  &-display,
  &-placeholder {
    cursor: pointer;
    color: #f66338;
    padding: 0.4rem 0;
    line-height: 2.4rem;
    border-bottom: 1px solid #dbdde0;
    min-width: 10rem;

    &::after {
      margin-left: 0.3rem;
      display: inline-block;
      border-left: solid 5px transparent;
      border-right: solid 5px transparent;
      border-top: solid 8px #f66338;
      content: '';
    }
  }

  &-placeholder {
    color: #999999;
  }
}

.content {
  min-width: 15rem;

  .search-result-container {
    max-height: 20rem;
    overflow-y: auto;
  }
}

.option {
  cursor: pointer;
  margin: 0;
  padding: 0.5rem 1.2rem;

  &:hover,
  &--selected {
    color: #f66338;
    background: #fff4f2;
  }
}

.searchbox {
  padding: 0.5rem;
  position: sticky;
  top: 0px;
  z-index: 10;
  background: white;
}

.ant-input {
  border: 1px solid #000 !important;
  box-shadow: none !important;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
}

.link-manage {
  padding: 0.5rem 1.2rem;
  i {
    margin-right: 10px;
  }
}
</style>
