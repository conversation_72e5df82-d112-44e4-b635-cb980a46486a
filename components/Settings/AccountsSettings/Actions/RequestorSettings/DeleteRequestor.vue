<template>
  <a-button
    :type="buttonType"
    icon="delete"
    v-tooltip="{ content: 'Delete Requestor' }"
    v-if="actionVisible"
    @click="handleClick"
  />
</template>

<script>
import { mapState } from 'vuex';

export default {
  props: {
    data: Object,
    actionVisible: Boolean,
    buttonType: {
      type: String,
      default: 'primary',
    },
  },
  methods: {
    handleClick() {
      let _this = this;
      this.$confirm({
        title: 'Do you want to delete this requestor?',
        content: `Requestor name: ${_this.data.full_name || _this.data.first_name + ' ' + _this.data.last_name}`,
        onOk() {
          _this.$store
            .dispatch('accounts_settings/deleteRequestor', _this.data.id)
            .then(() => {
              _this.$store.dispatch('accounts_settings/loadRequestors', {
                page: _this.pageInformation.page,
                per_page: _this.pageInformation.per_page,
                data_sort: _this.requestorFilterRemember?.data_sort,
                order_sort: _this.requestorFilterRemember?.order_sort,
                requestor_tag_ids: _this.requestorFilterRemember?.requestor_tag_ids,
                source_ids: _this.requestorFilterRemember?.source_ids,
                q: _this.requestorFilterRemember?.q,
              });
              _this.$message.success('Requestor deleted successfully.');
            })
            .catch((error) => {
              _this.$message.error(error.response.data.message);
            });
        },
      });
    },
  },
  computed: {
    ...mapState({
      pageInformation: (state) => state.accounts_settings.requestor_page_information,
      requestorFilterRemember: (state) => state.filter_remembered?.settings?.accounts?.requestors,
    }),
  },
};
</script>

<style lang="less" scoped></style>
