<template>
  <a-modal
    title="Link to Customers"
    :visible="isVisible"
    @destroyOnClose="true"
    @cancel="handleCancel"
    @ok="handleOk"
    :confirm-loading="confirmLoading"
    class="modal-asset-checklist-setting"
    :centered="true"
  >
    <span v-if="selectedCustomerIds"
      >Selected Customers: <strong>{{ selectCustomerNames }}</strong></span
    >

    <div class="fc-group-on-top">
      <div class="fc-group__left">
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" style="margin-left: -32px;" />
      </div>
      <div class="fc-group__right">
        <fc-link-btn :pathLabel="PATH_LABEL.CUSTOMERS" />
      </div>
    </div>

    <fc-table
      :columns="columns"
      :row-selection="rowSelection"
      :dataSource="customers"
      v-on:onTableChange="onTableChange"
      :loading="loading"
      :pagination="pageInformation"
    >
      <template slot="customer_addresses" slot-scope="text, record, index" v-if="text.length">
        <div v-for="(add, idx) in text" :key="add.id">
          <span style="margin-right: 8px;">
            <a-checkbox
              :value="add.id"
              :disabled="disableRow(record.id)"
              :checked="isAddressSelected(record.id, add.id)"
              @change="handleSelectAddress(record.id, index, add.id)"
            ></a-checkbox>
          </span>
          <span>{{ add.address }}</span>
          <span v-if="add.postal_code"> - {{ add.postal_code }}</span>
          <a-divider v-if="text.length > 1 && text.length - 1 !== idx"></a-divider>
        </div>
      </template>
    </fc-table>
  </a-modal>
</template>

<script>
import { mapState } from 'vuex';
import FcLinkBtn from '../../../Shared/FcLinkBtn.vue';
import { PATH_LABEL } from '../../../../utils/consts';

export default {
  components: { FcLinkBtn },
  data() {
    return {
      columns,
      loading: false,
      localSelectedCustomers: [],
      localSelectedCustomerIDs: [],
      search: '',
      perPage: 10,
      page: 1,
      dataSort: undefined,
      orderSort: undefined,
      localSelectedAddressIDs: [],
      PATH_LABEL,
    };
  },

  props: {
    isVisible: {
      type: Boolean,
      default: false,
    },
    isLinkingCustomer: {
      type: Boolean,
      default: false,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    fetch(payload = {}) {
      this.loading = true;
      this.$store.dispatch('billing_customers/loadAllBillingCustomers', { ...payload }).then(() => {
        this.loading = false;
      });
    },
    handleCancel() {
      this.localSelectedCustomers = [...this.selectedCustomers];
      this.localSelectedCustomerIDs = [...this.localSelectedCustomers.map((customer) => customer.id)];
      this.$emit('onCloseModal');
    },
    handleOk() {
      this.$store.dispatch('billing_customers/setSelectedCustomers', this.localSelectedCustomers);
      this.$store.dispatch('billing_customers/setSelectedCustomerParams', this.localSelectedAddressIDs);
      this.isLinkingCustomer && this.$emit('handleSubmitSelectedCustomers', this.localSelectedAddressIDs);
      this.$emit('onCloseModal');
    },

    handleInputSearchSubmit(value) {
      this.search = value;
      this.fetch({
        page: 1,
        per_page: this.perPage,
        data_sort: this.dataSort,
        order_sort: this.orderSort,
        q: value,
      });
    },

    onTableChange(value) {
      this.dataSort = value?.sorter?.field || undefined;
      this.orderSort = this.decoratorOrderSort(value?.sorter?.order);
      this.fetch({
        page: value.pagination.current,
        per_page: value.pagination.pageSize,
        data_sort: value?.sorter?.field || undefined,
        order_sort: this.decoratorOrderSort(value?.sorter?.order),
        q: this.search,
      });
    },

    decoratorOrderSort(data) {
      if (!data) return undefined;

      return data === 'ascend' ? 'asc' : 'desc';
    },

    handleSelectAddress(customerId, rowIndex, addressId) {
      const customerIndex = this.localSelectedAddressIDs.findIndex((entry) => entry.customer_id === customerId);

      if (customerIndex === -1) {
        this.localSelectedAddressIDs.push({
          customer_id: customerId,
          address_ids: [addressId],
        });
      } else {
        const addressIds = this.localSelectedAddressIDs[customerIndex].address_ids;
        const addressIndex = addressIds.indexOf(addressId);

        if (addressIndex === -1) {
          addressIds.push(addressId);
        } else {
          addressIds.splice(addressIndex, 1);
        }
      }
    },
  },

  computed: {
    ...mapState({
      customers: (state) => state.billing_customers.customers,
      selectedCustomers: (state) => state.billing_customers.selectedCustomers,
      pageInformation: (state) => state.billing_customers.pageInformation,
    }),

    rowSelection() {
      return {
        selectedRowKeys: this.localSelectedCustomerIDs,
        onChange: (selectedRowKeys) => {
          this.localSelectedCustomerIDs = selectedRowKeys;

          this.localSelectedCustomers = this.customers.filter((customer) => selectedRowKeys.includes(customer.id));

          this.localSelectedAddressIDs = this.localSelectedAddressIDs.filter((entry) =>
            this.localSelectedCustomerIDs.includes(entry.customer_id),
          );

          this.localSelectedCustomerIDs.forEach((customerId) => {
            const existingEntry = this.localSelectedAddressIDs.find((entry) => entry.customer_id === customerId);

            if (!existingEntry) {
              const customer = this.customers.find((customer) => customer.id === customerId);
              this.localSelectedAddressIDs.push({
                customer_id: customerId,
                address_ids: customer.customer_addresses.map((address) => address.id),
              });
            }
          });
        },
      };
    },

    selectedCustomerIds() {
      return this.localSelectedCustomers ? [...this.localSelectedCustomers.map((x) => x.id)].join(', ') : '';
    },

    selectCustomerNames() {
      return this.localSelectedCustomers ? [...this.localSelectedCustomers.map((x) => x.name)].join(', ') : '';
    },

    disableRow() {
      return (customerId) => !this.localSelectedCustomerIDs.includes(customerId);
    },

    isAddressSelected() {
      return (customerId, addressId) => {
        const entry = this.localSelectedAddressIDs.find((entry) => entry.customer_id === customerId);
        return entry && entry.address_ids && entry.address_ids.includes(addressId);
      };
    },

    shouldFetchData() {
      return this.isVisible;
    },
  },

  watch: {
    shouldFetchData(value) {
      if (value) {
        this.fetch();

        this.localSelectedCustomers = [...this.selectedCustomers];

        this.localSelectedCustomerIDs = [...this.selectedCustomers.map((customer) => customer.id)];

        this.localSelectedAddressIDs = this.localSelectedCustomers.map((customer) => ({
          customer_id: customer.id,
          address_ids: customer.addresses?.map((address) => address.id),
        }));
      }
    },
  },
};

const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: 'Email Address',
    dataIndex: 'email',
    sorter: true,
  },

  {
    title: 'Contact Number',
    dataIndex: 'contact_number',
    sorter: false,
  },
  {
    title: 'Address - Postal Code',
    dataIndex: 'customer_addresses',
    scopedSlots: { customRender: 'customer_addresses' },
    sorter: false,
  },
];
</script>

<style lang="less" scoped>
.ant-divider-horizontal {
  margin: 8px 0;
}
</style>
