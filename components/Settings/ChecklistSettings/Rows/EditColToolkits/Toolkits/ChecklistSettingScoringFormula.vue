<template>
  <div class="fc-checklist-setting-card">
    <a-form-item :validate-status="validateStatus" :help="help">
      <div class="fc-flex-center fc-checklist-setting-card-title">
        <div class="fc-flex-center">
          <checklist-setting-formula style="margin-right: 6px" />
          <checklist-setting-edit-title
            :defaultValue="data.title"
            placeholder="Scoring Formula"
            :index="index"
            :rowIndex="rowIndex"
          />
        </div>
        <div class="fc-flex-center">
          <edit-scoring-formula-drawer :blockData="data" :index="index" :rowIndex="rowIndex" />
          <checklist-setting-duplicate :index="index" :rowIndex="rowIndex" />
          <checklist-setting-remove :index="index" :rowIndex="rowIndex" />
        </div>
      </div>
    </a-form-item>
  </div>
</template>

<script>
import { ChecklistSettingFormula } from '@/components/Icons/index';
import ChecklistSettingEditRequired from './ChecklistSettingEditRequired.vue';
import ChecklistSettingEditTitle from './ChecklistSettingEditTitle.vue';
import ChecklistSettingRemove from './ChecklistSettingRemove.vue';
import ChecklistSettingDuplicate from './ChecklistSettingDuplicate.vue';
import EditScoringFormulaDrawer from '@/components/Settings/ChecklistSettings/Rows/RowsAllowScoringAction/EditScoringFormulaDrawer.vue';

import { mapState } from 'vuex';
import _ from 'lodash';
export default {
  components: {
    ChecklistSettingEditTitle,
    ChecklistSettingEditRequired,
    ChecklistSettingRemove,
    ChecklistSettingDuplicate,
    EditScoringFormulaDrawer,
    ChecklistSettingFormula,
  },
  props: {
    data: Object,
    index: Number,
    rowIndex: Number,
  },
  computed: {
    ...mapState({
      errors: (state) => state.checklist_setting_rows.errors,
      validateRenderKey: (state) => state.checklist_setting_rows.validateRenderKey,
    }),
  },
  data() {
    return {
      validateStatus: '',
      help: '',
    };
  },
  methods: {
    getValidateStatus() {
      let keys = _.keys(this.errors);
      let matchError = _.find(
        keys,
        (key) =>
          key.includes(`scoring_formula_numerator_${this.index}_${this.rowIndex}`) ||
          key.includes(`scoring_formula_denominator_${this.index}_${this.rowIndex}`),
      );

      let errorMessage = _.get(this.errors, `[${matchError}].errors[0].message`);

      if (_.isNil(errorMessage)) {
        this.validateStatus = '';
        this.help = '';
        return;
      }

      this.validateStatus = 'error';
      this.help = errorMessage;
    },
  },
  watch: {
    validateRenderKey(newVal, oldVal) {
      this.$nextTick(() => {
        this.getValidateStatus();
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
