<template>
  <div class="fc-checklist-custom-form">
    <h1 class="fc-checklist-main-form--header">
      Recurrence
      <a-tooltip
        title="You can configure this parent checklist to automatically generate child (i.e. pending) checklists at recurring intervals. If you do not wish to set any recurrence, just leave this to end after 0 occurrences (i.e no recurrence)"
      >
        <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
      </a-tooltip>
    </h1>
    <a-row>
      <div class="ant-form-item-label">
        <label class="ant-form-item-no-colon"
          >Start date and time
          <a-tooltip title="This is the date / time that the first child (i.e. pending) checklist will be generated.">
            <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
          </a-tooltip>
        </label>
      </div>
      <a-col>
        <a-date-picker
          ref="startDateTime"
          size="large"
          style="width: 100%"
          format="DD/MM/YYYY HH:mm"
          :disabled-date="disabledStartDate"
          :show-time="{ defaultValue: $moment('00:00', 'HH:mm'), format: 'HH:mm' }"
          @change="handleStartDateChange"
          v-model="startTimeMomentFormat"
        />
      </a-col>
    </a-row>
    <a-tabs :default-active-key="timeTabActiveKey" @change="handleDateTabChange">
      <a-tab-pane :key="repeatTimeType">
        <div slot="tab">
          <a-tooltip
            title="After setting the Start Date and Time, you can set the frequency (day, week, month) for the next child (i.e. pending) checklists to be generated."
          >
            Repeat Every
            <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
          </a-tooltip>
        </div>
        <div>
          <div class="ant-form-item-label"></div>
          <a-row :gutter="[16, 16]">
            <a-col :span="6">
              <a-input-number
                size="large"
                :min="1"
                :max="100000"
                v-model="numberRepeat"
                @change="handleRepeatEveryChange"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="18">
              <a-select
                v-model="repeatTimeType"
                size="large"
                style="width: 100%"
                @change="handleRepeatTimeChange"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option value="day"> Day </a-select-option>
                <a-select-option value="week"> Week </a-select-option>
                <a-select-option value="month"> Month </a-select-option>
              </a-select>
            </a-col>
          </a-row>
          <a-row v-if="repeatTimeType === 'week'">
            <div class="ant-form-item-label">
              <label class="ant-form-item-no-colon">Repeat on</label>
            </div>
            <repeat-week-form :defaultValue="dayOfWeek" v-on:onSelectedChange="handleReapetWeekFormChange" />
          </a-row>
          <a-row v-else-if="repeatTimeType === 'month'">
            <div class="ant-form-item-label">
              <label class="ant-form-item-no-colon">Repeat on</label>
            </div>
            <repeat-month-form
              :dayString="startTime"
              :kind="getKindOfMonth()"
              :defaultMonth="defaultMonth"
              :isLastDayOfMonthValid="isLastDayOfMonthValid"
              @onRepeatMonthChange="handleRepeatMonthChange"
            />
          </a-row>
          <a-row>
            <div class="ant-form-item-label">
              <label class="ant-form-item-no-colon"
                >Ends
                <a-tooltip title="Set the ending condition for the recurrence.">
                  <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
                </a-tooltip>
              </label>
            </div>
            <a-col>
              <a-radio-group class="fc-radio-button-group" v-model="radioButtonData" @change="handleModeChange">
                <a-row>
                  <a-col span="24">
                    <a-radio value="never_stop" class="fc-radio-button">
                      Never
                    </a-radio>
                  </a-col>
                </a-row>
                <a-row :gutter="[16, 16]">
                  <a-col span="6">
                    <div class="fc-radio-button">
                      <a-radio value="end_on"> On </a-radio>
                    </div>
                  </a-col>
                  <a-col span="18">
                    <div class="fc-radio-button" @click="handleSetEndDateClick('end_on')">
                      <a-date-picker
                        ref="endOn"
                        size="large"
                        format="DD/MM/YYYY"
                        :allowClear="false"
                        style="width: 100%"
                        :disabled-date="disabledEndDate"
                        :default-value="$moment().add(1, 'year')"
                        v-model="endOn"
                        @change="handleEndDateChange"
                      />
                    </div>
                  </a-col>
                </a-row>
                <a-row :gutter="[16, 16]">
                  <a-col span="6">
                    <div class="fc-radio-button">
                      <a-radio value="number_limit"> After </a-radio>
                    </div>
                  </a-col>
                  <a-col span="18">
                    <div class="fc-radio-button" @click="handleSetEndDateClick('number_limit')">
                      <div class="fc-checklist-custom-input-number">
                        <a-input-number
                          size="large"
                          :min="0"
                          :max="100000"
                          v-model="numberLimit"
                          @change="handleChangeNumberLimit"
                        />
                        <span style="padding: 12px">occurrences</span>
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </a-radio-group>
            </a-col>
          </a-row>
          <a-row style="margin-top: 32px" v-if="schedules.length">
            <a-table :columns="columns" :data-source="schedules" bordered :pagination="false" rowKey="id"> </a-table>
          </a-row>
        </div>
      </a-tab-pane>
      <a-tab-pane tab="OR" disabled></a-tab-pane>
      <a-tab-pane key="custom_datetime">
        <div slot="tab">
          <a-tooltip
            title="If your maintenance checklists do not recur at fixed intervals, use this tab to set custom date / times for the next checklists to be generated."
          >
            Add next date and time
            <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
          </a-tooltip>
        </div>
        <div>
          <div class="ant-form-item-label"></div>
          <a-form
            layout="inline"
            :form="custom_schedule_form"
            @submit="handleAddCustomSchedule"
            class="fc-next-date-container"
          >
            <a-form-item>
              <a-date-picker
                format="DD/MM/YYYY hh:mm A"
                :disabled-date="disabledStartDate"
                size="large"
                :show-time="{ format: 'HH:mm' }"
                placeholder="Select date and time"
                v-decorator="[
                  'time',
                  {
                    rules: [
                      {
                        required: true,
                        message: 'Please select date and time',
                      },
                    ],
                  },
                ]"
              />
            </a-form-item>
            <a-form-item>
              <a-button html-type="submit" size="large">Add next date and time</a-button>
            </a-form-item>
          </a-form>
          <a-row style="margin-top: 32px">
            <a-table
              :columns="custom_schedule_columns"
              :data-source="customSchedules"
              bordered
              rowKey="id"
              :pagination="{ pageSize: 5 }"
            >
              <span slot="action" slot-scope="text, record">
                <a-button icon="delete" @click="handleDeleteCustomSchedule(record.id)" />
              </span>
            </a-table>
          </a-row>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { mapState } from 'vuex';

import RepeatMonthForm from './RepeatTimeForm/RepeatMonthForm.vue';
import RepeatWeekForm from './RepeatTimeForm/RepeatWeekForm.vue';
const columns = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'Upcoming checklist schedule: First 5',
    dataIndex: 'time',
    key: 'time',
  },
];

const custom_schedule_columns = [
  {
    title: 'Custom Schedule',
    dataIndex: 'time',
    key: 'time',
    sorter: (a, b) => a.time.localeCompare(b.time),
  },
  {
    title: 'Action',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
];

export default {
  components: { RepeatWeekForm, RepeatMonthForm },
  props: {
    customRecurrence: Object,
    isEditing: Boolean,
    checklistSetting: Object,
  },
  data() {
    return {
      repeatTimeType: 'day',
      startTime: moment().format('DD/MM/YYYY HH:mm'),
      startTimeMomentFormat: this.isEditing
        ? moment(this.customRecurrence['recurrence_start_datetime'], 'X')
        : moment(),
      endOn: this.isEditing
        ? moment(this.customRecurrence['recurrence_start_datetime'], 'X').add(1, 'year')
        : moment().add(1, 'year'),
      radioButtonData: 'number_limit',
      numberRepeat: 1,
      dayOfWeek: [new Date().getDay()],
      numberLimit: 0,
      columns,
      defaultMonth: 0,
      customSchedules: [],
      custom_schedule_columns,
      custom_schedule_form: this.$form.createForm(this),
    };
  },
  mounted() {
    if (!this.isEditing) {
      let recurrence = {
        recurrence_start_datetime: this.startTime,
        recurrence_kind: 'day',
        recurrence_number_repeat: 1,
        recurrence_day_of_week: [new Date().getDay()],
        recurrence_day_of_month: 0,
        recurrence_week_of_month: 0,
        recurrence_day_in_week: 0,
        recurrence_never_stop: false,
        recurrence_enabled_end_on: false,
        recurrence_end_on: this.endOn.format('DD/MM/YYYY'),
        recurrence_enabled_number_limit: true,
        recurrence_number_limit: 0,
      };
      this.$store.dispatch('checklist_settings/setCustomRecurrence', recurrence);
    } else {
      this.customSchedules = this.checklistSetting.recurrence.next_datetime.map((e, i) => {
        return { id: i, time: moment(e).format('DD/MM/YYYY hh:mm A') };
      });

      this.repeatTimeType = this.calRepeatTimeType(this.customRecurrence['recurrence_kind'], this.repeatTimeType);
      this.startTime = this.customRecurrence['recurrence_start_datetime'].format('DD/MM/YYYY HH:mm');
      this.endOn = this.customRecurrence['recurrence_end_on'];
      this.numberRepeat = this.customRecurrence['recurrence_number_repeat'];
      this.dayOfWeek = this.customRecurrence['recurrence_day_of_week'];
      this.getCurrentEndKind();
      this.numberLimit = this.customRecurrence['recurrence_number_limit'];
      this.defaultMonth = this.customRecurrence['recurrence_day_of_month'];
    }
  },
  methods: {
    calRepeatTimeType(type, current_type) {
      if (type == 'week_in_month') return 'month';
      if (type == 'custom_datetime') return current_type ? current_type : 'day';
      return type;
    },
    handleRepeatTimeChange(value) {
      this.repeatTimeType = value;

      if (this.repeatTimeType === 'month') {
        this.custom_recurrence['recurrence_day_of_month'] = this.startTimeMomentFormat.format('DD');

        if (this.isLastDayOfMonthValid) {
          this.custom_recurrence['recurrence_kind'] = 'month';
        } else {
          const weekOfMonth = this.getWeekOfMonth(this.startTimeMomentFormat);
          const dayInWeek = this.startTimeMomentFormat.day();

          this.custom_recurrence['recurrence_kind'] = 'week_in_month';
          this.custom_recurrence['recurrence_week_of_month'] = weekOfMonth;
          this.custom_recurrence['recurrence_day_in_week'] = dayInWeek;
        }
      } else {
        this.custom_recurrence['recurrence_kind'] = value;
      }

      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleAddCustomSchedule(e) {
      e.preventDefault();

      this.custom_schedule_form.validateFields((err, values) => {
        if (!err) {
          this.customSchedules.unshift({
            time: moment(values.time).format('DD/MM/YYYY hh:mm A'),
            id: this.customSchedules.length + 1,
          });

          this.custom_schedule_form.resetFields();
        }
      });
    },
    handleDeleteCustomSchedule(id) {
      this.customSchedules = this.customSchedules
        .filter((item) => item.id !== id)
        .map((item, index) => {
          item.id = index + 1;
          return item;
        });
    },
    handleDateTabChange(tab) {
      if (tab === 'custom_datetime') {
        this.custom_recurrence['recurrence_kind'] = 'custom_datetime';
      } else {
        this.custom_recurrence['recurrence_kind'] = tab;
      }

      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
    },
    disabledStartDate(current) {
      // Can not select days before today
      return (
        current &&
        current <
          moment()
            .endOf('day')
            .subtract(1, 'd')
      );
    },
    disabledEndDate(current) {
      // Can not select when endDate < startTimeMomentFormat
      return (
        current &&
        current <
          moment(this.startTimeMomentFormat, 'DD/MM/YYYY')
            .endOf('day')
            .subtract(1, 'd')
      );
    },
    handleStartDateChange(value) {
      this.startTime = value.format('DD/MM/YYYY HH:mm');

      this.custom_recurrence['recurrence_start_datetime'] = value.format('DD/MM/YYYY HH:mm');
      this.custom_recurrence['recurrence_day_of_month'] = value.date();

      let endOn = { ...value };
      endOn = moment(endOn)
        .add(1, 'year')
        .format('DD/MM/YYYY');
      this.custom_recurrence['recurrence_end_on'] = endOn;
      this.endOn = moment({ ...value }, 'DD/MM/YYYY').add(1, 'year');

      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
      this.defaultMonth = this.custom_recurrence['recurrence_day_of_month'];
    },
    handleRepeatEveryChange(value) {
      this.custom_recurrence['recurrence_number_repeat'] = value;
      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleSetEndDateClick(id) {
      this.radioButtonData = id;
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleModeChange(e) {
      let id = e.target.value;
      this.custom_recurrence['recurrence_never_stop'] = id === 'never_stop';
      this.custom_recurrence['recurrence_enabled_end_on'] = id === 'end_on';
      this.custom_recurrence['recurrence_enabled_number_limit'] = id === 'number_limit';

      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleReapetWeekFormChange(selectedDays) {
      this.custom_recurrence['recurrence_day_of_week'] = selectedDays.map((e) => e.id);
      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleRepeatMonthChange(data) {
      this.custom_recurrence['recurrence_kind'] = this.isLastDayOfMonthValid ? data.recurrence_kind : 'week_in_month';
      this.custom_recurrence['recurrence_day_of_month'] = data.recurrence_day_of_month;
      this.custom_recurrence['recurrence_week_of_month'] = data.recurrence_week_of_month;
      this.custom_recurrence['recurrence_day_in_week'] = data.recurrence_day_in_week;
      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleEndDateChange(value) {
      this.custom_recurrence['recurrence_end_on'] = value.format('DD/MM/YYYY');
      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    handleChangeNumberLimit(value) {
      this.custom_recurrence['recurrence_number_limit'] = value;
      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('checklist_settings/getChecklistSchedule');
    },
    getCurrentEndKind() {
      if (this.customRecurrence['recurrence_never_stop']) {
        this.radioButtonData = 'never_stop';
      } else if (this.customRecurrence['recurrence_enabled_end_on']) {
        this.radioButtonData = 'end_on';
      } else if (this.customRecurrence['recurrence_enabled_number_limit']) {
        this.radioButtonData = 'number_limit';
      }
    },
    getKindOfMonth() {
      if (this.isLastDayOfMonthValid) {
        return this.customRecurrence['recurrence_kind'] === 'week_in_month' ? 'week' : 'day';
      } else {
        return 'week';
      }
    },
    getWeekOfMonth(m) {
      return (
        m.week() -
        moment(m)
          .startOf('month')
          .week() +
        1
      );
    },
  },
  computed: {
    ...mapState({
      custom_recurrence: (state) => state.checklist_settings.custom_recurrence,
    }),
    schedules() {
      if (this.$store.state.checklist_settings.schedules.length > 0) {
        const a = this.$store.state.checklist_settings.schedules.map((x, index) => ({
          id: index + 1,
          time: x,
        }));
        return this.$store.state.checklist_settings.schedules.map((x, index) => ({
          id: index + 1,
          time: x,
        }));
      } else {
        return [];
      }
    },
    isLastDayOfMonthValid() {
      return moment(this.startTime, 'DD/MM/YYYY HH:mm').date() <= 28;
    },
    timeTabActiveKey() {
      if (this.isEditing) return this.custom_recurrence['recurrence_kind'];
      else return 'day';
    },
  },
  watch: {
    customSchedules(newValue, oldValue) {
      this.custom_recurrence['recurrence_next_datetime'] = JSON.stringify(newValue.map((e) => e.time));
      this.$store.dispatch('checklist_settings/setCustomRecurrence', this.custom_recurrence);
    },
  },
};
</script>

<style lang="less" scoped>
.fc-checklist-custom-form {
  .custom-form-label {
    font-weight: 500;
    font-size: 16px;
    color: #a0a4a8;
    display: block;
  }
  .fc-radio-button {
    display: flex;
    align-items: center;
    height: 55px;
  }
  .fc-radio-button-group {
    display: flex;
    flex-direction: column;
  }
  .fc-checklist-custom-input-number {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    &:hover {
      border: 1px solid #ff6337;
    }
    .ant-input-number {
      border: 0;
      border-right: 1px solid #d9d9d9;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      &:hover {
        border-right: 1px solid #ff6337;
      }
    }
  }
}
.fc-next-date-container {
  display: flex;
  gap: 1rem;
}
</style>
