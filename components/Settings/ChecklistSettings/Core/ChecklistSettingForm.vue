<template>
  <a-form :form="form" :loading="isLoading">
    <div>
      <a-row :gutter="[32, 32]" type="flex">
        <a-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <checklist-setting-main-form :form="form" :isEditing="isEditing" />
        </a-col>
        <a-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12" class="border-left h-100">
          <checklist-setting-custom-form
            :customRecurrence="customRecurrence"
            :checklistSetting="checklistSetting"
            :isEditing="isEditing"
          />
        </a-col>
      </a-row>
      <a-divider type="horizontal" style="margin-bottom: 0"></a-divider>
      <a-row v-if="generatorState === BY_PROMPT">
        <checklist-setting-generator />
      </a-row>
      <a-row v-else-if="generatorState === BY_PDF">
        <checklist-setting-import-by-pdf />
      </a-row>
      <a-row v-else>
        <checklist-setting-rows :rows="mappedRows" />
      </a-row>
    </div>
    <div class="fc-checklist-setting-group-bottom">
      <div class="float-left">
        <a-button class="btn-wide" type="default" size="large" @click="onCancel">Cancel</a-button>
        <a-button
          v-if="isShowBtnGenerator"
          class="btn-wide"
          type="default"
          size="large"
          @click="onBackToChecklistGenerator"
          >Back to Checklist Generator</a-button
        >
      </div>

      <div class="float-right">
        <template v-if="!isEditing">
          <a-button
            class="btn-wide"
            size="large"
            type="dashed"
            v-tooltip="{
              content:
                'For checklist templates saved as draft, pending checklists will not be created even if scheduled.',
            }"
            :loading="isSubmitting"
            @click="handleSubmit($event, true)"
            >Save as draft</a-button
          >
          <a-button class="btn-wide" type="primary" size="large" :loading="isSubmitting" @click="handleSubmit"
            >Submit</a-button
          >
        </template>
        <template v-else>
          <a-button
            class="btn-wide"
            type="primary"
            size="large"
            @click="handleSubmit"
            v-if="!checklistSetting.is_draft"
            :loading="isSubmitting"
            >Submit</a-button
          >
          <template v-else>
            <a-button
              class="btn-wide"
              type="dashed"
              size="large"
              @click="handleSubmit($event, true)"
              :loading="isSubmitting"
              >Save as draft</a-button
            >
            <a-button class="btn-wide" type="primary" size="large" @click="handleSubmit($event)" :loading="isSubmitting"
              >Save and Approve</a-button
            >
          </template>
        </template>
      </div>
    </div>
  </a-form>
</template>

<script>
import _ from 'lodash';
import { mapState } from 'vuex';

import ChecklistSettingCustomForm from '@/components/Settings/ChecklistSettings/Forms/ChecklistSettingCustomForm.vue';
import ChecklistSettingMainForm from '@/components/Settings/ChecklistSettings/Forms/ChecklistSettingMainForm.vue';
import ChecklistSettingRows from '@/components/Settings/ChecklistSettings/Rows/ChecklistSettingRows.vue';
import ChecklistSettingGenerator from '@/components/Settings/ChecklistSettings/Actions/ChecklistSettingGenerator.vue';
import ChecklistSettingImportByPdf from '../Actions/ChecklistSettingImportByPdf.vue';
import { checkBlockValidators } from '@/utils/validator_helper';

import { LABEL_TYPES } from '@/utils/consts';
const WAITING_TIME = 2000;
const EDIT_WAITING_TIME = 1000;
const SIGN_OFF_STEP = {
  single_sign_off: 0,
  skip: 1,
  multiple_sign_off: 2,
};

export default {
  components: {
    ChecklistSettingMainForm,
    ChecklistSettingCustomForm,
    ChecklistSettingRows,
    ChecklistSettingGenerator,
    ChecklistSettingImportByPdf,
  },
  props: {
    isEditing: {
      type: Boolean,
      default: false,
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this, {
        onValuesChange: this.handleValue,
      }),
      initalSettingValues: {}, // form's loaded value before any change is made
      isSubmitting: false,
      BY_PROMPT: 'generator',
      BY_PDF: 'generator-by-pdf',
      signOffStep: SIGN_OFF_STEP,
      afterSubmit: false,
    };
  },

  mounted() {
    this.$store.dispatch('checklist_settings/getChecklistSchedule');
    if (!this.isEditing) {
      this.form.setFieldsValue({
        ['checklist_require_completion_remarks']: true,
      });
      this.$store.dispatch('linked_items/setSelectedLocationTags', []);
    } else {
      // this.$store.dispatch('checklist_settings/setGeneratorState', undefined);
      if (this.$router.query) {
        this.$store.dispatch('checklist_settings/setGeneratorState', this.$router.query);
      }
      if (this.checklistSetting.asset_trackings) {
        this.$store.dispatch('checklist_settings/setSelectedAssets', this.checklistSetting.asset_trackings);
      }
      if (this.checklistSetting.label_ids) {
        this.$store.dispatch('checklist_labels/setSelectedLabels', {
          labelType: LABEL_TYPES.checklist,
          selected_labels: this.checklistSetting.label_ids,
        });
        let unauthorizedLabels = this.checklistSetting.labels.filter(
          (labelObj) => !this.includedInLabelOptions(labelObj),
        );
        this.$store.commit('checklist_labels/SET_UNAUTHORIZED_LABEL_OPTIONS', {
          labelType: LABEL_TYPES.checklist,
          labels: unauthorizedLabels,
        });
      }
      if (this.checklistSetting?.customers) {
        this.$store.dispatch('billing_customers/setSelectedCustomers', this.checklistSetting.customers);
      }
      this.form.setFieldsValue({
        ['checklist_name']: this.checklistSetting.name,
        ['checklist_notes']: this.checklistSetting.notes,
        ['checklist_staff_ids']: this.checklistSetting.staff_ids,
        ['checklist_manager_ids']: this.checklistSetting.manager_ids,
        ['checklist_require_check_time']: this.checklistSetting.require_check_time,
        ['checklist_require_completion_remarks']: this.checklistSetting.require_completion_remarks,
        ['checklist_require_photo']: this.checklistSetting.require_photo,
        ['is_create_child_checklist_for_each_asset']: this.checklistSetting.is_create_child_checklist_for_each_asset,
        ['is_create_child_checklist_for_each_location_tag']: this.checklistSetting
          .is_create_child_checklist_for_each_location_tag,
      });
      this.$store.dispatch('linked_items/setSelectedLocationTags', this.checklistSetting.location_tags);
    }
    this.afterSubmit = false;
    this.setInitialFormValues();

    if (this.form) {
      this.$store.dispatch('checklist_settings/setCurrentForm', this.form);
    }
  },

  methods: {
    handleValue(props, values) {
      this.$store.dispatch('checklist_settings/setCurrentChecklistSetting', {
        ...this.form.getFieldsValue(),
        ...values,
      });
    },

    setInitialFormValues() {
      // save form values before any change is made to compare with the values afterward
      let initalFormValue = this.getFormData(this.form.getFieldsValue(), false);
      initalFormValue.forEach((value, key) => (this.initalSettingValues[key] = value));
      this.initalSettingValues.logoImg = this.form.getFieldValue('images');
    },

    checkRowsBlocksValidators() {
      let areValid = true;
      this.rows.forEach((row, rowIndex) => {
        const { isValid, block: errBlock } = checkBlockValidators(row.meta_data.blocks);
        if (!isValid) {
          this.$message.error(
            `Incorrect validation settings for row [${rowIndex + 1}]: ${row.description}, block [${errBlock.index}]: ${
              errBlock.title
            }`,
          );
          areValid = false;
          return;
        }
      });

      return areValid;
    },

    getFormData(values, isDraft) {
      let formData = new FormData();
      formData.append('checklist_name', values.checklist_name);
      formData.append('checklist_notes', values.checklist_notes || '');
      formData.append('checklist_is_draft', isDraft);
      formData.append('checklist_staff_ids', JSON.stringify(values.checklist_staff_ids || []));
      formData.append('checklist_manager_ids', JSON.stringify(values.checklist_manager_ids || []));
      formData.append('checklist_require_photo', values.checklist_require_photo || false);
      formData.append('checklist_require_completion_remarks', values.checklist_require_completion_remarks || false);
      formData.append('checklist_require_check_time', values.checklist_require_check_time || false);
      formData.append(
        'checklist_asset_tracking_ids',
        JSON.stringify(this.selectedAssets.map((x) => x.id)) || JSON.stringify([]),
      );
      formData.append(
        'checklist_label_ids',
        JSON.stringify(JSON.parse(JSON.stringify(this.selectedLabels))) || JSON.stringify([]),
      );
      formData.append('checklist_customer_params', JSON.stringify(this.selectedCustomerParams) || JSON.stringify([]));
      formData.append('checklist_is_public', this.isPublic);
      formData.append(
        'is_create_child_checklist_for_each_asset',
        values.is_create_child_checklist_for_each_asset || false,
      );
      formData.append(
        'is_create_child_checklist_for_each_location_tag',
        values.is_create_child_checklist_for_each_location_tag || false,
      );
      formData.append('recurrence_start_datetime', this.customRecurrence.recurrence_start_datetime);
      formData.append('recurrence_kind', this.customRecurrence.recurrence_kind);
      formData.append('recurrence_number_repeat', this.customRecurrence.recurrence_number_repeat);
      formData.append('recurrence_day_of_week', JSON.stringify(this.customRecurrence.recurrence_day_of_week));
      formData.append('recurrence_day_of_month', this.customRecurrence.recurrence_day_of_month || 0);
      formData.append('recurrence_week_of_month', this.customRecurrence.recurrence_week_of_month);
      formData.append('recurrence_day_in_week', this.customRecurrence.recurrence_day_in_week);
      formData.append('recurrence_never_stop', this.customRecurrence.recurrence_never_stop);
      formData.append('recurrence_enabled_end_on', this.customRecurrence.recurrence_enabled_end_on);
      formData.append('recurrence_end_on', this.customRecurrence.recurrence_end_on);
      formData.append('recurrence_enabled_number_limit', this.customRecurrence.recurrence_enabled_number_limit);
      formData.append('recurrence_number_limit', this.customRecurrence.recurrence_number_limit);
      formData.append('recurrence_next_datetime', this.customRecurrence.recurrence_next_datetime || JSON.stringify([]));
      formData.append('enable_score', this.isShowMaxScore);
      formData.append('allow_responders_to_see_scoring', this.allowRespondersToSeeScoring);
      this.rows.map((row, index) => {
        row.side_node = index + 1;
      });
      formData.append('data_checklist_rows', JSON.stringify(this.rows));
      formData.append('checklist_location_tag_ids', JSON.stringify(this.linkedLocationTags.map((item) => item.id)));
      values.logo_id && formData.append('logo_id', values.logo_id);

      formData.append('sign_off_type', this.signOffStep[this.currentSignOffStep]);

      if (!_.isNil(values.checklist_process_flow_id)) {
        formData.append('checklist_process_flow_id', values.checklist_process_flow_id);
      }

      formData.append('allow_create_ad_hoc_on_mobile', values.allow_create_ad_hoc_on_mobile || false);
      formData.append('create_ad_hoc_on_mobile_restriction', values.create_ad_hoc_on_mobile_restriction || 'assigned');
      return formData;
    },

    handleSubmitDebounce: _.debounce(function(values, isDraft = false) {
      this.setInitialFormValues(); // update initalFormValues to submitted values
      let formData = this.getFormData(values, isDraft);
      let _query = { tab: isDraft ? 'draft' : 'active' };

      if (this.isEditing) {
        this.$store
          .dispatch('checklist_settings/updateChecklistSetting', {
            id: this.checklistSetting.id,
            payload: formData,
          })
          .then(() => {
            if (this.$route.query.tab === 'draft' && !isDraft) {
              this.onClearPayload();
            }
            this.afterSubmit = true;
            localStorage.setItem('scroll.checklist_setting.id', this.checklistSetting.id);
            this.$router.push({
              path: '/settings/checklist_settings',
              query: _query,
            });
            this.$message.success('Checklist Setting Updated Successfully!');
            this.$store.dispatch('checklist_settings/setIsPublic', false);
          })
          .catch((error) => {
            this.isSubmitting = false;
            this.$message.error(error.response.data.message);
          });
      } else {
        this.$store
          .dispatch('checklist_settings/createChecklistSetting', formData)
          .then(() => {
            this.$store.dispatch('checklist_settings/setIsPublic', false);
            this.onClearPayload();
            this.afterSubmit = true;
            this.$router.push({
              path: '/settings/checklist_settings',
              query: _query,
            });
            let message = isDraft
              ? 'Draft checklist successfully created and saved in the Draft tab'
              : 'Create New Checklist Setting Successfully';
            this.$message.success(message);
          })
          .catch((error) => {
            this.isSubmitting = false;
            this.$message.error(error.response.data.message);
          });
      }
    }, WAITING_TIME),

    handleSubmit(e, isDraft = false) {
      e.preventDefault();
      this.handleReValidateScoringFormula();

      this.form.validateFieldsAndScroll((err, values) => {
        if (!err) {
          // validate validators for blocks in rows
          if (!this.checkRowsBlocksValidators()) {
            return;
          }

          this.isSubmitting = true;
          if (values.images.length > 0 && values.images[0].originFileObj) {
            const fileForm = new FormData();
            fileForm.append('file', values.images[0].originFileObj);
            this.$store
              .dispatch('checklist_settings/uploadChecklistLogo', fileForm)
              .then((res) => {
                this.checklistSetting.logo?.id &&
                  this.$store.dispatch('checklist_settings/deleteChecklistLogo', this.checklistSetting.logo.id);
                values.logo_id = res.id;
                this.handleSubmitDebounce(values, isDraft);
              })
              .catch((error) => {
                this.isSubmitting = false;
                this.$message.error(error.response.data.message);
              });
          } else if (this.checklistSetting.logo?.id && !values.images.length) {
            this.$store
              .dispatch('checklist_settings/deleteChecklistLogo', this.checklistSetting.logo.id)
              .then((res) => {
                this.handleSubmitDebounce(values, isDraft);
              })
              .catch((error) => {
                this.isSubmitting = false;
                this.$message.error(error.response.data.message);
              });
          } else {
            this.handleSubmitDebounce(values, isDraft);
          }
        } else {
          this.isSubmitting = false;
          let errorMessages = this.getErrorMessage(err);
          if (errorMessages.length > 0) {
            errorMessages.forEach((msg) => {
              this.$message.error(msg);
            });

            this.$store.dispatch('checklist_setting_rows/setErrors', err);
            this.$store.dispatch('checklist_setting_rows/setValidateRenderKey');
          } else {
            this.$message.error('Please complete the compulsory fields marked with *');
          }
        }
      });
    },
    onCancel() {
      this.$router.push({
        path: '/settings/checklist_settings',
        query: this.$route.query,
      });
    },
    onClearPayload() {
      this.$store.dispatch('checklist_settings/setPayload', undefined);
    },
    onBackToChecklistGenerator() {
      this.$store.dispatch('checklist_settings/setGeneratorState', this.$router.query);
    },
    isSectionRow(row) {
      return row?.meta_data?.blocks[0]?.type === 'section';
    },
    includedInLabelOptions(label) {
      return this.labelOptions.some((option) => option.id === label.id);
    },

    getUpdatedState() {
      if (this.afterSubmit) return false;
      // Called from parent: check if any edit changes happened and return true/false to parent for route guarding
      // Compare form fields before vs after
      let newSettingValues = {};
      let currentFormValues = this.getFormData(this.form.getFieldsValue(), false);
      currentFormValues.forEach((value, key) => (newSettingValues[key] = value));
      newSettingValues.logoImg = this.form.getFieldValue('images');

      if (!_.isEqual(this.initalSettingValues, newSettingValues)) {
        return true;
      }
      return false;
    },
    getErrorMessage(err) {
      let results = [];
      let keys = Object.keys(err);

      keys.forEach((key) => {
        let errMessages = err[key].errors;
        if (_.isEmpty(errMessages)) {
          return;
        }

        results = [...results, ...errMessages.map((err) => err.message)];
      });
      return results;
    },
    handleReValidateScoringFormula() {
      const formInstance = this.form;

      let fieldsMeta = _.get(formInstance, 'fieldsStore.fieldsMeta');
      if (_.isEmpty(fieldsMeta)) return;

      let allFieldNames = Object.keys(fieldsMeta);
      let formulaFields = allFieldNames.filter((fieldName) => fieldName.includes('scoring_formula'));

      if (_.isEmpty(formulaFields)) return;

      formulaFields.forEach((fieldName) => {
        let fieldValue = formInstance.getFieldValue(fieldName);
        formInstance.setFields({
          [fieldName]: {
            value: fieldValue,
            touched: true,
            validating: true,
          },
        });
      });
    },
  },

  computed: {
    ...mapState({
      customRecurrence: (state) => state.checklist_settings.custom_recurrence,
      rows: (state) => state.checklist_setting_rows.checklist_rows,
      checklistSetting: (state) => state.checklist_settings.checklist_setting,
      isLoading: (state) => state.checklist_settings.isLoading,
      selectedAssets: (state) => state.checklist_settings.selected_assets,
      linkedLocationTags: (state) => state.linked_items.selectedLocationTags,
      selectedLabels: (state) => state.checklist_labels[LABEL_TYPES.checklist.key].selected_labels,
      selectedCustomers: (state) => state.billing_customers.selectedCustomers,
      selectedCustomerParams: (state) => state.billing_customers.selectedCustomerParams,
      generatorState: (state) => state.checklist_settings.generator_state,
      isShowMaxScore: (state) => state.checklist_settings.isShowMaxScore,
      allowRespondersToSeeScoring: (state) => state.checklist_settings.allow_responders_to_see_scoring,
      currentSignOffStep: (state) => state.checklist_settings.currentSignOffStep,
      labelOptions: (state) => state.checklist_labels[LABEL_TYPES.checklist.key].label_options,
    }),

    mappedRows() {
      let r_index = 0;

      return this?.rows?.map((item) => {
        if (!this.isSectionRow(item)) {
          r_index += 1;
        }

        return {
          ...item,
          r_index,
        };
      });
    },

    isShowBtnGenerator() {
      return !this.generatorState && this.$route.query?.type === 'generator';
    },
  },
};
</script>

<style lang="less">
.fc-checklist-setting-group-bottom {
  z-index: 2;
  position: sticky;
  bottom: -1px;
  margin: 0 -30px;
  padding: 0 30px;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-top: 1px solid #d9d9d9;
  align-items: center;
  height: 80px;
}
.border-left {
  border-left: 1px solid #d9d9d9;
}
</style>
