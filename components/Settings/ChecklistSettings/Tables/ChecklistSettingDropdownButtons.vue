<template>
  <a-dropdown>
    <a-button type="primary" icon="plus" class="fc-group-button cyan" @click="(e) => e.preventDefault()">
      New
    </a-button>
    <a-menu slot="overlay">
      <a-menu-item>
        <nuxt-link to="/settings/checklist_settings/template"
          >Start with a template from the checklist library</nuxt-link
        >
      </a-menu-item>
      <a-menu-item @click="handleDirectGeneratorChecklist('generator')">
        <template v-if="enable_generative_ai">
          <nuxt-link to="/settings/checklist_settings/new?type=generator"
            >Start using Automated Checklist Generator (powered by ChatGPT)</nuxt-link
          >
        </template>
        <template v-else>
          <span>Start using Automated Checklist Generator (powered by ChatGPT)</span>
        </template>
      </a-menu-item>

      <a-menu-item @click="handleDirectGeneratorChecklist('generator-by-pdf')">
        <template v-if="enable_generative_ai">
          <nuxt-link to="/settings/checklist_settings/new?type=generator-by-pdf">
            Start using Smart Checklist Import (Powered by ChatGPT)
            <a-tag color="orange">Beta</a-tag>
          </nuxt-link>
        </template>
        <template v-else>
          <span>
            Start using Smart Checklist Import (Powered by ChatGPT)
            <a-tag color="orange">Beta</a-tag>
          </span>
        </template>
      </a-menu-item>
      <a-menu-item @click="handleDirectGeneratorChecklist(undefined)">
        <nuxt-link to="/settings/checklist_settings/new">Start from a blank template</nuxt-link>
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>

<script>
import { mapState } from 'vuex';

export default {
  methods: {
    handleDirectGeneratorChecklist(value) {
      if (!this.enable_generative_ai && value) {
        this.$message.error('Generative AI is not supported for this instance.');
        return;
      }
      this.$store.dispatch('checklist_settings/setGeneratorState', value);
    },
  },
  computed: {
    ...mapState({
      enable_generative_ai: (state) => state.managers.profile?.agent_management?.enable_generative_ai,
    }),
  },
};
</script>
