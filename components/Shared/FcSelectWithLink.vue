<template>
  <a-form-item :label="label">
    <a-select
      v-bind="$attrs"
      :size="size"
      style="width: 100%"
      :showSearch="showSearch"
      :filterOption="filterOption"
      @select="handleSelect"
      :placeholder="placeholder"
      :getPopupContainer="(trigger) => trigger.parentNode"
      v-decorator="vDecorator"
      :loading="isLoading"
    >
      <div slot="dropdownRender" slot-scope="menu">
        <fc-link-to-management :type="type" :menu="menu" />
      </div>

      <a-select-option v-for="option in options" :key="option[nameOfKey]">
        {{ option[name] }}
      </a-select-option>

      <a-select-option :key="'loadMore_' + page" v-if="isLoadMore && !isLoadMoreCompleted">
        <infinite-loading @infinite="infiniteHandler" ref="infiniteLoading">
          <div slot="spinner"><a-spin /></div>
          <div slot="no-more"></div>
          <div slot="no-results"></div>
        </infinite-loading>
      </a-select-option>
    </a-select>
  </a-form-item>
</template>

<script>
import FcLinkToManagement from './FcLinkToManagement.vue';

export default {
  props: {
    size: {
      type: String,
      default: 'large',
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: 'Select an option',
    },
    options: {
      type: Array,
      default: [],
    },
    nameOfKey: {
      type: String,
      default: 'id',
    },
    name: {
      type: String,
      default: 'name',
    },
    showLink: {
      type: Boolean,
      default: true,
    },
    path: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: undefined,
    },
    vDecorator: {
      type: Array,
      default: () => [],
    },
    isLoadMore: {
      type: Boolean,
      default: false,
    },
    dispatchTrigger: {
      type: String,
      default: '',
    },
    pageTrigger: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      default: '',
    },
  },

  mounted() {
    this.$refs.infiniteLoading && this.$refs.infiniteLoading.stateChanger.reset();
    this.isLoadMoreCompleted = false;
  },

  data() {
    return {
      selectedValue: undefined,
      isLoadMoreCompleted: false,
      page: this.pageTrigger,
      q: '',
      isLoading: false,
      debounce: null,
    };
  },

  components: {
    FcLinkToManagement,
  },

  methods: {
    async fetch(payload = {}) {
      this.isLoading = true;
      let response = this.$store
        .dispatch(this.dispatchTrigger, {
          ...payload,
        })
        .finally(() => {
          this.isLoading = false;
        });

      return response;
    },

    filterOption(input, option) {
      if (!option.componentOptions.children[0].text) return;
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },

    handleSelect(value) {
      this.selectedValue = value;
      this.$emit('handleSelect', this.selectedValue);
    },

    infiniteHandler($state) {
      this.$store
        .dispatch(this.dispatchTrigger, {
          page: this.page,
          q: this.q,
        })
        .then(({ data }) => {
          if (data.length) {
            this.page += 1;
            $state.loaded();
          } else {
            $state.complete();
            this.isLoadMoreCompleted = true;
          }
        })
        .catch((error) => {
          $state.complete();
          this.isLoadMoreCompleted = true;
        });
    },
  },

  computed: {},
};
</script>

<style lang="less">
.link {
  padding: 5px 12px;
}
</style>
