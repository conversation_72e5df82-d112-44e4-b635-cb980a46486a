<template>
  <a @click="redirectToVendor" v-if="vendor_id">{{ vendor_name }}</a>
</template>

<script>
export default {
  props: {
    vendor_id: {
      type: Number,
    },
    vendor_name: {
      type: String,
      default: '',
    },
  },
  methods: {
    redirectToVendor() {
      const url = this.$router.resolve({
        path: '/features/vendors',
        query: { ids: JSON.stringify([this.vendor_id]) },
      }).href;

      window.open(url, '_blank', 'noopener,noreferrer');
    },
  },
};
</script>

<style lang="less" scoped></style>
