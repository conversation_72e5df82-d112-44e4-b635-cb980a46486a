<template>
  <div class="fc-filter-score-range" style="padding: 10px">
    <div class="score-range-inputs">
      <a-input-number
        v-model="min"
        :min="0"
        :max="100"
        :step="1"
        placeholder="Min"
        style="width: 80px; margin-right: 8px;"
      />
      %
      <span style="margin: 0 4px;">-</span>
      <a-input-number v-model="max" :min="0" :max="100" :step="1" placeholder="Max" style="width: 80px;" />
      %
    </div>
    <div class="footer-container">
      <a-button size="small" @click="handleReset">Reset</a-button>
      <a-button type="primary" size="small" @click="handleOK">OK</a-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    setSelectedKeys: {
      type: Function,
      required: true,
    },
    selectedKeys: {
      required: true,
      default: () => [],
    },
    confirm: {
      type: Function,
      required: true,
    },
    clearFilters: {
      type: Function,
      required: true,
    },
    column: {
      type: Object,
      required: false,
    },
  },
  data() {
    return {
      min: null,
      max: null,
    };
  },
  watch: {
    selectedKeys(newVal) {
      if (Array.isArray(newVal) && newVal.length === 2) {
        this.min = newVal[0];
        this.max = newVal[1];
      } else {
        this.min = null;
        this.max = null;
      }
    },
  },
  methods: {
    handleOK() {
      // Only set if at least one value is set
      if (this.min !== null || this.max !== null) {
        this.setSelectedKeys([this.min, this.max]);
      } else {
        this.setSelectedKeys([]);
      }
      this.confirm();
    },
    handleReset() {
      this.min = null;
      this.max = null;
      this.clearFilters();
      this.confirm();
    },
  },
  mounted() {
    if (Array.isArray(this.selectedKeys) && this.selectedKeys.length === 2) {
      this.min = this.selectedKeys[0];
      this.max = this.selectedKeys[1];
    }
  },
};
</script>

<style scoped>
.fc-filter-score-range {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 8px 0;
}
.score-range-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.footer-container {
  display: flex;
  gap: 8px;
}
</style>
