<template>
  <a-tag class="fc-due-date-tag" :color="tagColor" :disabled="disabled" @click="handleClick">
    <span v-if="dueDate.label && dueDate.label.length">{{ dueDate.label }}</span>
    <span v-else>Due Date</span>

    <span v-if="dueDate.time">: {{ dueDate.time | formatTimeStamp }}</span>
  </a-tag>
</template>

<script>
import { getDueDateStatusColor } from '~/utils/consts';
export default {
  props: {
    dueDate: {
      type: Object,
      default: () => ({}),
    },
    customColor: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleClick(e) {
      this.$emit('click', e);
    },
  },
  computed: {
    tagColor() {
      return this.customColor || getDueDateStatusColor(this.dueDate.label);
    },
  },
};
</script>

<style lang="less" scoped>
.fc-due-date-tag {
  width: fit-content;
  text-wrap: wrap;
  cursor: pointer;
}
</style>
