<template>
  <div class="toolkit-checklist-card">
    <i class="float-right selectable" @click="onRemove">
      <trash-icon v-tooltip="{ content: 'Remove this field' }" />
    </i>
    <i class="float-right margin-right-10 selectable" @click="onClickRequired">
      <required-icon v-if="block.compulsory" v-tooltip="{ content: 'This field is required' }" />
      <unrequired-icon v-if="!block.compulsory" v-tooltip="{ content: 'This field is not required' }" />
    </i>
    <a-tooltip :overlayStyle="{ whiteSpace: 'pre-line' }" :title="getHiddenIconTooltipText">
      <i class="float-right margin-right-10 selectable" @click="onHidden">
        <a-icon type="eye-invisible" class="eye-icon" v-if="block.hidden" />
        <a-icon type="eye" class="eye-icon" v-else />
      </i>
    </a-tooltip>
    <span v-if="!isEditing" class="margin-right-10"><fault-type-icon /> <span v-html="block.title"/></span>
    <i class="selectable" @click="onEdit"><edit-icon v-if="!isEditing"/></i>
    <input
      @blur="handleEdit"
      @keyup.enter="handleEdit"
      class="input-edit"
      ref="title"
      :value="block.title"
      v-if="isEditing"
    />
    <fc-form-item-input :isReadOnly="false" :block="block" :index="index" :page="page" />
  </div>
</template>

<script>
import { RequiredIcon, UnrequiredIcon, TrashIcon, FaultTypeIcon, EditIcon } from '~/components/Icons';
import { getHiddenIconTooltipText } from '@/utils/dynamic_form_item_helper';
import FcFormItemInput from '@/components/Shared/FcFormItemInput.vue';

export default {
  props: {
    block: Object,
    index: Number,
    page: String,
  },
  components: {
    RequiredIcon,
    UnrequiredIcon,
    TrashIcon,
    FaultTypeIcon,
    EditIcon,
    FcFormItemInput,
  },
  data() {
    return {
      isEditing: false,
    };
  },
  methods: {
    onClickRequired() {
      this.block.compulsory = !this.block.compulsory;
      this.$store.dispatch(`${this.page}/updateFormField`, {
        index: this.index,
        block: this.block,
      });
    },
    onRemove() {
      this.$store.dispatch(`${this.page}/removeFormField`, {
        index: this.index,
      });
    },
    onEdit() {
      this.isEditing = true;
      this.$store.dispatch('request-types/setIsEditing', true);
      this.$nextTick(() => {
        this.$refs.title.focus();
      });
    },
    handleEdit() {
      this.block.title = this.$refs.title.value;
      this.$store.dispatch(`${this.page}/updateFormField`, {
        index: this.index,
        block: this.block,
      });
      this.isEditing = false;
      this.$store.dispatch('request-types/setIsEditing', false);
    },
    onHidden() {
      this.block.hidden = !this.block.hidden;
      this.$store.dispatch(`${this.page}/updateFormField`, { index: this.index, block: this.block });
    },
    getHiddenIconTooltipText() {
      return getHiddenIconTooltipText(this.block.hidden);
    },
  },
};
</script>
<style lang="less" scoped>
.ant-form-no-margin.form-item-fault-type {
  .ant-select.ant-select-disabled.ant-select-lg {
    width: 100%;
  }
}
</style>
