<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center">
      <div class="fc-group__left">
        <fc-search @onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button v-if="isShowExportBtn" type="primary" class="fc-group-button yellow" @click="onClickExportCSV">
          <a-icon :component="ExportIcon" />
          Export CSV
        </a-button>
        <fc-add-to-dashboard :type="'facilities_data_table'" v-if="!shownInDashboard" />
      </div>
    </div>
    <fc-table
      :columns="columns"
      rowKey="id"
      :loading="loading"
      :dataSource="tableData"
      :tableHeight="currentDynamicHeight"
      @onTableChange="handleTableChange"
    >
      <a
        slot="booking"
        slot-scope="text, record"
        :href="
          `/features/facility_bookings?q=${record.name}&start_date=${record.created_at}&end_date=${record.created_at}`
        "
        target="_blank"
        rel="noopener noreferrer"
      >
        {{ text }}
      </a>
    </fc-table>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { ExportIcon } from '@/components/Icons';
import FcSearch from '@/components/Shared/FcSearch.vue';
import FcDecoratorTime from '@/components/Shared/FcDecoratorTime.vue';
import { generateAndDownloadCsv } from '@/utils/helper';

export default {
  props: {
    apiResponse: {
      type: Object,
    },
    dateRangePayload: {
      type: Object,
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: false,
  },
  components: {
    FcSearch,
    FcDecoratorTime,
  },
  data() {
    return {
      columns: [
        {
          title: 'Date',
          dataIndex: 'created_at',
          sorter: (a, b) => this.dateSorter(a.created_at, b.created_at),
          defaultSortOrder: 'descend',
        },
        {
          title: 'Facility Name',
          dataIndex: 'name',
          sorter: (a, b) => a.name.localeCompare(b.name),
        },
        {
          title: 'No. of Bookings',
          dataIndex: 'number_of_booking',
          scopedSlots: { customRender: 'booking' },
          sorter: (a, b) => a.number_of_booking - b.number_of_booking,
        },
        {
          title: 'Total Time Booked (mins)',
          dataIndex: 'total_minutes_booked',
          sorter: (a, b) => a.total_minutes_booked - b.total_minutes_booked,
        },
      ],
      ExportIcon,
      loading: false,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      dynamicHeight: 'auto',
    };
  },
  mounted() {
    if (!this.shownInDashboard) {
      this.calDynamicHeight();
    }
  },
  methods: {
    handleInputSearchSubmit(value) {
      this.q = value.toLowerCase();
    },
    handleTableChange({ pagination, filters, sorter }) {
      if (!sorter.order) {
        this.sorter.data_sort = undefined;
        this.sorter.order_sort = undefined;
      } else {
        this.sorter.data_sort = sorter.field;
        this.sorter.order_sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      }
    },

    dateSorter(dateA, dateB) {
      let dateFormat = dateA.length > 7 ? 'DD/MM/YYYY' : 'MM/YYYY';

      return moment(dateA, dateFormat) - moment(dateB, dateFormat);
    },
    getSortedTableData() {
      if (!this.tableData || !this.tableData.length) {
        return [
          {
            created_at: null,
            name: null,
            number_of_booking: null,
          },
        ];
      }

      return [...this.tableData].sort((a, b) => {
        if (!this.sorter.data_sort) return 0;
        const field = this.sorter.data_sort;
        const order = this.sorter.order_sort;

        if (field === 'created_at') {
          return order === 'asc'
            ? this.dateSorter(a.created_at, b.created_at)
            : this.dateSorter(b.created_at, a.created_at);
        }

        if (field === 'name') {
          return order === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);
        }

        return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
      });
    },
    onClickExportCSV() {
      const sortedData = this.getSortedTableData();
      const exportData = sortedData.map((item) => ({
        created_at: item.created_at,
        name: item.name,
        number_of_booking: item.number_of_booking,
        total_minutes_booked: item.total_minutes_booked,
      }));
      const headers = {
        created_at: 'Date',
        name: 'Facility Name',
        number_of_booking: 'No. of Bookings',
        total_minutes_booked: 'Total Time Booked (mins)',
      };

      generateAndDownloadCsv(exportData, 'facilities_statistics', headers);
    },

    calDynamicHeight() {
      this.$store.dispatch('dynamic_height/setIsCalculateDynamicHeight', true);
      this.$store.dispatch('dynamic_height/setCurrentSettings', {
        pageContainer: 'facilities-statistics-container',
        isExistTabBar: false,
        isExistGroupOnTop: true,
        isExistPageTip: false,
      });
    },
  },
  computed: {
    ...mapState({
      currentDynamicHeight: (state) => state.dynamic_height.currentDynamicHeight,
    }),
    trackingData() {
      return Object.keys(this.apiResponse).length ? this.apiResponse.data : [];
    },
    tableData() {
      if (this.q.length <= 0) {
        return this.trackingData;
      }

      return this.trackingData.filter((row) => {
        const rowSearchText = [row.created_at, row.name, row.number_of_booking, row.total_minutes_booked]
          .join()
          .toLowerCase();
        return rowSearchText.includes(this.q);
      });
    },
  },
};
</script>

<style></style>
