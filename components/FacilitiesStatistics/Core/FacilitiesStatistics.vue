<template>
  <div class="facilities-statistics-container">
    <div class="facilities-statistics-filter" style="padding: 0;">
      <facilities-statistics-filter
        @applyFilter="onApplyFilter"
        @setInitialDateRangeAndFrequencyPayload="setInitialDateRangeAndFrequencyPayload"
      />
    </div>
    <div class="facilities-statistics-table">
      <facilities-statistics-table
        :apiResponse="facilitiesStatistics"
        :dateRangePayload="dateRangeAndFrequencyPayload"
      />
    </div>
  </div>
</template>

<script>
import './index.less';
import { mapState } from 'vuex';
import FacilitiesStatisticsFilter from './FacilitiesStatisticsFilter.vue';
import FacilitiesStatisticsTable from './FacilitiesStatisticsTable.vue';

export default {
  components: {
    FacilitiesStatisticsFilter,
    FacilitiesStatisticsTable,
  },
  data() {
    return {
      dateRangeAndFrequencyPayload: {},
    };
  },
  computed: {
    ...mapState({
      facilitiesStatistics: (state) => state.facilities_statistics.facilitiesStatistics,
    }),
  },
  methods: {
    setInitialDateRangeAndFrequencyPayload(childPayload) {
      this.dateRangeAndFrequencyPayload = childPayload;
    },
    fetch(payload) {
      this.$store.dispatch('facilities_statistics/loadFacilitiesStatistics', { ...payload });
    },
    onApplyFilter(childPayload) {
      this.dateRangeAndFrequencyPayload = childPayload;
      this.$store.dispatch('facilities_statistics/loadFacilitiesStatistics', childPayload);
      this.$store.dispatch('facilities_statistics/setDateRange', childPayload);
    },
  },
};
</script>
