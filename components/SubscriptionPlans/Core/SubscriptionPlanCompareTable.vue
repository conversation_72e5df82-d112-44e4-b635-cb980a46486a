<template>
  <div class="subscription-plan-compare-table">
    <div v-for="(feature, i) in plansBenefits" :key="i">
      <div class="plan-row comparison-table feature-group">
        <a
          v-if="feature.link"
          :href="`${LANDING_PAGE_PATH}${feature.link}`"
          target="_blank"
          rel="nofollow noopener"
          class="plan-column feature-group-link"
        >
          {{ feature.feature_group_name }}
        </a>
        <div v-else class="plan-column feature-group-name">
          {{ feature.feature_group_name }}
        </div>
        <div class="plan-column"></div>
        <div class="plan-column"></div>
        <div class="plan-column"></div>
      </div>
      <div
        class="plan-row comparison-table"
        v-for="(f, index) in feature.features"
        :class="getClass(index, feature)"
        :key="index"
      >
        <div class="plan-column comparison-header">
          <a v-if="f.link" :href="`${LANDING_PAGE_PATH}${f.link}`" target="_blank" rel="nofollow noopener">
            {{ f.name }}
          </a>
          <span v-else>
            {{ f.name }}
          </span>
        </div>
        <div class="plan-column" v-for="(v, i) in f.value" style="margin: auto" :key="i">
          <a-icon type="check-circle" :theme="'twoTone'" :twoToneColor="'#22D69B'" v-if="v === true" />
          <a-icon type="minus-circle" :theme="'twoTone'" :twoToneColor="'#FF6377'" v-else-if="v === false" />

          <span v-else-if="renderValue(v)">
            <span v-if="v === 'custom_storage'"> {{ number_of_accounts / 10 }} TB </span>
            <span v-else-if="v === 'custom_onboarding'" class="custom-onboarding">
              <a
                href="https://facilitybot.co/resources/onboarding#enhanced-onboarding"
                target="_blank"
                rel="nofollow noopener"
              >
                &dollar;
              </a>
            </span>
            <span v-else-if="v === 'custom_analytics'" class="custom-analytics">
              <a href="https://facilitybot.co/features/analytics" target="_blank" rel="nofollow noopener">
                &dollar;
              </a>
            </span>
            <span v-else-if="v === 'custom_property_portfolio'" class="custom-property-portfolio">
              <a href="https://facilitybot.co/features/property-portfolio" target="_blank" rel="nofollow noopener">
                &dollar;
              </a>
            </span>
            <span v-else-if="v === 'limit_accounts'">
              {{ number_of_accounts }}
            </span>
            <span v-else-if="v === 'limit_sensors'"> Up to {{ number_of_accounts * 2 }} </span>
            <span v-else>
              {{ v }}
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

const PLANS = {
  Basic: {
    name: 'Basic',
    maximum_accounts: 10,
    currency: {
      USD: {
        annual: 450,
        monthly: 540,
      },
      SGD: {
        annual: 600,
        monthly: 720,
      },
      INR: {
        annual: 36000,
        monthly: 44000,
      },
    },
  },
  Professional: {
    name: 'Professional',
    maximum_accounts: 25,
    currency: {
      USD: {
        annual: 925,
        monthly: 1110,
      },
      SGD: {
        annual: 1250,
        monthly: 1500,
      },
      INR: {
        annual: 75000,
        monthly: 90000,
      },
    },
  },
  Plus: {
    name: 'Plus',
    maximum_accounts: 50,
    currency: {
      USD: {
        annual: 1850,
        monthly: 2220,
      },
      SGD: {
        annual: 2500,
        monthly: 3000,
      },
      INR: {
        annual: 150000,
        monthly: 180000,
      },
    },
    additional_account_price: {
      USD: {
        annual: 37,
        monthly: 45,
      },
      SGD: {
        annual: 50,
        monthly: 60,
      },
      INR: {
        annual: 3000,
        monthly: 3600,
      },
    },
  },
};

const CUSTOM_CELL_VALUE = {
  // Pricing table cells with custom value render logic
  dynamicAccountsLimit: 'limit_accounts',
  dynamicSensorsLimit: 'limit_sensors',
  enhancedOnboardingLink: 'custom_onboarding',
  dynamicStorage: 'custom_storage',
  analyticsLink: 'custom_analytics',
  propertyPortfolioLink: 'custom_property_portfolio',
};

const ALL_PLANS = Object.entries(PLANS).map(([key, planObj]) => planObj); // array of all defined plans above

export default {
  data() {
    return {
      plansBenefits: [
        {
          feature_group_name: 'Accounts',
          features: [
            {
              name: 'Unlimited Requestors',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Maximum number of Admin, Manager and Responder Accounts',
              value: this.getPlanValues({
                Basic: 10,
                Professional: 25,
                Plus: CUSTOM_CELL_VALUE.dynamicAccountsLimit,
              }),
            },
            {
              name: 'Custom Manager Accounts',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Mobile',
          features: [
            {
              name: 'Requestor Mobile App',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Responder Mobile App',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Push Notifications',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Requests',
          features: [
            {
              name: 'Unlimited Requests ("Work Orders")',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Fault Reporting',
              value: this.getAppliedPlans(ALL_PLANS),
              link: 'features/fault-reporting',
            },
            {
              name: 'Custom Service Requests',
              value: this.getAppliedPlans(ALL_PLANS),
              link: 'features/custom-service-requests',
            },
            {
              name: 'Export Requests to CSV',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Generate Request PDF',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Checklists',
          link: 'features/preventive-maintenance',
          features: [
            {
              name: 'Unlimited Checklists',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Unlimited Rows in Checklists',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Checklist Builder',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Checklist Templates',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Checklist Scheduling',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Checklist Calendar',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Send Checklists via Email',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Completion of Checklists via Mobile App',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Assets',
          link: 'features/asset-management',
          features: [
            {
              name: 'Unlimited Assets',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Import Assets',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Custom Asset Fields',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Asset Hierachy view',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Asset Request History',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Asset Checklist History',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Asset Parts History',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Asset Metering History',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'QR Code Web Forms',
          link: 'features/qr_code_fault_reporting',
          features: [
            {
              name: 'Unlimited Web Forms',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Location QR Code Web Forms',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Asset QR Code Web Forms',
              value: this.getAppliedPlans(ALL_PLANS),
              link: 'features/qr-code-scanning',
            },
            {
              name: 'Toilet Feedback Form Webview',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Meters',
          link: 'features/meters',
          features: [
            {
              name: 'Unlimited Meters',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Scheduled Meter Readings via Checklists',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'IoT Meter Readings',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Parts',
          link: 'features/parts',
          features: [
            {
              name: 'Unlimited Parts',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Link Parts to Assets',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Permit to Work',
          link: 'features/permit-to-work',
          features: [
            {
              name: 'Unlimited Permit to Work Applications',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Licenses',
          link: 'features/licenses',
          features: [
            {
              name: 'Unlimited Licenses',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Contracts',
          link: 'features/contracts',
          features: [
            {
              name: 'Unlimited Contracts',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Leases',
          link: 'features/leases',
          features: [
            {
              name: 'Unlimited Leases',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Workflows',
          link: 'features/configurable-workflows',
          features: [
            {
              name: 'Automatic Assignment',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Automatic Email Alerts',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Attendance',
          link: 'features/attendance',
          features: [
            {
              name: 'Unlimited site locations',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Unlimited check in / check outs',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Responder Live Location',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
              link: 'features/responder-live-location',
            },
          ],
        },
        {
          feature_group_name: 'Schedules',
          link: 'features/schedules',
          features: [
            {
              name: 'Unlimited Responder Scheduling',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Vendors',
          link: 'features/vendor-management',
          features: [
            {
              name: 'Unlimited Vendors and Invoices',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Schedule of Rates',
          link: 'features/schedule-of-rates',
          features: [
            {
              name: 'Unlimited Schedule of Rates',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Procurement',
          link: 'features/procurement',
          features: [
            {
              name: 'Unlimited RFQs',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Expenditures',
          link: 'features/expenditures',
          features: [
            {
              name: 'Unlimited Expenditures',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Budgets',
          link: 'features/budget',
          features: [
            {
              name: 'Unlimited Budgets',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Billing',
          link: 'features/billing',
          features: [
            {
              name: 'Unlimited Billing',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Payments',
          link: 'features/payments',
          features: [
            {
              name: 'Unlimited Payment tickets',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Customers',
          link: 'features/customers',
          features: [
            {
              name: 'Unlimited Customers',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Tenant portal',
          link: 'features/tenant-portal',
          features: [
            {
              name: 'Custom Subdomain and Configurable Sign In Page',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Facilities',
          link: 'features/facility-and-room-booking',
          features: [
            {
              name: 'Unlimited Facilities',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Unlimited Facility Bookings',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Room Tablet Webview',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Desks',
          link: 'features/hotdesks',
          features: [
            {
              name: 'Unlimited Zones / Hot Desks',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Deliveries',
          link: 'features/deliveries',
          features: [
            {
              name: 'Unlimited Deliveries',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Visitors',
          link: 'features/visitor-management',
          features: [
            {
              name: 'Unlimited Visitors',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Visitors Sign In Webview',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Visit Reception Tablet',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },

        {
          feature_group_name: 'FAQs',
          link: 'features/automated-faq-response',
          features: [
            {
              name: 'Unlimited FAQs',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Surveys',
          link: 'features/surveys',
          features: [
            {
              name: 'Unlimited Surveys',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Broadcasts',
          link: 'features/broadcasts',
          features: [
            {
              name: 'Unlimited Broadcasts',
              value: this.getAppliedPlans(ALL_PLANS),
            },
          ],
        },
        {
          feature_group_name: 'Statistics',
          link: 'features/statistics',
          features: [
            {
              name: 'Custom Dashboard',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
              link: 'features/custom-dashboard',
            },
            {
              name: 'Map Dashboard',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
              link: 'features/map-dashboard',
            },
            {
              name: 'Pre-Configured Charts and Tables',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Export CSV',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Analytics ',
              value: this.getPlanValues({
                Basic: CUSTOM_CELL_VALUE.analyticsLink,
                Professional: CUSTOM_CELL_VALUE.analyticsLink,
                Plus: CUSTOM_CELL_VALUE.analyticsLink,
              }),
              link: 'features/analytics',
            },
            {
              name: 'Audit Log',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
          ],
        },
        {
          feature_group_name: 'Integrations',
          features: [
            {
              name: 'Telegram',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Facebook',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Workplace',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Slack',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Teams',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Line',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Viber',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Webchat',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Google Chat',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'WhatsApp',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Sensors',
              value: this.getPlanValues({
                Professional: 'Up to 50',
                Plus: CUSTOM_CELL_VALUE.dynamicSensorsLimit,
              }),
              link: 'features/iot-sensors-integration',
            },
            {
              name: 'Building Information Model (BIM) Integration',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
              link: 'features/bim-integration',
            },
            {
              name: 'API',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Zapier',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Single Sign On',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Portfolio Portal',
              value: this.getPlanValues({
                Basic: CUSTOM_CELL_VALUE.propertyPortfolioLink,
                Professional: CUSTOM_CELL_VALUE.propertyPortfolioLink,
                Plus: CUSTOM_CELL_VALUE.propertyPortfolioLink,
              }),
              link: 'features/property-portfolio',
            },
          ],
        },
        {
          feature_group_name: 'Support',
          features: [
            {
              name: 'In Platform Chat',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Zoom Support Call',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Email Support',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Dedicated Customer Success Manager',
              value: this.getAppliedPlans([PLANS.Professional, PLANS.Plus]),
            },
            {
              name: 'Standard Onboarding',
              value: this.getAppliedPlans(ALL_PLANS),
            },
            {
              name: 'Enhanced Onboarding',
              value: this.getPlanValues({
                Basic: CUSTOM_CELL_VALUE.enhancedOnboardingLink,
                Professional: CUSTOM_CELL_VALUE.enhancedOnboardingLink,
                Plus: CUSTOM_CELL_VALUE.enhancedOnboardingLink,
              }),
              link: 'resources/onboarding#enhanced-onboarding',
            },
          ],
        },
        {
          feature_group_name: 'Storage',
          features: [
            {
              name: 'Maximum Storage',
              value: this.getPlanValues({
                Basic: '1TB',
                Professional: '2.5TB',
                Plus: CUSTOM_CELL_VALUE.dynamicStorage,
              }),
            },
          ],
        },
      ],
      LANDING_PAGE_PATH: this.$config.fbotLandingPageUrl,
    };
  },
  methods: {
    getClass(index, feature) {
      return index < feature.features.length - 1 ? 'border-bottom' : '';
    },
    getAppliedPlans(appliedPlans) {
      // Receives an array of applied plans
      let values = [];
      for (let key in PLANS) {
        values.push(appliedPlans.includes(PLANS[key]) ? true : false);
      }

      return values;
    },
    getPlanValues(planValues) {
      // Receive an object of any size, object's keys = Defined plan's name
      let values = [];
      for (let definedPlan in PLANS) {
        if (definedPlan in planValues) values.push(planValues[definedPlan]);
        else values.push(false);
      }
      return values;
    },
  },
  computed: {
    ...mapState({
      number_of_accounts: (state) => state.subscription_plans.number_of_accounts,
    }),
    number() {
      return this.number_of_accounts;
    },

    renderValue() {
      return (value) => {
        return typeof value === 'string' || typeof value === 'number';
      };
    },
  },
};
</script>

<style lang="less">
.subscription-plan-compare-table {
  position: relative;
  z-index: 1;

  .plan-row {
    &.comparison-table {
      &.feature-group {
        background: #fff4f2 !important;

        .feature-group-name {
          background: inherit;
          text-align: left;
          font-size: 20px;
          line-height: 26px;
          color: #ff765d;
        }

        .feature-group-link {
          font-size: 20px;
          line-height: 26px;
          text-align: left;
          text-decoration: none;
        }
      }

      .comparison-header {
        background: #fff;
        text-align: left;
        font-size: 14px;
        line-height: 26px;
        font-weight: 400;
        // color: #ff765d;
      }

      & .plan-column {
        border-right: none !important;

        .custom-onboarding,
        .custom-analytics,
        .custom-property-portfolio {
          a {
            font-size: 18px;
            color: #ff765d;
          }
        }
      }

      & .plan-column:first-child {
        border-right: 1px solid #dbdde0 !important;
      }
    }
  }
}
.border-bottom {
  border-bottom: 1px solid #dbdde0;
}
</style>
