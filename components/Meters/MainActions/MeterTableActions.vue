<template>
  <div id="fc-gutter" :class="visibleCls">
    <reminder-action :actionVisible="actionVisible" :data="data" />
    <statistics-action :actionVisible="actionVisible" :data="data" />
    <new-reading-action :actionVisible="actionVisible" :data="data" />
    <edit-action :actionVisible="actionVisible" :data="data" />
    <delete-action :actionVisible="actionVisible" :data="data" />
  </div>
</template>

<script>
import EditAction from './MeterItems/EditAction.vue';
import DeleteAction from './MeterItems/DeleteAction.vue';
import StatisticsAction from './MeterItems/StatisticsAction.vue';
import NewReadingAction from './MeterItems/NewReadingAction.vue';
import ReminderAction from './MeterItems/ReminderAction.vue';
export default {
  props: {
    data: Object,
    actionVisible: Boolean,
  },
  components: {
    EditAction,
    DeleteAction,
    StatisticsAction,
    NewReadingAction,
    ReminderAction,
  },
  computed: {
    visibleCls() {
      return this.actionVisible ? '' : 'fc-gutter-hide';
    },
  },
  methods: {
    isVisible() {
      return (
        this.data.status === 'Pending Approval' &&
        this.data.sent.filter((x) => x.assign_to_manager_id != null).length == 0
      );
    },
    isPending() {
      return this.data.status === 'Pending Approval';
    },
  },
};
</script>

<style lang="less"></style>
