<template>
  <a-badge
    v-if="actionVisible"
    :status="hasActiveReminders ? 'warning' : null"
    :offset="[-8, 8]"
    :dot="hasActiveReminders"
  >
    <a-button
      :type="buttonType"
      icon="bell"
      v-tooltip="{ content: tooltipContent }"
      @click="handleClick"
      class="reminder-button"
    />
  </a-badge>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      required: true,
    },
    actionVisible: {
      type: Boolean,
      default: true,
    },
    buttonType: {
      type: String,
      default: 'primary',
    },
  },
  computed: {
    hasActiveReminders() {
      return this.data?.has_recurrence ?? false;
    },
    tooltipContent() {
      return this.hasActiveReminders
        ? 'Meter reading reminders are active for this meter'
        : 'Set reminders to record meter readings';
    },
  },
  methods: {
    handleClick() {
      this.$store.dispatch('meters/setIsShowDrawer', true);
      this.$store.dispatch('meters/setCurrentDrawerGutter', 'reminder');
      this.$store.dispatch('meters/setCurrentMeter', this.data);
    },
  },
};
</script>

<style scoped>
.reminder-button {
  /* Ensure consistent button styling */
}
</style>
