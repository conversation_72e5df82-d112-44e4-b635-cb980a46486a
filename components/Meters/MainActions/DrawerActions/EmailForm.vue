<template>
  <div>
    <a-row style="margin-top: 32px">
      <a-checkbox :checked="isPushReminderNotifications" @change="handleChangeIsPushReminderNotifications">
        <span>Mobile Notification</span>
      </a-checkbox>
    </a-row>

    <a-row style="margin-top: 32px" v-if="this.isPushReminderNotifications">
      <a-select
        class="select-assign-managers"
        mode="multiple"
        size="large"
        showSearch
        placeholder="Please select accounts"
        :getPopupContainer="(trigger) => trigger.parentNode"
        :filterOption="false"
        @search="handleSearch"
        @change="handleChangeAssignedManagers"
        :value="reminderManagerIds"
      >
        <div slot="dropdownRender" slot-scope="menu">
          <fc-link-to-management type="ACCOUNTS" :menu="menu" />
        </div>

        <a-select-option v-for="(cb, index) in managers" :key="index" :value="cb.id">
          <div class="manager-option">
            <span class="manager-name">{{ cb.name }}</span>
            <fc-status-tag :status="cb.role || cb.role_text || 'User'" class="manager-role" />
          </div>
        </a-select-option>
      </a-select>
    </a-row>

    <a-row style="margin-top: 32px">
      <a-checkbox :checked="isSendReminderEmails" @change="handleChangeIsSendReminderEmails">
        <span>Email Notification ({{ reminderEmails.length }}/{{ limitedEmailNumber }})</span>
      </a-checkbox>
    </a-row>

    <a-row style="margin-top: 32px" v-if="isSendReminderEmails">
      <a-col span="24">
        <div class="flex flex-col">
          <div class="email-input-container">
            <a-input
              v-model="newEmail"
              size="large"
              placeholder="Enter email address"
              style="flex: 1"
              :disabled="reminderEmails.length >= limitedEmailNumber"
              @pressEnter="addEmail"
            />
            <a-button
              type="primary"
              size="large"
              @click="addEmail"
              :disabled="!newEmail || reminderEmails.length >= limitedEmailNumber"
            >
              Add
            </a-button>
          </div>
        </div>
      </a-col>
    </a-row>

    <a-row style="margin-top: 32px" v-if="isSendReminderEmails">
      <a-table :columns="emailColumns" :data-source="reminderEmails" bordered :pagination="false" rowKey="id">
        <template slot="email" slot-scope="text, record">
          <editable-cell :text="text" @change="onCellChange(record.id, 'email', $event)" />
        </template>
        <template slot="operation" slot-scope="text, record">
          <a-popconfirm v-if="reminderEmails.length" title="Sure to delete?" @confirm="() => onDelete(record.id)">
            <a href="javascript:;">Delete</a>
          </a-popconfirm>
        </template>
      </a-table>
    </a-row>
  </div>
</template>

<script>
import _ from 'lodash';
import EditableCell from './EditableCell.vue';
import FcStatusTag from '@/components/Shared/FcStatusTag.vue';
import { validateEmail } from '@/utils/helper';
import { mapState } from 'vuex';

const emailColumns = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    scopedSlots: { customRender: 'email' },
  },
  {
    title: '',
    dataIndex: 'operation',
    scopedSlots: { customRender: 'operation' },
  },
];
export default {
  components: {
    EditableCell,
    FcStatusTag,
  },
  props: {},
  data() {
    return {
      newEmail: '',
      emailColumns,
    };
  },
  methods: {
    addEmail(e) {
      if (e && e.preventDefault) {
        e.preventDefault();
        e.stopPropagation();
      }
      if (!this.deepValidateEmail(this.newEmail)) {
        this.$message.error('Please enter a valid email address.');
        return;
      }

      let reminderEmailsClone = _.cloneDeep(this.reminderEmails);

      const emailStrings = reminderEmailsClone.map((item) => item.email);
      emailStrings.push(this.newEmail);
      reminderEmailsClone = this.remapEmails(emailStrings);

      this.$store.dispatch('meters/setReminderEmails', reminderEmailsClone);

      this.newEmail = '';
    },
    onDelete(id) {
      let reminderEmailsClone = _.cloneDeep(this.reminderEmails);

      reminderEmailsClone = reminderEmailsClone.filter((e) => e.id !== id);
      this.remapEmails(reminderEmailsClone);
      this.$store.dispatch('meters/setReminderEmails', reminderEmailsClone);
    },
    onCellChange(id, dataIndex, value) {
      let reminderEmailsClone = _.cloneDeep(this.reminderEmails);

      const targetIndex = reminderEmailsClone.findIndex((item) => item.id === id);

      if (targetIndex !== -1) {
        if (dataIndex === 'email' && (!value || value.trim() === '' || !validateEmail(value))) {
          this.$message.error('Please enter a valid email address.');
          return;
        }

        reminderEmailsClone[targetIndex][dataIndex] = value.trim();
        reminderEmailsClone = this.remapEmails(reminderEmailsClone.map((item) => item.email));

        this.$store.dispatch('meters/setReminderEmails', reminderEmailsClone);
      }
    },
    remapEmails(emails) {
      return emails.map((email, index) => {
        return {
          email,
          id: index + 1,
        };
      });
    },
    deepValidateEmail(email) {
      return email && email.trim() !== '' && validateEmail(email);
    },
    handleChangeIsSendReminderEmails(e) {
      this.$store.dispatch('meters/setIsSendReminderEmails', e.target.checked);
    },
    handleChangeIsPushReminderNotifications(e) {
      this.$store.dispatch('meters/setIsPushReminderNotifications', e.target.checked);
    },
    handleSearch(value) {
      if (this.timeout) {
        clearTimeout(this.timeout);
        this.timeout = null;
      }

      this.timeout = setTimeout(() => {
        this.loadAssignmentManagers({ q: value });
      }, 200);
    },
    async loadAssignmentManagers(payload = {}) {
      this.$store
        .dispatch('managers/loadAssignmentManagers', payload)
        .then((res) => {
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
    handleChangeAssignedManagers(e) {
      this.$store.dispatch('meters/setReminderManagerIds', e);
    },
  },
  computed: {
    ...mapState({
      reminderEmails: (state) => state.meters.reminder_emails,
      isSendReminderEmails: (state) => state.meters.is_send_reminder_emails,
      isPushReminderNotifications: (state) => state.meters.is_push_reminder_notifications,
      managers: (state) => state.managers.assignment_managers,
      reminderManagerIds: (state) => state.meters.reminder_manager_ids,
      limitedEmailNumber: (state) => state.meters.meter?.limited_email_number,
    }),
  },
  mounted() {
    this.loadAssignmentManagers();
  },
};
</script>

<style scoped lang="less">
.email-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.manager-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.manager-name {
  flex: 1;
  margin-right: 12px;
}

.manager-role {
  flex-shrink: 0;
}
</style>
