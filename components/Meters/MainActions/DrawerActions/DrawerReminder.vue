<template>
  <a-spin class="fc-spin-container" :spinning="isLoading" v-if="isLoading"></a-spin>
  <a-form :form="form" @submit="handleSubmit" class="fc-v2-drawer-container" v-else>
    <div class="fc-v2-drawer-container__body">
      <a-form-item label="Start date and time">
        <a-date-picker
          ref="startDateTime"
          size="large"
          style="width: 100%"
          format="DD/MM/YYYY HH:mm"
          :disabled-date="disabledStartDate"
          :show-time="{ defaultValue: $moment('00:00', 'HH:mm'), format: 'HH:mm' }"
          @change="handleStartDateChange"
          v-model="startTimeMomentFormat"
        />
      </a-form-item>
      <a-tabs :default-active-key="timeTabActiveKey" @change="handleDateTabChange">
        <a-tab-pane :key="repeatTimeType">
          <div slot="tab">
            <a-tooltip
              title="After setting the Start Date and Time, you can set the frequency (day, week, month) for the next child (i.e. pending) checklists to be generated."
            >
              Repeat Every
              <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
            </a-tooltip>
          </div>
          <div>
            <div class="ant-form-item-label"></div>
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <a-input-number
                  size="large"
                  :min="1"
                  :max="100000"
                  v-model="numberRepeat"
                  @change="handleRepeatEveryChange"
                  style="width: 100%"
                />
              </a-col>
              <a-col :span="16">
                <a-select
                  v-model="repeatTimeType"
                  size="large"
                  style="width: 100%"
                  @change="handleRepeatTimeChange"
                  :getPopupContainer="(trigger) => trigger.parentNode"
                >
                  <a-select-option value="day"> Day </a-select-option>
                  <a-select-option value="week"> Week </a-select-option>
                  <a-select-option value="month"> Month </a-select-option>
                </a-select>
              </a-col>
            </a-row>
            <a-row v-if="repeatTimeType === 'week'">
              <div class="ant-form-item-label">
                <label class="ant-form-item-no-colon">Repeat on</label>
              </div>
              <repeat-week-form :defaultValue="dayOfWeek" v-on:onSelectedChange="handleRepeatWeekFormChange" />
            </a-row>
            <a-row v-else-if="repeatTimeType === 'month'">
              <div class="ant-form-item-label">
                <label class="ant-form-item-no-colon">Repeat on</label>
              </div>
              <repeat-month-form
                :dayString="startTime"
                :kind="getKindOfMonth()"
                :defaultMonth="defaultMonth"
                :isLastDayOfMonthValid="isLastDayOfMonthValid"
                @onRepeatMonthChange="handleRepeatMonthChange"
              />
            </a-row>
            <a-row>
              <div class="ant-form-item-label">
                <label class="ant-form-item-no-colon"
                  >Ends
                  <a-tooltip title="Set the ending condition for the recurrence.">
                    <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
                  </a-tooltip>
                </label>
              </div>
              <a-col>
                <a-radio-group class="fc-radio-button-group" v-model="radioButtonData" @change="handleModeChange">
                  <a-row>
                    <a-col span="24">
                      <a-radio value="never_stop" class="fc-radio-button">
                        Never
                      </a-radio>
                    </a-col>
                  </a-row>
                  <a-row :gutter="[16, 16]">
                    <a-col span="6">
                      <div class="fc-radio-button">
                        <a-radio value="end_on"> On </a-radio>
                      </div>
                    </a-col>
                    <a-col span="18">
                      <div class="fc-radio-button" @click="handleSetEndDateClick('end_on')">
                        <a-date-picker
                          ref="endOn"
                          size="large"
                          format="DD/MM/YYYY"
                          :allowClear="false"
                          style="width: 100%"
                          :disabled-date="disabledEndDate"
                          :default-value="$moment().add(1, 'year')"
                          v-model="endOn"
                          @change="handleEndDateChange"
                        />
                      </div>
                    </a-col>
                  </a-row>
                  <a-row :gutter="[16, 16]">
                    <a-col span="6">
                      <div class="fc-radio-button">
                        <a-radio value="number_limit"> After </a-radio>
                      </div>
                    </a-col>
                    <a-col span="18">
                      <div class="fc-radio-button" @click="handleSetEndDateClick('number_limit')">
                        <div class="fc-meters-reminder-custom-input-number">
                          <a-input-number
                            size="large"
                            :min="0"
                            :max="100000"
                            v-model="numberLimit"
                            @change="handleChangeNumberLimit"
                          />
                          <span style="padding: 12px">occurrences</span>
                        </div>
                      </div>
                    </a-col>
                  </a-row>
                </a-radio-group>
              </a-col>
            </a-row>

            <a-row style="margin-top: 32px">
              <a-table :columns="columns" :data-source="schedules" bordered :pagination="false" rowKey="id"> </a-table>
            </a-row>

            <email-form></email-form>
          </div>
        </a-tab-pane>
        <a-tab-pane tab="OR" disabled></a-tab-pane>
        <a-tab-pane key="custom_datetime">
          <div slot="tab">
            <a-tooltip
              title="If your maintenance checklists do not recur at fixed intervals, use this tab to set custom date / times for the next checklists to be generated."
            >
              Add next date and time
              <a-icon type="info-circle" theme="filled" class="checklist-setting-info-circle" />
            </a-tooltip>
          </div>
          <div>
            <div class="ant-form-item-label"></div>
            <a-form
              layout="inline"
              :form="custom_schedule_form"
              @submit="handleAddCustomSchedule"
              class="fc-next-date-container"
            >
              <a-form-item>
                <a-date-picker
                  format="DD/MM/YYYY hh:mm A"
                  :disabled-date="disabledStartDate"
                  size="large"
                  :show-time="{ format: 'HH:mm' }"
                  placeholder="Select date and time"
                  v-decorator="[
                    'time',
                    {
                      rules: [
                        {
                          required: true,
                          message: 'Please select date and time',
                        },
                      ],
                    },
                  ]"
                />
              </a-form-item>
              <a-form-item>
                <a-button html-type="submit" size="large">Add next date and time</a-button>
              </a-form-item>
            </a-form>
            <a-row style="margin-top: 32px">
              <a-table
                :columns="custom_schedule_columns"
                :data-source="customSchedules"
                bordered
                rowKey="id"
                :pagination="{ pageSize: 5 }"
              >
                <span slot="action" slot-scope="text, record">
                  <a-button icon="delete" @click="handleDeleteCustomSchedule(record.id)" />
                </span>
              </a-table>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="fc-global-drawer__footer fc-v2-drawer-footer">
      <div class="fc-global-drawer__footer--group-buttons">
        <a-button size="large" type="default" @click="onCancel">Cancel</a-button>
        <a-button size="large" type="primary" html-type="submit">Submit</a-button>
      </div>
    </div>
  </a-form>
</template>

<script>
import RepeatMonthForm from '../../../Settings/ChecklistSettings/Forms/RepeatTimeForm/RepeatMonthForm.vue';
import RepeatWeekForm from '../../../Settings/ChecklistSettings/Forms/RepeatTimeForm/RepeatWeekForm.vue';
import EmailForm from './EmailForm.vue';

import moment from 'moment';
import RSVP from 'rsvp';

import { mapState } from 'vuex';

const columns = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'Upcoming checklist schedule: First 5',
    dataIndex: 'time',
    key: 'time',
  },
];

const custom_schedule_columns = [
  {
    title: 'Custom Schedule',
    dataIndex: 'time',
    key: 'time',
    sorter: (a, b) => a.time.localeCompare(b.time),
  },
  {
    title: 'Action',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
];

export default {
  components: { RepeatMonthForm, RepeatWeekForm, EmailForm },
  props: {
    data: Object,
  },
  computed: {
    ...mapState({
      custom_recurrence: (state) => state.meters.custom_recurrence,
      recurrence: (state) => state.meters.recurrence,
      reminderEmails: (state) => state.meters.reminder_emails,
      isSendReminderEmails: (state) => state.meters.is_send_reminder_emails,
      isPushReminderNotifications: (state) => state.meters.is_push_reminder_notifications,
      reminderManagerIds: (state) => state.meters.reminder_manager_ids,
    }),
    schedules() {
      if (this.$store.state.meters.schedules.length > 0) {
        return this.$store.state.meters.schedules.map((x, index) => ({
          id: index + 1,
          time: x,
        }));
      } else {
        return [];
      }
    },
    isLastDayOfMonthValid() {
      return moment(this.startTime, 'DD/MM/YYYY HH:mm').date() <= 28;
    },
    timeTabActiveKey() {
      if (this.isEditing) return this.custom_recurrence['recurrence_kind'];
      else return 'day';
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      isLoading: false,
      isEditing: false,

      repeatTimeType: 'day',
      startTime: moment().format('DD/MM/YYYY HH:mm'),
      startTimeMomentFormat: this.isEditing
        ? moment(this.customRecurrence['recurrence_start_datetime'], 'X')
        : moment(),
      endOn: this.isEditing
        ? moment(this.customRecurrence['recurrence_start_datetime'], 'X').add(1, 'year')
        : moment().add(1, 'year'),
      radioButtonData: 'number_limit',
      numberRepeat: 1,
      dayOfWeek: [new Date().getDay()],
      numberLimit: 0,
      columns,
      defaultMonth: 0,
      customSchedules: [],
      custom_schedule_columns,
      custom_schedule_form: this.$form.createForm(this),
    };
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();

      const validationError = this.validateReminderSettings();
      if (validationError) {
        this.$message.error(validationError);
        return;
      }

      Promise.all([
        this.$store.dispatch('meters/updateMeter', {
          id: this.data.id,
          payload: {
            is_send_reminder_emails: this.isSendReminderEmails,
            is_push_reminder_notifications: this.isPushReminderNotifications,
            ...(this.isSendReminderEmails
              ? { reminder_emails: JSON.stringify(this.reminderEmails.map((e) => e.email)) }
              : {}),
            ...(this.isPushReminderNotifications
              ? { reminder_manager_ids: JSON.stringify(this.reminderManagerIds) }
              : {}),
          },
        }),
        this.$store.dispatch('meters/createMeterReminderRecurrence', this.data.id),
      ])
        .then((res) => {
          this.$store.dispatch('meters/loadMeter', this.data.id);
          this.$message.success('Reminder created successfully');
          this.$store.dispatch('meters/setIsShowDrawer', false);
        })
        .catch((error) => {
          this.$message.error(error.response.data.message);
        })
        .finally(() => {});
    },

    validateReminderSettings() {
      if (!this.isSendReminderEmails && !this.isPushReminderNotifications) {
        return 'Please select at least one account or email address to alert';
      }

      if (!this.reminderEmails?.length && !this.reminderManagerIds?.length) {
        return 'Please select at least one account or email address to alert';
      }

      return null;
    },

    calRepeatTimeType(type, current_type) {
      if (type == 'week_in_month') return 'month';
      if (type == 'custom_datetime') return current_type ? current_type : 'day';
      return type;
    },
    handleRepeatTimeChange(value) {
      this.repeatTimeType = value;

      if (this.repeatTimeType === 'month') {
        this.custom_recurrence['recurrence_day_of_month'] = this.startTimeMomentFormat.format('DD');

        if (this.isLastDayOfMonthValid) {
          this.custom_recurrence['recurrence_kind'] = 'month';
        } else {
          const weekOfMonth = this.getWeekOfMonth(this.startTimeMomentFormat);
          const dayInWeek = this.startTimeMomentFormat.day();

          this.custom_recurrence['recurrence_kind'] = 'week_in_month';
          this.custom_recurrence['recurrence_week_of_month'] = weekOfMonth;
          this.custom_recurrence['recurrence_day_in_week'] = dayInWeek;
        }
      } else {
        this.custom_recurrence['recurrence_kind'] = value;
      }

      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleAddCustomSchedule(e) {
      e.preventDefault();

      this.custom_schedule_form.validateFields((err, values) => {
        if (!err) {
          this.customSchedules.unshift({
            time: moment(values.time).format('DD/MM/YYYY hh:mm A'),
            id: this.customSchedules.length + 1,
          });

          this.custom_schedule_form.resetFields();
        }
      });
    },
    handleDeleteCustomSchedule(id) {
      this.customSchedules = this.customSchedules
        .filter((item) => item.id !== id)
        .map((item, index) => {
          item.id = index + 1;
          return item;
        });
    },
    handleDateTabChange(tab) {
      if (tab === 'custom_datetime') {
        this.custom_recurrence['recurrence_kind'] = 'custom_datetime';
      } else {
        this.custom_recurrence['recurrence_kind'] = tab;
      }

      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    disabledStartDate(current) {
      // Can not select days before today
      return (
        current &&
        current <
          moment()
            .endOf('day')
            .subtract(1, 'd')
      );
    },
    disabledEndDate(current) {
      // Can not select when endDate < startTimeMomentFormat
      return (
        current &&
        current <
          moment(this.startTimeMomentFormat, 'DD/MM/YYYY')
            .endOf('day')
            .subtract(1, 'd')
      );
    },
    handleStartDateChange(value) {
      this.startTime = value.format('DD/MM/YYYY HH:mm');

      this.custom_recurrence['recurrence_start_datetime'] = value.format('DD/MM/YYYY HH:mm');
      this.custom_recurrence['recurrence_day_of_month'] = value.date();

      let endOn = { ...value };
      endOn = moment(endOn)
        .add(1, 'year')
        .format('DD/MM/YYYY');
      this.custom_recurrence['recurrence_end_on'] = endOn;
      this.endOn = moment({ ...value }, 'DD/MM/YYYY').add(1, 'year');

      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
      this.defaultMonth = this.custom_recurrence['recurrence_day_of_month'];
    },
    handleRepeatEveryChange(value) {
      this.custom_recurrence['recurrence_number_repeat'] = value;
      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleSetEndDateClick(id) {
      this.radioButtonData = id;
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleModeChange(e) {
      let id = e.target.value;
      this.custom_recurrence['recurrence_never_stop'] = id === 'never_stop';
      this.custom_recurrence['recurrence_enabled_end_on'] = id === 'end_on';
      this.custom_recurrence['recurrence_enabled_number_limit'] = id === 'number_limit';

      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleReapetWeekFormChange(selectedDays) {
      this.custom_recurrence['recurrence_day_of_week'] = selectedDays.map((e) => e.id);
      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleRepeatMonthChange(data) {
      this.custom_recurrence['recurrence_kind'] = this.isLastDayOfMonthValid ? data.recurrence_kind : 'week_in_month';
      this.custom_recurrence['recurrence_day_of_month'] = data.recurrence_day_of_month;
      this.custom_recurrence['recurrence_week_of_month'] = data.recurrence_week_of_month;
      this.custom_recurrence['recurrence_day_in_week'] = data.recurrence_day_in_week;
      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleEndDateChange(value) {
      this.custom_recurrence['recurrence_end_on'] = value.format('DD/MM/YYYY');
      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    handleChangeNumberLimit(value) {
      this.custom_recurrence['recurrence_number_limit'] = value;
      this.$store.dispatch('meters/setCustomRecurrence', this.custom_recurrence);
      this.$store.dispatch('meters/getReminderSchedule');
    },
    getCurrentEndKind() {
      if (this.custom_recurrence['recurrence_never_stop']) {
        this.radioButtonData = 'never_stop';
      } else if (this.custom_recurrence['recurrence_enabled_end_on']) {
        this.radioButtonData = 'end_on';
      } else if (this.custom_recurrence['recurrence_enabled_number_limit']) {
        this.radioButtonData = 'number_limit';
      }
    },
    getKindOfMonth() {
      if (this.isLastDayOfMonthValid) {
        return this.custom_recurrence['recurrence_kind'] === 'week_in_month' ? 'week' : 'day';
      } else {
        return 'week';
      }
    },
    getWeekOfMonth(m) {
      return (
        m.week() -
        moment(m)
          .startOf('month')
          .week() +
        1
      );
    },
    onCancel() {
      this.$store.dispatch('meters/setIsShowDrawer', false);
    },
  },
  mounted() {
    this.isLoading = true;

    RSVP.all([
      this.$store.dispatch('meters/loadMeter', this.data.id),
      this.$store.dispatch('meters/loadRecurrenceSetting', { meter_id: this.data.id }),
    ])
      .then(() => {
        this.isEditing = this.recurrence.id !== null;

        if (!this.isEditing) {
          let recurrence = {
            recurrence_start_datetime: this.startTime,
            recurrence_kind: 'day',
            recurrence_number_repeat: 1,
            recurrence_day_of_week: [new Date().getDay()],
            recurrence_day_of_month: 0,
            recurrence_week_of_month: 0,
            recurrence_day_in_week: 0,
            recurrence_never_stop: false,
            recurrence_enabled_end_on: false,
            recurrence_end_on: this.endOn.format('DD/MM/YYYY'),
            recurrence_enabled_number_limit: true,
            recurrence_number_limit: 0,
          };
          this.$store.dispatch('meters/setCustomRecurrence', recurrence);
        } else {
          this.customSchedules = this.recurrence.recurrence_next_datetime.map((e, i) => {
            return { id: i, time: moment(e).format('DD/MM/YYYY hh:mm A') };
          });

          this.repeatTimeType = this.calRepeatTimeType(this.custom_recurrence['recurrence_kind'], this.repeatTimeType);
          this.startTime = this.custom_recurrence['recurrence_start_datetime'].format('DD/MM/YYYY HH:mm');
          this.endOn = this.custom_recurrence['recurrence_end_on'];
          this.numberRepeat = this.custom_recurrence['recurrence_number_repeat'];
          this.dayOfWeek = this.custom_recurrence['recurrence_day_of_week'];
          this.getCurrentEndKind();
          this.numberLimit = this.custom_recurrence['recurrence_number_limit'];
          this.defaultMonth = this.custom_recurrence['recurrence_day_of_month'];
        }

        this.$store.dispatch('meters/getReminderSchedule');
      })
      .finally(() => {
        this.isLoading = false;
      });
  },
};
</script>

<style lang="less" scoped>
.content {
  flex-basis: 30%;
}

.fc-v2-drawer-container__body {
  padding: 16px 24px;

  .fc-radio-button {
    display: flex;
    align-items: center;
    height: 55px;
  }
  .fc-radio-button-group {
    display: flex;
    flex-direction: column;
  }

  .fc-meters-reminder-custom-input-number {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    &:hover {
      border: 1px solid #ff6337;
    }
    .ant-input-number {
      border: 0;
      border-right: 1px solid #d9d9d9;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      &:hover {
        border-right: 1px solid #ff6337;
      }
    }
  }
}
</style>
