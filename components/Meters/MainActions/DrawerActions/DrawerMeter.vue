<template>
  <a-drawer
    :width="800"
    :visible="isShowDrawer"
    @close="onClose"
    :closable="false"
    id="fc-global-drawer"
    :destroyOnClose="true"
    :getContainer="'.fc-main-drawer'"
  >
    <template slot="title">
      <div class="fc-global-drawer-title">
        <span>{{ currentGutter.title }}</span>
        <div class="fc-global-drawer-group-icon">
          <a-button
            v-for="action in DRAWER_META"
            :key="action.id"
            @click="handleGutterClick(action)"
            :type="gutterButtonType(action)"
            :icon="action.icon"
            v-tooltip="{ content: action.title }"
          >
          </a-button>
          <i class="anticon btn-fc-global-drawer-close" @click="onClose"><DrawerCloseIcon /></i>
        </div>
      </div>
    </template>

    <drawer-meter-reading :data="data" v-if="currentDrawerGutter === DRAWER_META.reading.id" />
    <drawer-meter-edit :data="data" v-if="currentDrawerGutter === DRAWER_META.edit.id" />
    <drawer-reminder :data="data" v-if="currentDrawerGutter === DRAWER_META.reminder.id" />
  </a-drawer>
</template>

<script>
import { mapState } from 'vuex';
import { DrawerCloseIcon } from '~/components/Icons';
import DrawerMeterEdit from './DrawerMeterEdit.vue';
import DrawerMeterReading from './DrawerMeterReading.vue';
import DrawerReminder from './DrawerReminder.vue';

import DeleteAction from '../MeterItems/DeleteAction.vue';
const DRAWER_META = {
  reading: {
    id: 'reading',
    title: 'New Meter Reading',
    icon: 'dashboard',
  },
  edit: {
    id: 'edit',
    title: 'Edit Details',
    icon: 'edit',
  },
  reminder: {
    id: 'reminder',
    title: 'Set Reminder',
    icon: 'bell',
  },
};

export default {
  props: {
    data: {
      type: Object,
    },
  },
  components: {
    DrawerCloseIcon,
    DrawerMeterReading,
    DrawerMeterEdit,
    DrawerReminder,
    DeleteAction,
  },
  data() {
    return {
      DRAWER_META,
    };
  },
  beforeDestroy() {
    this.$store.dispatch('meters/setIsShowDrawer', false);
  },
  computed: {
    ...mapState({
      currentDrawerGutter: (state) => state.meters.currentDrawerGutter,
      isShowDrawer: (state) => state.meters.isShowDrawer,
    }),
    currentGutter() {
      console.log(this.currentDrawerGutter);
      return this.DRAWER_META[this.currentDrawerGutter];
    },
    refLink() {
      return `/features/qr_code_settings?id=${this.data.id}#new`;
    },
  },
  methods: {
    onClose() {
      this.$store.dispatch('meters/setIsShowDrawer', false);
    },
    handleGutterClick(action) {
      this.$store.dispatch('meters/setCurrentDrawerGutter', action.id);
    },
    gutterButtonType(action) {
      return this.currentDrawerGutter === action.id ? 'primary' : 'default';
    },
  },
};
</script>

<style lang="less" scoped></style>
