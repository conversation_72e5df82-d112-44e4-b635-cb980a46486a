<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center; padding: 13px 14px">
      <div class="fc-group__left">
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button v-if="isShowExportBtn" type="primary" class="fc-group-button yellow" @click="onClickExportCSV">
          <a-icon :component="ExportIcon" />
          Export CSV
        </a-button>
        <fc-add-to-dashboard :type="'request_location_tag_table'" v-if="!shownInDashboard" />
      </div>
    </div>
    <div>
      <!-- <p class="text-title">Location Tag Stats</p> -->
      <a-table
        :columns="columns"
        :rowKey="(record) => record.key || record.request_location"
        :dataSource="tableData"
        :loading="loading"
        v-bind="scrollYProperty"
        @change="handleTableChange"
      >
        <template slot="percentage" slot-scope="text">
          {{ `${text}%` }}
        </template>
        <template slot="request_number" slot-scope="text, record">
          <a
            :href="`/features/requests?request_ids=[${record.request_ids}]`"
            target="_blank"
            rel="noopener nofollow noreferrer"
          >
            {{ text }}
          </a>
        </template>
        <template slot="request_location" slot-scope="text, record">
          <span>
            <a-badge class="fc-badge" :color="record.colored_legend" />
          </span>
          <span>
            {{ text }}
          </span>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script>
const columns = [
  {
    title: 'Location Tag',
    dataIndex: 'request_location',
    scopedSlots: { customRender: 'request_location' },
    sorter: (a, b) => a.request_location.toLowerCase().localeCompare(b.request_location.toLowerCase()),
    width: '60%',
  },
  {
    title: 'No. of Requests',
    dataIndex: 'request_number',
    scopedSlots: { customRender: 'request_number' },
    sorter: (a, b) => a.request_number - b.request_number,
    width: '20%',
  },
  {
    title: 'Percentage of Total',
    dataIndex: 'percentage',
    scopedSlots: { customRender: 'percentage' },
    sorter: (a, b) => a.percentage - b.percentage,
    width: '20%',
  },
];
import { generateAndDownloadCsv } from '@/utils/helper';
import { ExportIcon } from '@/components/Icons';
import { mapState } from 'vuex';
import { generateBgColor } from '@/utils/chart_config';
import _ from 'lodash';

export default {
  props: {
    data: {
      type: Object,
    },
    selectedDateRange: {
      type: Array,
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: {
      type: Boolean,
      default: false,
    },
    yScrollHeight: undefined,
  },
  data() {
    return {
      ExportIcon,
      columns,
      dataSource: [],
      loading: false,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      isExportCsvShowLocationSubTag: false,
    };
  },
  methods: {
    handleExportCsvOptionChange(e) {
      this.isExportCsvShowLocationSubTag = e.target.value;
    },
    onClickExportCSV() {
      let _this = this;
      this.$confirm({
        title: 'Export CSV Options',
        content: () => (
          <div>
            <p>Please select how you want to display the Location Tags in the CSV Export</p>
            <a-radio-group v-model={_this.isExportCsvShowLocationSubTag} onChange={_this.handleExportCsvOptionChange}>
              <a-radio value={false}>Hide Location Sub Tags in CSV</a-radio>
              <a-radio value={true}>Show Location Sub Tags in CSV</a-radio>
            </a-radio-group>
          </div>
        ),
        onOk() {
          const sortedData = _this.getSortedTableData();
          const exportData = _this.isExportCsvShowLocationSubTag
            ? _this.prepareDataWithLocationSubTags(sortedData)
            : _this.prepareDataWithoutLocationSubTags(sortedData);
          const headers = _this.isExportCsvShowLocationSubTag
            ? {
                request_location: 'Location Tag',
                location_sub_tag: 'Location Sub Tag',
                location_sub_sub_tag: 'Location Sub Sub Tag',
                request_number: 'No. of Requests',
                percentage: 'Percentage of Total',
              }
            : {
                request_location: 'Location Tag',
                request_number: 'No. of Requests',
                percentage: 'Percentage of Total',
              };
          const filename = _this.isExportCsvShowLocationSubTag
            ? 'request_breakdown_request_location_tags_sub_tags'
            : 'request_breakdown_request_location_tags';

          generateAndDownloadCsv(exportData, filename, headers);
        },
        onCancel() {},
      });
    },

    getSortedTableData() {
      if (!this.tableData || !this.tableData.length) {
        return [
          {
            request_location: null,
            location_sub_tag: null,
            location_sub_sub_tag: null,
            request_number: null,
            percentage: null,
          },
        ];
      }

      return [...this.tableData].sort((a, b) => {
        if (!this.sorter.data_sort) return 0;

        const field = this.sorter.data_sort;
        const order = this.sorter.order_sort;

        if (field === 'request_location') {
          return order === 'asc'
            ? a[field].toLowerCase().localeCompare(b[field].toLowerCase())
            : b[field].toLowerCase().localeCompare(a[field].toLowerCase());
        }

        return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
      });
    },

    prepareDataWithoutLocationSubTags(data) {
      return data.map((locationTag) => ({
        request_location: `"${locationTag.request_location}"`,
        request_number: locationTag.request_number,
        percentage: `${locationTag.percentage}%`,
      }));
    },

    prepareDataWithLocationSubTags(data) {
      const result = [];
      data.forEach((locationTag) => {
        // If the locationTag has children, return subTag rows instead
        if (locationTag?.children?.length) {
          locationTag.children.forEach((subTag) => {
            const locationSubTagRow = this.getSubTagRow(locationTag, subTag);
            result.push(...locationSubTagRow);
          });
        } else {
          result.push({
            request_location: `"${locationTag.request_location}"`,
            location_sub_tag: '',
            location_sub_sub_tag: '',
            request_number: locationTag.request_number,
            percentage: `${locationTag.percentage}%`,
          });
        }
      });
      return result;
    },
    getSubTagRow(locationTag, subTag) {
      // If the subTag has children, return subSubTag rows instead
      if (subTag.children?.length) {
        return subTag.children.map((subSubTag) => ({
          request_location: `"${locationTag.request_location}"`,
          location_sub_tag: `"${subTag.request_location}"`,
          location_sub_sub_tag: `"${subSubTag.request_location}"`,
          request_number: subSubTag.request_number,
          percentage: `${subSubTag.percentage}%`,
        }));
      }

      return [
        {
          request_location: `"${locationTag.request_location}"`,
          location_sub_tag: `"${subTag.request_location}"`,
          location_sub_sub_tag: '',
          request_number: subTag.request_number,
          percentage: `${subTag.percentage}%`,
        },
      ];
    },

    handleTableChange(pagination, filters, sorter) {
      if (!sorter.order) {
        this.sorter.data_sort = undefined;
        this.sorter.order_sort = undefined;
      } else {
        this.sorter.data_sort = sorter.field;
        this.sorter.order_sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      }
    },

    handleInputSearchSubmit(value) {
      this.q = value.toLowerCase();
    },

    formatTreeData(data) {
      if (!data) return [];

      return data.map((item) => {
        return {
          ...(item.hasOwnProperty('colored_legend') ? { colored_legend: item.colored_legend } : {}),
          key: item.id,
          request_location: item.name,
          request_number: item.total_count,
          request_ids: item.request_ids,
          children: !_.isEmpty(item.children) ? this.formatTreeData(item.children) : null,
          percentage: ((item.total_count / this.data.chart_data.total_location_tag_request_count) * 100).toFixed(2),
        };
      });
    },
  },
  computed: {
    ...mapState({
      requestBreakdownFilter: (state) => state.request_statistics.breakDownFilter,
    }),
    requestData() {
      let request_location_tag = _.cloneDeep(this.data.chart_data.request_location_tag);
      if (!this.data.chart_data || !request_location_tag.length) return [];
      const coloredLegend = generateBgColor(this.data.chart_data.request_location_tag.length);
      request_location_tag = request_location_tag.map((item, index) => {
        return { ...item, colored_legend: coloredLegend[index] };
      });

      return this.formatTreeData(request_location_tag);
    },
    tableData() {
      return this.q.length
        ? this.requestData.filter(
            (r) =>
              r.request_location.toLowerCase().search(this.q) > -1 ||
              r.request_number.toString().search(this.q) > -1 ||
              r.percentage.toString().search(this.q) > -1,
          )
        : this.requestData;
    },
    scrollYProperty() {
      if (this.yScrollHeight) {
        return { scroll: { y: this.yScrollHeight } };
      }
    },
  },
};
</script>
