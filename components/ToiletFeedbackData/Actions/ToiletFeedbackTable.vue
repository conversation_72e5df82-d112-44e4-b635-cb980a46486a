<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center; padding: 13px 14px">
      <div class="fc-group__left">
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button
          v-if="isShowExportBtn"
          type="primary"
          class="fc-group-button yellow"
          @click="onClickExportCSV"
          :loading="loading"
        >
          <a-icon :component="ExportIcon" />
          Export CSV
        </a-button>
        <fc-add-to-dashboard :type="'toilet_feedback_ratings_table'" v-if="!shownInDashboard" />
      </div>
    </div>
    <div>
      <a-table
        :columns="columns"
        rowKey="location_id"
        :dataSource="tableData"
        v-bind="scrollYProperty"
        @change="handleTableChange"
      >
        <span slot="location" slot-scope="text, record">
          <a target="_blank" rel="nofollow noopener" :href="locationQrCodeLink(record.location_id)">{{ text }}</a>
        </span>
        <span slot="count_excellent" slot-scope="text, record">
          {{ columnsData(text, record.percent_excellent) }}
        </span>
        <span slot="count_good" slot-scope="text, record">
          {{ columnsData(text, record.percent_good) }}
        </span>
        <span slot="count_poor" slot-scope="text, record">
          {{ columnsData(text, record.percent_poor) }}
        </span>
        <span slot="count_very_poor" slot-scope="text, record">
          {{ columnsData(text, record.percent_very_poor) }}
        </span>
      </a-table>
    </div>
  </div>
</template>

<script>
import { ExportIcon } from '@/components/Icons';
import FcSearch from '@/components/Shared/FcSearch.vue';
import { generateAndDownloadCsv } from '@/utils/helper';

const columns = [
  {
    title: 'Location',
    dataIndex: 'location_name',
    sorter: (a, b) => a.location_name.toLowerCase().localeCompare(b.location_name.toLowerCase()),
    scopedSlots: { customRender: 'location' },
  },
  {
    title: 'Excellent',
    dataIndex: 'count_excellent',
    scopedSlots: { customRender: 'count_excellent' },
    sorter: (a, b) => a.count_excellent - b.count_excellent,
  },
  {
    title: 'Good',
    dataIndex: 'count_good',
    scopedSlots: { customRender: 'count_good' },
    sorter: (a, b) => a.count_good - b.count_good,
  },
  {
    title: 'Poor',
    dataIndex: 'count_poor',
    scopedSlots: { customRender: 'count_poor' },
    sorter: (a, b) => a.count_poor - b.count_poor,
  },
  {
    title: 'Very Poor',
    dataIndex: 'count_very_poor',
    scopedSlots: { customRender: 'count_very_poor' },
    sorter: (a, b) => a.count_very_poor - b.count_very_poor,
  },
];
export default {
  components: {
    FcSearch,
  },
  props: {
    data: {
      type: Object,
    },
    selectedDateRange: {
      type: Array,
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: {
      type: Boolean,
      default: false,
    },
    yScrollHeight: undefined,
  },
  data() {
    return {
      columns,
      ExportIcon,
      dataSource: [],
      loading: false,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      isExportCsvShowLocationSubTag: false,
    };
  },
  methods: {
    columnsData(count, percentage) {
      return `${count} (${Math.round(percentage * 100)}%)`;
    },
    locationQrCodeLink(id) {
      return `/features/qr_code_settings?location_ids=[${id}]`;
    },
    handleInputSearchSubmit(value) {
      this.q = value.toLowerCase();
    },
    handleTableChange(pagination, filters, sorter) {
      if (!sorter.order) {
        this.sorter.data_sort = undefined;
        this.sorter.order_sort = undefined;
      } else {
        this.sorter.data_sort = sorter.field;
        this.sorter.order_sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      }
    },
    getSortedTableData() {
      if (!this.tableData || !this.tableData.length) {
        return [
          {
            location_name: null,
            count_excellent: null,
            count_good: null,
            count_poor: null,
            count_very_poor: null,
          },
        ];
      }

      return [...this.tableData].sort((a, b) => {
        if (!this.sorter.data_sort) return 0;

        const field = this.sorter.data_sort;
        const order = this.sorter.order_sort;

        if (field === 'location_name') {
          return order === 'asc'
            ? a[field].toLowerCase().localeCompare(b[field].toLowerCase())
            : b[field].toLowerCase().localeCompare(a[field].toLowerCase());
        }

        return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
      });
    },

    handleExportCsvOptionChange(e) {
      this.isExportCsvShowLocationSubTag = e.target.value;
    },
    onClickExportCSV() {
      let _this = this;
      this.$confirm({
        title: 'Export CSV Options',
        content: () => (
          <div>
            <p>Please select how you want to display the Location Tags in the CSV Export</p>
            <a-radio-group v-model={_this.isExportCsvShowLocationSubTag} onChange={_this.handleExportCsvOptionChange}>
              <a-radio value={false}>Hide Location Sub Tags in CSV</a-radio>
              <a-radio value={true}>Show Location Sub Tags in CSV</a-radio>
            </a-radio-group>
          </div>
        ),
        onOk() {
          const sortedData = _this.getSortedTableData();
          const exportData = _this.isExportCsvShowLocationSubTag
            ? _this.prepareDataWithLocationSubTags(sortedData)
            : _this.prepareDataWithoutLocationSubTags(sortedData);
          const headers = _this.isExportCsvShowLocationSubTag
            ? {
                location_name: 'Location',
                location_sub_tag: 'Location Sub Tag',
                location_sub_sub_tag: 'Location Sub Sub Tag',
                count_excellent: 'Excellent',
                count_good: 'Good',
                count_poor: 'Poor',
                count_very_poor: 'Very Poor',
              }
            : {
                location_name: 'Location',
                count_excellent: 'Excellent',
                count_good: 'Good',
                count_poor: 'Poor',
                count_very_poor: 'Very Poor',
              };
          const filename = _this.isExportCsvShowLocationSubTag
            ? 'toilet_feedback_data_location_tags_sub_tags'
            : 'toilet_feedback_data_location_tags';

          generateAndDownloadCsv(exportData, filename, headers);
        },
        onCancel() {},
      });
    },
    prepareDataWithoutLocationSubTags(data) {
      return data.map((locationTag) => ({
        location_name: `"${locationTag.location_name}"`,
        count_excellent: this.columnsData(locationTag.count_excellent, locationTag.percent_excellent),
        count_good: this.columnsData(locationTag.count_good, locationTag.percent_good),
        count_poor: this.columnsData(locationTag.count_poor, locationTag.percent_poor),
        count_very_poor: this.columnsData(locationTag.count_very_poor, locationTag.percent_very_poor),
      }));
    },

    prepareDataWithLocationSubTags(data) {
      const result = [];
      data.forEach((locationTag) => {
        // If the locationTag has children, return subTag rows instead
        if (locationTag?.children?.length) {
          locationTag.children.forEach((subTag) => {
            const locationSubTagRow = this.getSubTagRow(locationTag, subTag);
            result.push(...locationSubTagRow);
          });
        } else {
          result.push({
            location_name: `"${locationTag.location_name}"`,
            location_sub_tag: '',
            location_sub_sub_tag: '',
            count_excellent: this.columnsData(locationTag.count_excellent, locationTag.percent_excellent),
            count_good: this.columnsData(locationTag.count_good, locationTag.percent_good),
            count_poor: this.columnsData(locationTag.count_poor, locationTag.percent_poor),
            count_very_poor: this.columnsData(locationTag.count_very_poor, locationTag.percent_very_poor),
          });
        }
      });
      return result;
    },
    getSubTagRow(locationTag, subTag) {
      // If the subTag has children, return subSubTag rows instead
      if (subTag.children?.length) {
        return subTag.children.map((subSubTag) => ({
          location_name: `"${subSubTag.location_name}"`,
          location_sub_tag: `"${subTag.location_name}"`,
          location_sub_sub_tag: `"${subSubTag.location_name}"`,
          count_excellent: this.columnsData(subTag.count_excellent, subTag.percent_excellent),
          count_good: this.columnsData(subTag.count_good, subTag.percent_good),
          count_poor: this.columnsData(subTag.count_poor, subTag.percent_poor),
          count_very_poor: this.columnsData(subTag.count_very_poor, subTag.percent_very_poor),
        }));
      }

      return [
        {
          location_name: `"${locationTag.location_name}"`,
          location_sub_tag: `"${subTag.location_name}"`,
          location_sub_sub_tag: '',
          count_excellent: this.columnsData(subTag.count_excellent, subTag.percent_excellent),
          count_good: this.columnsData(subTag.count_good, subTag.percent_good),
          count_poor: this.columnsData(subTag.count_poor, subTag.percent_poor),
          count_very_poor: this.columnsData(subTag.count_very_poor, subTag.percent_very_poor),
        },
      ];
    },
  },
  computed: {
    scrollYProperty() {
      if (this.yScrollHeight) {
        return { scroll: { y: this.yScrollHeight } };
      }
    },
    tableData() {
      return this.q.length
        ? this.data.table_results.filter((r) => r.location_name.toLowerCase().search(this.q) > -1)
        : this.data.table_results;
    },
  },
};
</script>
