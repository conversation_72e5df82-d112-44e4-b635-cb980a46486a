<template>
  <div class="fc-v2-drawer-container">
    <div class="fc-v2-drawer-container__body">
      <div class="fc-global-drawer__header">
        <div class="fc-drawer-checlist__header">
          {{ data.description }}
        </div>
        <i>Asset ID: {{ data.asset_id }}</i>
      </div>
      <a-tabs :active-key="defaultActiveKey" @change="handleTabChange">
        <a-tab-pane key="checklists" tab="Checklists">
          <table-preventive-maintenance-history :data="data" />
        </a-tab-pane>
        <a-tab-pane key="requests" tab="Requests">
          <table-fault-report-history :data="data" />
        </a-tab-pane>
        <a-tab-pane key="expenditures" tab="Expenditures">
          <table-expenditures-history :data="data" />
        </a-tab-pane>
        <a-tab-pane key="meter_readings" tab="Meter Readings">
          <table-meters-history :data="data" />
        </a-tab-pane>
        <a-tab-pane key="parts" tab="Parts">
          <table-parts-history :data="data" />
        </a-tab-pane>
        <a-tab-pane key="downtime" tab="Downtime">
          <table-downtime-history :data="data" />
        </a-tab-pane>
        <a-tab-pane key="insights" v-if="enable_generative_ai">
          <template #tab>
            <span>
              Insights
              <a-tag color="orange">Beta</a-tag>
            </span>
          </template>
          <insights :linkedChecklists="linkedChecklists" />
        </a-tab-pane>
        <a-tab-pane key="qr_code_scans" tab="QR Code Scans">
          <table-qr-code-history :data="data" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import AssetTrackingForm from '../Forms/AssetTrackingForm.vue';
import TableFaultReportHistory from '../Tables/TableFaultReportHistory.vue';
import TablePreventiveMaintenanceHistory from '../Tables/TablePreventiveMaintenanceHistory.vue';
import TableQrCodeHistory from '../Tables/TableQrCodeHistory.vue';
import TableMetersHistory from '../Tables/TableMetersHistory.vue';
import TablePartsHistory from '../Tables/TablePartsHistory.vue';
import TableExpendituresHistory from '../Tables/TableExpendituresHistory.vue';
import TableDowntimeHistory from '../Tables/TableDowntimeHistory.vue';
import Insights from '../Insights/Insights.vue';
import { processChecklistArray } from '@/utils/helper';

export default {
  components: {
    AssetTrackingForm,
    TablePreventiveMaintenanceHistory,
    TableFaultReportHistory,
    TableQrCodeHistory,
    TableMetersHistory,
    TablePartsHistory,
    TableExpendituresHistory,
    TableDowntimeHistory,
    Insights,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      isCompleteLoading: false,
      isDataLoading: false,
      defaultActiveKey: 'checklists',
      linkedChecklists: [],
    };
  },
  mounted() {
    if (this.$route.query.history_tab) {
      this.defaultActiveKey = this.$route.query.history_tab;
    }
    this.asyncData();
  },
  methods: {
    asyncData() {
      this.isDataLoading = true;
      this.$store
        .dispatch('assets/loadAssetTracking', this.data.id)
        .then((res) => {
          const assetId = res.asset_id;
          this.$store
            .dispatch('assets/loadLinkedChecklists', assetId)
            .then((res) => {
              this.linkedChecklists = processChecklistArray(res);
              this.isDataLoading = false;
            })
            .catch(() => {
              this.isDataLoading = false;
            });
          this.isDataLoading = false;
        })
        .catch(() => {
          this.isDataLoading = false;
        });
    },
    handleTabChange(e) {
      this.defaultActiveKey = e;
    },
  },
  computed: {
    ...mapState({
      assetTracking: (state) => state.assets.asset_tracking,
      enable_generative_ai: (state) => state.managers.profile?.agent_management?.enable_generative_ai,
    }),
  },
};
</script>

<style lang="less">
.fc-asset-note {
  background: #f9f9fa;
  border: 1px solid #dbdde0;
  border-radius: 8px;
  padding: 15px 21px;
  margin-bottom: 8px;
}
</style>
