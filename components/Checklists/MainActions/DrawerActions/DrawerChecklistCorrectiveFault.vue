<template>
  <div>
    <a-form :form="form" class="fc-v2-drawer-container" @submit="handleSubmit">
      <div class="fc-v2-drawer-container__body">
        <checklist-detail-header :data="data" />

        <div class="fc-checklist-container">
          <a-form-item class="new-request-card" label="Request Type" :required="false">
            <i style="display: flex; float: right"><required-icon /></i>

            <fc-select-with-link
              :options="combineAllRequestTypes"
              v-on:handleSelect="handleRequestTypeChange"
              placeholder="Select Request Type"
              :vDecorator="[
                'request_type_id',
                {
                  rules: [{ required: true, message: 'Please select request type' }],
                  initialValue: initialRequestTypeId,
                },
              ]"
              :isLoadMore="isAllowCustomFaults"
              dispatchTrigger="request-types/loadMoreRequestTypes"
              :pageTrigger="page"
              type="REQUEST_TYPES"
              :disabled="!!paymentModalData.request_type_id"
            />
          </a-form-item>

          <request-form-items :chosenRequestType="chosenRequestType" :key="chosenRequestType.id" :form="form" />

          <template v-if="$auth.user.role !== 'requestor'">
            <div class="new-request-card">
              <fc-user-picker-form-items :guestId="0" />
            </div>
          </template>
        </div>
      </div>

      <div class="fc-global-drawer__footer fc-v2-drawer-footer">
        <div class="fc-global-drawer__footer--group-buttons flex-end">
          <a-button size="large" type="primary" html-type="submit" :loading="isCompleteLoading">Submit</a-button>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script>
import RSVP from 'rsvp';
import { mapState } from 'vuex';

import FcStatusTag from '../../../Shared/FcStatusTag.vue';
import FcUploadFiles from '../../../Shared/FcUploadFiles.vue';
import FcbSelectV3 from '../../../Shared/FcbSelectV3.vue';
import FcLinkToManagement from '../../../Shared/FcLinkToManagement.vue';
import FcUserPickerFormItems from '../../../Shared/FcUserPickerFormItems.vue';

import ChecklistDetailHeader from '../Actions/ChecklistDetailHeader.vue';
import RequestFaultReport from '../../../Requests/MainActions/RequestFaultReport.vue';

import { getBase64 } from '../../../../utils/helper';
import { REQUEST_TYPES } from '../../../../utils/consts';

export default {
  components: {
    FcStatusTag,
    FcUploadFiles,
    FcbSelectV3,
    FcLinkToManagement,
    FcUserPickerFormItems,
    ChecklistDetailHeader,
    RequestFaultReport,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      isCompleteLoading: false,
      selectedRequestorId: 0,
      faultSubType: [],
      chosenRequestType: {},
      page: 2,
    };
  },
  methods: {
    setFaultSubType(id) {
      this.faultSubType = this.faultTypes.find((fault) => fault.id === id)?.fault_sub_type_tags || [];
    },

    handleSubmit(e) {
      e.preventDefault();
      this.isCompleteLoading = true;
      this.form.validateFields(async (err, values) => {
        if (!err) {
          let formData = new FormData();
          const fault_images = values.image ? values.image : [];
          const requestType = JSON.parse(JSON.stringify(this.chosenRequestType));
          const kind = requestType?.kind;

          formData.append('kind', kind);
          formData.append('guest_id', values.guest_id == 0 ? 'manual_request' : values.guest_id);
          formData.append('full_name_manual_user', values.full_name_manually);

          switch (kind) {
            case 'report_fault':
              formData.append('facility_id', values.fault_type_id || '');
              formData.append('fault_sub_type_tag_id', values.fault_sub_type_tag_id || '');
              formData.append('content', values.description || '');
              formData.append('location_fault', values.location || '');
              formData.append('fault_location_ids', JSON.stringify(values.fault_location_ids));

              await this.uploadImage(fault_images).then(() => {
                formData.append('fault_image_ids', JSON.stringify(this.reportFaultImages));
              });
              break;

            case 'talk_to_agent':
              formData.append('content', values.description || '');
              break;

            default:
              formData.append('request_type_id', requestType.id);
              const pure_data = requestType.meta_data.blocks;
              let custom_content = values.custom_content;
              await this.combineBase64(pure_data, custom_content).then((res) => {
                let shouldExit = false;
                pure_data.forEach((data, index) => {
                  let custom_data = custom_content?.[this.chosenRequestType.id + '_' + index];

                  if (shouldExit) {
                    return;
                  }

                  if (_.isNil(custom_data)) {
                    if (data.type === 'payment') {
                      if (!this.paidStatus) {
                        this.$message.error('Please paid first before submit');
                        shouldExit = true;
                        return;
                      }

                      data['options']['email'] = this.emailPayment;
                      data['post_content'] = JSON.stringify({
                        payment_id: this.paymentId,
                      });

                      formData.append('payment_type_id', data.options.payment_type);
                      formData.append('email_payment', this.emailPayment);
                      if (this.paymentIntent) formData.append('payment_intent_id', this.paymentIntent);
                    }
                    return;
                  }

                  switch (data.type) {
                    case 'order_items':
                      data['post_content'] = JSON.stringify(this.currentOrderItems);
                      break;
                    case 'image':
                    case 'video':
                    case 'excel':
                    case 'pdf':
                      data['post_content'] = res[index];
                      break;
                    case 'multiple_images_videos':
                      data['type'] = 'media';
                      data['post_content'] = JSON.stringify(res[index]);
                      break;
                    case 'select_tags':
                      data['post_content'] = JSON.stringify(custom_data.map((e) => JSON.parse(e).id));
                      break;
                    case 'checkbox':
                      data['post_content'] = JSON.stringify(
                        custom_data.map((e) => {
                          let data = JSON.parse(e);
                          data.value = true;
                          return data;
                        }),
                      );
                      break;
                    case 'radio':
                      let data_clone = data.content;
                      data_clone.map((e, i) => {
                        e.value = i === custom_data;
                      });
                      data['post_content'] = JSON.stringify(data_clone);
                      break;
                    case 'datetime':
                      data['post_content'] = custom_data.format('DD/MM/YYYY hh:mm A');
                      break;
                    case 'single_choice_dropdown':
                    case 'multiple_choice_dropdown':
                      if (data.type == 'single_choice_dropdown') custom_data = [custom_data];
                      data['post_content'] = JSON.stringify(
                        data.content.map((item, index) => {
                          return custom_data.includes(index) ? { ...item, ...{ value: true } } : item;
                        }),
                      );
                      break;
                    default:
                      data['post_content'] =
                        typeof custom_data === 'string' ? custom_data : JSON.stringify(custom_data);
                  }
                });

                if (shouldExit) {
                  this.isSubmitLoading = false;
                  return;
                }
                formData.append('custom_content', JSON.stringify({ blocks: pure_data }));
              });
              break;
          }

          this.submitData(formData);
        } else {
          this.isCompleteLoading = false;
        }
      });
    },

    async processBase64(data, index, custom_content) {
      const mediaType = ['image', 'video', 'excel', 'pdf'];
      let custom_data = custom_content?.[this.chosenRequestType.id + '_' + index];
      if (!custom_data) return;
      if (mediaType.includes(data.type)) {
        if (!custom_data[0]) return;
        return await getBase64(custom_data[0].originFileObj);
      }
    },

    async combineBase64(pure_data, custom_content) {
      let response = await Promise.all(
        pure_data.map(async (data, index) => {
          let custom_data = custom_content?.[this.chosenRequestType.id + '_' + index];

          if (custom_data === undefined || custom_data == null) return;
          if (data.type === 'multiple_images_videos') {
            const mediaIds = await this.processMultipleMedias(custom_data);
            return mediaIds;
          }
          if (data.type === 'image' || data.type === 'video' || data.type === 'excel' || data.type === 'pdf') {
            if (custom_data[0] === undefined) return;
            return getBase64(custom_data[0].originFileObj);
          }
        }),
      );

      return response;
    },

    async processMultipleMedias(mediaFiles) {
      const uploadPromises = mediaFiles.map((file) => {
        let formDataImage = new FormData();
        formDataImage.append('upload', file.originFileObj);
        return this.$store
          .dispatch('internal_tools/uploadAttachment', formDataImage)
          .then((res) => ({ success: true, id: res.id }))
          .catch(() => ({ success: false, error: 'Error uploading media' }));
      });

      const results = await Promise.all(uploadPromises);
      const mediaIds = results.filter((result) => result.success).map((result) => result.id);

      results.forEach((result) => {
        if (!result.success) {
          this.$message.error(result.error);
        }
      });

      return mediaIds;
    },

    submitData(formData) {
      this.$store
        .dispatch('checklists/createCorrectiveFault', {
          id: this.data.id,
          payload: formData,
        })
        .then(() => {
          this.$message.success('Create Corrective Fault success');
          this.$store.dispatch(this.setDrawerDispatchStr, false);
          this.$store.dispatch('requests/setReportFaultImages');
          this.isCompleteLoading = false;
          this.reloadChecklists();
        })
        .catch((error) => {
          this.$store.dispatch('requests/setReportFaultImages');
          this.$message.error(error.response.data.message);
          this.isCompleteLoading = false;
        });
    },

    reloadChecklists() {
      this.$store.dispatch('checklists/loadChecklist', this.data.id).then((response) => {
        const _checklists = this.sourceChecklists.map((c) => {
          if (c.id == this.data.id) {
            return { ...c, link_request_ids: response.link_request_ids, link_requests: response.link_requests };
          } else {
            return c;
          }
        });
        this.$store.dispatch(this.setChecklistDispatchStr, _checklists);
      });
    },

    uploadImage(images) {
      return Promise.all(
        images.map((image) => {
          if (image.id) {
            return this.$store.dispatch('requests/pushReportFaultImages', image.id);
          } else if (image.status === 'done') {
            const formData = new FormData();
            formData.append('file', image.originFileObj);
            return this.$store.dispatch('requests/uploadFaultImages', formData);
          }
        }),
      );
    },

    async fetchRequestTypes() {
      await RSVP.all([
        this.$store.dispatch('agents/loadFeatureSettings'),
        this.$store.dispatch('request-types/loadAllRequestTypes'),
      ]);

      const { combineAllRequestTypes, paymentModalData } = this;
      if (combineAllRequestTypes.length) {
        if (paymentModalData.request_type_id) {
          this.chosenRequestType =
            combineAllRequestTypes.find(({ id }) => id == paymentModalData.request_type_id) || {};
        } else {
          this.chosenRequestType = combineAllRequestTypes[0];
        }
      } else {
        this.chosenRequestType = {};
      }
    },

    handleRequestTypeChange(value) {
      this.chosenRequestType = this.combineAllRequestTypes.find((r) => r.id === value);
      this.$store.dispatch('payments/setShowSubmitFormRequest', true);
    },
  },
  mounted() {
    this.$store.dispatch('payments/setShowSubmitFormRequest', true);
    this.fetchRequestTypes();
  },
  computed: {
    ...mapState({
      faultLocations: (state) => state.v2.locations.locations,
      faultTypes: (state) => state.v2.fault_types.fault_types,
      guests: (state) => state.guests.guests,
      checklists: (state) => state.checklists.checklists,
      reportFaultImages: (state) => state['requests'].reportFaultImages,
      requestTypes: (state) => state['request-types'].requestTypes,

      feature_settings: (state) => state.agents.feature_settings,
      paymentModalData: (state) => state.payments.paymentModalData,
      checklistsSignOff: (state) => state.checklists.checklists_sign_off,
      currentOrderItems: (state) => state['request-types'].currentOrderItems,
      paidStatus: (state) => state.payments.paidStatus,
      emailPayment: (state) => state.payments.emailPayment,
      paymentId: (state) => state.payments.paymentId,
      paymentModalData: (state) => state.payments.paymentModalData,
    }),

    isAllowCustomFaults() {
      return this.feature_settings.custom_fault_feature?.enable_custom_fault === true;
    },
    combineAllRequestTypes() {
      if (this.isAllowCustomFaults) {
        return [...this.getRequestStaticData, ...this.requestTypes];
      } else {
        return this.getRequestStaticData;
      }
    },

    getRequestStaticData() {
      let staticData = [];

      const reportFaultEnabled = this.feature_settings.fault_reporting_feature?.enable_report_fault;
      const talkToAgentEnabled = this.feature_settings.chat_with_staff_feature?.enable_chat_with_staff;

      if (reportFaultEnabled) {
        staticData.push(REQUEST_TYPES.report_fault);
      }
      if (talkToAgentEnabled) {
        staticData.push(REQUEST_TYPES.talk_to_agent);
      }

      return staticData;
    },

    initialRequestTypeId() {
      if (this.paymentModalData.request_type_id) {
        return this.paymentModalData.request_type_id;
      } else if (this.combineAllRequestTypes.length) {
        return this.combineAllRequestTypes[0].id;
      } else {
        return null;
      }
    },

    isTabReview() {
      return this.$route.query?.tab === 'reviewing';
    },

    sourceChecklists() {
      return this.isTabReview ? this.checklistsSignOff : this.checklists;
    },

    setChecklistDispatchStr() {
      return this.isTabReview ? 'checklists/setChecklistsSignOff' : 'checklists/setChecklists';
    },

    setDrawerDispatchStr() {
      return this.isTabReview
        ? 'checklists/setIsShowDrawerChecklistReviewing'
        : 'checklists/setIsShowDrawerChecklistPending';
    },
  },
};
</script>

<style lang="less" scoped></style>
