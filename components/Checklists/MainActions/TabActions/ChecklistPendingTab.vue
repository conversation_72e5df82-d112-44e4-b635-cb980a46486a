<template>
  <div>
    <div class="fc-group-on-top" ref="fcGroupOnTop">
      <div class="fc-group__left">
        <fc-select-entries
          v-on:onShowSizeChange="handleSelectEntriesChange"
          :currentSize="pendingChecklistFilter && pendingChecklistFilter.per_page"
        />
        <fc-customize-columns-table :dataSource="columns" @onColumnsChange="handleColumnsChange" />
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" :initSearchText="initSearchText" />
        <fc-clear-filter :filteredKey="pendingChecklistFilterKey" @onClearFilter="onClearFilter"></fc-clear-filter>
      </div>
      <div class="fc-group__right">
        <export-csv :start_date="getStartDate" :end_date="getEndDate" :q="initSearchText" />
        <nuxt-link to="/features/checklists/upcoming_calendar">
          <a-button type="primary" icon="calendar" class="fc-group-button blue">
            Calendar
          </a-button>
        </nuxt-link>
        <nuxt-link to="/features/checklists/upcoming_checklists">
          <a-button type="primary" icon="check" class="fc-group-button cyan">
            Upcoming
          </a-button>
        </nuxt-link>
      </div>
    </div>
    <div class="checklist-tip fc-page-tip" ref="fcPageTip">
      <div>
        <a-icon :component="YellowHintIcon"></a-icon>
        <span>
          All checklists which are pending for completion will be listed here.
        </span>
        <a-button type="primary" class="fc-group-button blue" style="margin-top: 3px">
          <a
            href="https://blog.facilitybot.co/blog/knowledge-base/manager-web-portal/checklists/"
            target="_blank"
            rel="noopener nofollow noreferrer"
          >
            Help
          </a>
        </a-button>
      </div>
      <div>
        <bulk-delete-checklists
          :selectedChecklistIds="selectedChecklistIds"
          v-on:fetchAfterDeleted="fetchAfterDeleted"
        />
      </div>
    </div>
    <div>
      <fc-table-v2
        v-if="dynamicColumns.length"
        :columns="dynamicColumns"
        :dataSource="checklists"
        v-on:onTableChange="onTableChange"
        v-on:onSelectedRowChange="onSelectedRowChange"
        :loading="loading"
        :pagination="pageInformation"
        :tableHeight="currentDynamicHeight"
        ref="tableExpenditures"
        :row-selection="{
          selectedRowKeys: selectedChecklistIds,
          onChange: onSelectChange,
        }"
        :rememberFilter="pendingChecklistFilter"
      >
        <template slot="checklist_id_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <fc-filter-labels-v2
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
            }"
            :labelType="LABEL_TYPES.checklist"
            ref="fcFilterLabels"
          />
        </template>
        <template slot="created_at_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <a-range-picker
            class="range-filter"
            :format="dateFormat"
            v-model="createdAtDateRange"
            @change="(e) => handleRangePickerChange(e, 'created_at')"
            allowClear
          />
        </template>
        <template slot="updated_at_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <a-range-picker
            class="range-filter"
            :format="dateFormat"
            v-model="updatedAtDateRange"
            @change="(e) => handleRangePickerChange(e, 'updated_at')"
            allowClear
          />
        </template>
        <template
          slot="linked_assets_filter"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <fc-filter-checklist-linked-asset
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
              column,
            }"
            ref="fcFilterLinkedAssets"
            :searchable="true"
            :multipleChoice="true"
          />
        </template>
        <template
          slot="linked_location_tags_filter"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <fc-filter-location-tags-v2
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
              column,
            }"
            :isVisible="locationModalVisible"
            @onCloseModal="onCloseLocationModal"
          />
        </template>
        <template
          slot="completed_by_filter"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <fc-filter-checklist-assign-to
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
              column,
            }"
            ref="fcFilterAssignTo"
            :searchable="true"
            :multipleChoice="true"
          />
        </template>
        <span slot="status" slot-scope="text, record">
          <fc-status-tag :status="text" />
          <checklist-due-date :checklist="record" checklistType="pending" />
        </span>
        <div slot="checklists" slot-scope="text, record">
          <span>{{ record.name }}</span>
          <div v-if="record.notes" class="fc-linked-text">
            <span>Notes</span>
            <div v-html="record.notes"></div>
          </div>
          <div v-if="record.link_asset_id" class="fc-linked-text">
            <span>Linked Asset ID</span>
            <a href="#">{{ record.link_asset_id }}</a>
          </div>
          <div v-if="record.link_asset_ids.length" class="fc-linked-text" style="word-break: break-all">
            <span>Linked Asset IDs</span>
            <span>
              <span v-for="(asset_id, index) in record.link_asset_ids" :key="index">
                <fc-link-asset-detail :asset_id="asset_id" />
                <span v-if="index !== record.link_asset_ids.length - 1">, </span>
              </span>
            </span>
          </div>
          <div v-if="isLinkedRequests(record)" class="fc-linked-text">
            <span>Linked Request IDs</span>
            <span>
              <span v-for="(request, index) in record.link_requests" :key="index">
                <a :href="`/features/requests?request_ids=[${request.id}]`" target="_blank" rel="nofollow noopener">{{
                  request.case_id
                }}</a>
                <span v-if="index !== record.link_requests.length - 1">, </span>
              </span>
            </span>
          </div>
          <div v-if="record && record.location_tags.length > 0" class="fc-linked-text linked-location-tags-list">
            <span>Linked Location Tag(s)</span>
            <span>
              <span v-for="(linkToLocation, index) in record.location_tags" :key="linkToLocation.id">
                <fc-link-location-detail :data="linkToLocation" />
                <span v-if="index < record.location_tags.length - 1">,</span>
              </span>
            </span>
          </div>
          <checklist-setting-linked-customers :customers="record.customers" />
        </div>
        <template slot="assigned_managers" slot-scope="text, record">
          <span v-if="record.sign_off_type == 'multiple_steps'">{{ record.process_flow.name }}</span>
          <span v-else-if="record.assigned_managers.length == 0 || record.sign_off_type == 'skip_sign_off'"></span>
          <span v-else-if="record.assigned_managers.length >= managersListLength">All Managers</span>
          <span v-else>{{ getAssignedManagersList(record.assigned_managers) }}</span>
        </template>
        <span slot="format_time" slot-scope="text">
          <fc-decorator-time :time="text" />
        </span>
        <template slot="action" slot-scope="text, record">
          <table-actions :data="record" :actionVisible="record.id === chosenItem.id" />
        </template>
        <template slot="checklist_id" slot-scope="text, record">
          <div>{{ text }}</div>
          <fc-label-tags v-if="isExistLabel(record)" :labels="record.labels" />
        </template>
        <template slot="recurrence" slot-scope="text, record">
          <div v-if="record.recurrence.kind === 'custom_datetime'">
            Custom recurrence
          </div>
          <div v-else-if="isNoRecurrence(record)">No recurrence</div>
          <div v-else>
            <div class="inline_row">
              <span>Start date and time: </span>
              <span>{{ record.recurrence.start_datetime | formatTimeStamp }}</span>
            </div>
            <div class="inline_row">
              <span>Repeat Every: </span>
              <span>{{ getRepeatDescription(record) }}</span>
            </div>
            <div class="inline_row">
              <span>Ends: </span>
              <span>{{ getEndsDescription(record.recurrence) }}</span>
            </div>
          </div>
        </template>
      </fc-table-v2>
      <drawer-checklist-pending :data="checklistData" />
      <modal-checklist-cancel :data="checklistData" :checklists="checklists" />
    </div>
  </div>
</template>

<script>
import _ from 'lodash';
import { mapState } from 'vuex';

import FcAttachmentLink from '@/components/Shared/FcAttachmentLink.vue';
import FcDecoratorTime from '@/components/Shared/FcDecoratorTime.vue';
import FcSearch from '@/components/Shared/FcSearch.vue';
import FcSelectEntries from '@/components/Shared/FcSelectEntries.vue';
import FcStatusTag from '@/components/Shared/FcStatusTag.vue';
import FcTableV2 from '@/components/Shared/FcTableV2.js';
import TableActions from '@/components/Checklists/MainActions/ChecklistPendingTableActions.vue';
import DrawerChecklistPending from '@/components/Checklists/MainActions/DrawerActions/Pending/DrawerChecklistPending.vue';
import ModalChecklistCancel from '@/components/Checklists/MainActions/ModalActions/ModalChecklistCancel.vue';
import ExportCsv from '@/components/Checklists/MainActions/Actions/ExportCsvChecklist.vue';
import ChecklistDueDate from '@/components/Checklists/MainActions/Actions/ChecklistDueDate.vue';
import FcFilterLabelsV2 from '@/components/Shared/Label/FcFilterLabelsV2.vue';
import ChecklistSettingLinkedCustomers from '@/components/Settings/ChecklistSettings/Actions/ChecklistSettingLinkedCustomers.vue';
import BulkDeleteChecklists from '@/components/Checklists/MainActions/Actions/Pending/BulkDeleteChecklists';
import FcLabelTags from '@/components/Shared/Label/FcLabelTags.vue';
import FcFilterLocationTags from '@/components/Shared/FcTableColumnFilters/FcFilterLocationTags.vue';
import FcFilterLocationTagsV2 from '@/components/Shared/FcTableColumnFilters/FcFilterLocationTagsV2.vue';
import FcCustomizeColumnsTable from '@/components/Shared/FcCustomizeColumnsTable.vue';

import { YellowHintIcon } from '@/components/Icons/index.js';
import { RECURRENCE_FILTER, FILTER_REMEMBERED_PROPERTIES, LABEL_TYPES } from '@/utils/consts';
const pendingChecklistFilterKey = `${FILTER_REMEMBERED_PROPERTIES.CHECKLIST}.${FILTER_REMEMBERED_PROPERTIES.PENDING_CHECKLIST}`;

export default {
  props: {
    checklists: {
      type: Array,
      default: [],
    },
    pageInformation: {
      type: Object,
    },
    labels: {
      type: Array,
      default: [],
    },
  },
  components: {
    FcTableV2,
    FcSearch,
    FcStatusTag,
    FcAttachmentLink,
    FcSelectEntries,
    FcDecoratorTime,
    TableActions,
    DrawerChecklistPending,
    ModalChecklistCancel,
    ExportCsv,
    FcFilterLabelsV2,
    ChecklistSettingLinkedCustomers,
    BulkDeleteChecklists,
    FcFilterLocationTags,
    FcFilterLocationTagsV2,
    FcLabelTags,
    FcCustomizeColumnsTable,
    ChecklistDueDate,
  },
  data() {
    return {
      YellowHintIcon,
      columns: [
        {
          title: 'Checklist ID',
          dataIndex: 'checklist_id',
          sorter: true,
          disabled: true,
          defaultChecked: true,
          scopedSlots: {
            customRender: 'checklist_id',
            filterDropdown: 'checklist_id_filter',
          },
          filterKey: 'label_ids',
          sorterKey: 'indentity_number',
        },
        {
          title: 'Status',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          disabled: false,
          defaultChecked: true,
          width: 180,
        },
        {
          title: 'Details',
          dataIndex: 'checklists',
          scopedSlots: { customRender: 'checklists', filterDropdown: 'linked_assets_filter' },
          disabled: false,
          defaultChecked: true,
          filterKey: 'link_asset_ids',
        },
        {
          title: '',
          dataIndex: 'linked_location_tags',
          scopedSlots: { filterDropdown: 'linked_location_tags_filter' },
          disabled: false,
          defaultChecked: true,
          width: 30,
          onFilterDropdownVisibleChange: () => {
            this.locationModalVisible = true;
          },
          filterKey: 'location_tag_ids',
        },
        {
          title: 'Recurrence',
          dataIndex: 'recurrence',
          sorter: false,
          disabled: false,
          defaultChecked: true,
          scopedSlots: { customRender: 'recurrence' },
          filters: RECURRENCE_FILTER.map(({ id, title }) => ({
            text: title,
            value: id,
          })),
          filterKey: 'recurrence_kinds',
        },
        {
          title: 'Created at',
          dataIndex: 'created_at',
          scopedSlots: { customRender: 'format_time', filterDropdown: 'created_at_filter' },
          sorter: true,
          disabled: false,
          defaultChecked: true,
          filterKey: 'createdAtDateRange',
        },
        {
          title: 'Assign To',
          dataIndex: 'completed_by',
          scopedSlots: { filterDropdown: 'completed_by_filter' },
          disabled: false,
          defaultChecked: true,
          filterKey: 'staff_ids',
        },
        {
          title: 'Sign Off By',
          dataIndex: 'assigned_managers',
          disabled: false,
          defaultChecked: true,
          scopedSlots: { customRender: 'assigned_managers' },
        },
        {
          title: 'Last Modified',
          dataIndex: 'updated_at',
          scopedSlots: { customRender: 'format_time', filterDropdown: 'updated_at_filter' },
          sorter: true,
          disabled: false,
          defaultChecked: true,
          filterKey: 'updatedAtDateRange',
        },
        {
          title: '',
          dataIndex: '',
          scopedSlots: { customRender: 'action' },
          className: 'fc-action-row',
          width: 0,
          disabled: true,
          defaultChecked: true,
        },
      ],
      dateFormat: 'DD/MM/YYYY',
      createdAtDateRange: [],
      updatedAtDateRange: [],
      loading: false,
      chosenItem: { id: 0 },
      perPage: 10,
      page: 1,
      q: '',
      orderSort: undefined,
      dataSort: undefined,
      filter: undefined,
      checklistData: {},
      dynamicColumns: [],
      selectedChecklistIds: [],
      selectedLabels: [],
      selectedRecurrences: [],
      pendingChecklistFilterKey,
      selectedCompletedBy: [],
      selectedLinkedAssets: [],
      selectedLinkedLocations: [],
      locationModalVisible: false,
      LABEL_TYPES,
    };
  },
  created() {
    this.initializeColumns();
  },
  watch: {
    pendingChecklistFilter(newValue) {
      if (!newValue) {
        return;
      }
      if (!newValue.label_ids || newValue.label_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'checklist_id' && (column.filteredValue = null);
        });
      }
      if (!newValue.recurrence_kinds || newValue.recurrence_kinds.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'recurrence' && (column.filteredValue = null);
        });
      }
      if (!newValue.link_asset_ids || newValue.link_asset_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'checklists' && (column.filteredValue = null);
        });
      }
      if (!newValue.location_tag_ids || newValue.location_tag_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'linked_location_tags' && (column.filteredValue = null);
        });
      }
      if (!newValue.staff_ids || newValue.staff_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'completed_by' && (column.filteredValue = null);
        });
      }
    },
  },
  mounted() {
    let start_created_at = this.pendingChecklistFilter?.start_created_at;
    let end_created_at = this.pendingChecklistFilter?.end_created_at;
    let start_updated_at = this.pendingChecklistFilter?.start_updated_at;
    let end_updated_at = this.pendingChecklistFilter?.end_updated_at;
    let labelIds = this.pendingChecklistFilter?.label_ids;
    let assetIds = this.pendingChecklistFilter?.link_asset_ids;
    let locationIds = this.pendingChecklistFilter?.location_tag_ids;
    let staffIds = this.pendingChecklistFilter?.staff_ids;
    this.selectedChecklistIds = [];
    this.calDynamicHeight();
    if (start_created_at && end_created_at) {
      this.createdAtDateRange = [moment(start_created_at, 'DD/MM/YYYY'), moment(end_created_at, 'DD/MM/YYYY')];
    }
    if (start_updated_at && start_updated_at) {
      this.updatedAtDateRange = [moment(start_updated_at, 'DD/MM/YYYY'), moment(end_updated_at, 'DD/MM/YYYY')];
    }
    if (labelIds) {
      this.selectedLabels = labelIds.map(String);
    }
    if (assetIds && assetIds.length) {
      this.selectedLinkedAssets = JSON.parse(assetIds).map(String);
    }
    if (locationIds && locationIds.length) {
      this.selectedLinkedLocations = JSON.parse(locationIds).map(String);
    }
    if (staffIds && staffIds.length) {
      this.selectedCompletedBy = JSON.parse(staffIds).map(String);
    }
    this.q = this.initSearchText;
  },
  methods: {
    onClearFilter(payload) {
      this.$refs?.fcFilterLabels?.handleReset();
      this.$refs?.fcFilterLinkedAssets?.handleReset();
      this.$refs?.fcFilterLinkedLocations?.handleReset();
      this.$refs?.fcFilterAssignTo?.handleReset();
      this.dateRange = [];
      this.selectedRecurrences = [];
      this.selectedCompletedBy = [];
      this.selectedLinkedAssets = [];
      this.selectedLinkedLocations = [];
      this.selectedLabels = [];
      this.q = '';
      this.createdAtDateRange = [];
      this.updatedAtDateRange = [];
      this.fetch(payload, { isShowClearFilter: false });
    },
    handleSelectEntriesChange(perPage) {
      this.page = 1;
      this.perPage = perPage;
      this.fetch({}, { isShowClearFilter: null });
    },
    handleInputSearchSubmit(value) {
      this.page = 1;
      this.q = value;
      this.fetch();
    },
    handleRangePickerChange(value, filter) {
      this.page = 1;
      this.fetch();
    },
    handleVisibleChange(id) {
      this.loadingQRCode = true;
      this.$store.dispatch('checklist_settings/loadQRCodeImage', id).then(() => {
        this.loadingQRCode = false;
      });
    },
    onCloseLocationModal() {
      this.locationModalVisible = false;
    },
    fetch(filterPayload = {}, { isShowClearFilter = true } = {}) {
      this.loading = true;
      let payload = {
        page: this.page,
        per_page: this.perPage,
        data_sort: this.dataSort,
        order_sort: this.orderSort,
        q: this.q,
        ids: this.checklistIds,
        param_tracking_id: this.paramTrackingId,
        recurrence_kinds: this.selectedRecurrences,
        staff_ids: this.selectedCompletedBy,
        location_tag_ids: this.selectedLinkedLocations,
        link_asset_ids: this.selectedLinkedAssets,
        start_created_at: this.createdAtDateRange.length
          ? moment(this.createdAtDateRange[0]).format('DD/MM/YYYY')
          : undefined,
        end_created_at: this.createdAtDateRange.length
          ? moment(this.createdAtDateRange[1]).format('DD/MM/YYYY')
          : undefined,
        start_updated_at: this.updatedAtDateRange.length
          ? moment(this.updatedAtDateRange[0]).format('DD/MM/YYYY')
          : undefined,
        end_updated_at: this.updatedAtDateRange.length
          ? moment(this.updatedAtDateRange[1]).format('DD/MM/YYYY')
          : undefined,
        label_ids: JSON.stringify(filterPayload.label_ids),
      };
      // Handle isShowClearFilter logic
      if (_.isNull(isShowClearFilter)) {
        isShowClearFilter = this.pendingChecklistFilter?.isShowClearFilter;
      } else if (!isShowClearFilter) {
        // If isShowClearFilter is false, use only the payload without merging with the current filter
        payload = filterPayload;
      }
      delete payload.ids;
      this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
        property: pendingChecklistFilterKey,
        value: {
          ...payload,
          label_ids: this.selectedLabels,
          link_asset_ids: this.selectedLinkedAssets,
          location_tag_ids: this.selectedLinkedLocations,
          staff_ids: this.selectedCompletedBy,
          isShowClearFilter,
          createdAtDateRange: JSON.stringify(this.createdAtDateRange),
          updatedAtDateRange: JSON.stringify(this.updatedAtDateRange),
        },
      });

      this.$store
        .dispatch('checklists/loadAll', {
          ...payload,
          label_ids: JSON.stringify(this.selectedLabels),
          isShowClearFilter: undefined,
        })
        .then(() => {
          this.loading = false;
        })
        .catch((error) => {
          this.$message.error(error.response?.data?.message);
          this.loading = false;
        });
    },
    isNoRecurrence(record) {
      let recurrence = record.recurrence;
      if (!recurrence.id) return true;

      return recurrence.enabled_number_limit && recurrence.number_limit <= 0;
    },
    getRepeatDescription(record) {
      let recurrence = record.recurrence;
      switch (recurrence.kind) {
        case 'day':
          return `${recurrence.number_repeat} Day`;
        case 'week':
          return `${recurrence.number_repeat} Week on ${this.getDaysOfWeek(recurrence.day_of_week)}`;
        case 'month':
          return `${recurrence.number_repeat} Month on the ${recurrence.day_of_month}`;
        case 'week_in_month':
          return `${recurrence.number_repeat} Month on the ${this.getWeekNumber(recurrence)}`;
      }
    },
    getDaysOfWeek(days) {
      return days
        .map((x) =>
          moment()
            .day(x)
            .format('ddd'),
        )
        .join(', ');
    },
    getWeekNumber(recurrence) {
      let week = '';
      let week_of_month = recurrence.week_of_month;
      if (week_of_month == 1) {
        week = 'First';
      } else if (week_of_month == 2) {
        week = 'Second';
      } else if (week_of_month == 3) {
        week = 'Third';
      } else if (week_of_month == 4) {
        week = 'Fourth';
      } else if (week_of_month == 5) {
        week = 'Last';
      }
      return `${week} ${moment()
        .day(recurrence.day_in_week)
        .format('dddd')}`;
    },
    getEndsDescription(recurrence) {
      if (recurrence.never_stop) {
        return 'Never';
      }
      if (recurrence.enabled_end_on) {
        return `On ${moment(recurrence.end_on, 'X').format('DD/MM/YYYY')}`;
      }
      if (recurrence.enabled_number_limit) {
        return `After ${recurrence.number_limit} occurrences`;
      }
    },
    onTableChange(value) {
      let filterPayload = this.decoratorFilter(value.filters);
      this.selectedLabels = filterPayload?.label_ids || undefined;
      this.selectedRecurrences = filterPayload?.recurrence_kinds || undefined;
      this.selectedCompletedBy = filterPayload?.completed_by || undefined;
      this.selectedLinkedAssets = filterPayload?.linked_assets || undefined;
      this.selectedLinkedLocations = filterPayload?.linked_locations || undefined;
      if (!_.isEmpty(filterPayload)) {
        this.columns = convertToFilteredColumn(this.columns, value.filters);
      }
      if (this.page != value.pagination.current) {
        this.page = value.pagination.current;
      } else {
        this.page = 1;
        this.dataSort = this.decoratorDataSort(value?.sorter?.field);
        this.orderSort = this.decoratorOrderSort(value?.sorter?.order);
      }
      this.fetch({ ...filterPayload });
    },
    onSelectedRowChange(value) {
      this.chosenItem = value;
      if (value.id) this.checklistData = value;
    },
    decoratorOrderSort(data) {
      if (!data) return undefined;

      return data === 'ascend' ? 'asc' : 'desc';
    },
    decoratorDataSort(data) {
      if (!data) return undefined;

      return data === 'checklist_id' ? 'indentity_number' : data;
    },
    decoratorFilter(data) {
      const result = {};
      if (data['recurrence']?.length) {
        result['recurrence_kinds'] = JSON.stringify(data['recurrence']);
      }
      if (data['checklist_id']?.length) {
        const labelIds = data?.checklist_id?.map((str) => Number(str));
        result['label_ids'] = labelIds;
      }
      if (data['completed_by']?.length) {
        const ids = data?.completed_by?.map((str) => Number(str));
        result['completed_by'] = JSON.stringify(ids);
      }
      if (data['checklists']?.length) {
        const ids = data?.checklists?.map((str) => Number(str));
        result['linked_assets'] = JSON.stringify(ids);
      }
      if (data['linked_location_tags']?.length) {
        const ids = data?.linked_location_tags?.map((str) => Number(str));
        result['linked_locations'] = JSON.stringify(ids);
      }
      return result;
    },
    renameObject(oldProp, newProp, { [oldProp]: old, ...others }) {
      return {
        [newProp]: old,
        ...others,
      };
    },
    onClickAssetId(id) {
      this.$router.push({ path: '/features/assets', query: { asset_id: id } });
    },
    isExistLabel(record) {
      return record?.labels?.length;
    },
    calDynamicHeight() {
      this.$store.dispatch('dynamic_height/setIsCalculateDynamicHeight', true);
      this.$store.dispatch('dynamic_height/setCurrentSettings', {
        pageContainer: 'checklists-feature-container',
        isExistTabBar: true,
        isExistGroupOnTop: true,
        isExistPageTip: true,
      });
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedChecklistIds = [...selectedRowKeys];
    },
    fetchAfterDeleted() {
      this.selectedChecklistIds = [];
      this.fetch();
    },
    handleColumnsChange(newColumns) {
      localStorage.setItem('checklist_pending_columns', JSON.stringify(newColumns));
      // Step 1: Split checked columns
      const selectedColumns = this.columns.filter((column) => {
        const columnExist = newColumns.some((newCol) => column.dataIndex === newCol.dataIndex);
        return columnExist;
      });

      // Step 2: Split the action column
      const actionColumn = this.columns.find((item) => item.dataIndex === '') || {};

      this.dynamicColumns = [...selectedColumns, actionColumn];
    },
    initializeColumns() {
      let checklist_pending_filter = localStorage.getItem('checklist_pending_columns');

      if (checklist_pending_filter && checklist_pending_filter.length) {
        const parsedFilterColumns = JSON.parse(checklist_pending_filter);
        const parsedFilterKeyIndexes = parsedFilterColumns.map((item) => item.dataIndex);

        const mapColumnsWithCondition = this.columns.map((col) => {
          if (col.dataIndex === '') return col; // action column always checked
          return { ...col, defaultChecked: parsedFilterKeyIndexes.includes(col.dataIndex) };
        });

        this.columns = mapColumnsWithCondition;
        this.dynamicColumns = mapColumnsWithCondition.filter((column) => column.defaultChecked);
      } else {
        this.dynamicColumns = this.defaultColumns;
      }
    },
    getAssignedManagersList(managers) {
      return managers.map((manager) => manager.name).join(', ');
    },
  },
  computed: {
    decoratorCostCentre() {
      return this.costCentres.map(({ id, name }) => ({
        text: name,
        value: id.toString(),
      }));
    },
    decoratorAllStatus() {
      return this.allStatus.map(({ id, status }) => ({
        text: status,
        value: status,
      }));
    },
    defaultColumns() {
      return this.columns.filter((e) => e.defaultChecked);
    },
    getStartDate() {
      return this.createdAtDateRange.length ? moment(this.createdAtDateRange[0]).format('DD/MM/YYYY') : undefined;
    },
    getEndDate() {
      return this.createdAtDateRange.length ? moment(this.createdAtDateRange[1]).format('DD/MM/YYYY') : undefined;
    },
    initSearchText() {
      return this.pendingChecklistFilter?.q;
    },
    isLinkedRequests() {
      return (record) => record.link_requests?.length;
    },
    managersListLength() {
      return this.assignment_managers.filter((e) => e.role === 'manager' || e.role === 'admin').length;
    },
    ...mapState({
      QRCodeImage: (state) => state.checklist_settings.qr_code_image,
      checklistIds: (state) => state.checklists.checklistIds,
      paramTrackingId: (state) => state.param_trackings.paramTrackingId,
      currentDynamicHeight: (state) => state.dynamic_height.currentDynamicHeight,
      pendingChecklistFilter: (state) => _.get(state.filter_remembered, pendingChecklistFilterKey),
      assignment_managers: (state) => state.managers.assignment_managers,
    }),
  },
};
</script>

<style lang="less">
.range-filter.ant-calendar-picker {
  .ant-input {
    padding: 0;
    height: 40px;
  }
}

.checklist-tip {
  padding: 12px 34px;
  background: #fff;
  border-bottom: 0.8px solid #eef1f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-checklists .ant-table-body {
  max-height: calc(100vh - 355px) !important;
}

.checklist-linked-location-tags {
  .linked-container {
    display: flex;
    flex-direction: column;
  }
}
</style>
