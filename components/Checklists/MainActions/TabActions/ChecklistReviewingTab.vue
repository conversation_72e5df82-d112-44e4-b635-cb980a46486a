<template>
  <div>
    <div class="fc-group-on-top" ref="fcGroupOnTop">
      <div class="fc-group__left">
        <fc-select-entries
          v-on:onShowSizeChange="handleSelectEntriesChange"
          :currentSize="forReviewChecklist && forReviewChecklist.per_page"
        />
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" :initSearchText="initSearchText" />
        <fc-clear-filter :filteredKey="forReviewChecklistFilterKey" @onClearFilter="onClearFilter"></fc-clear-filter>
      </div>
      <div class="fc-group__right">
        <a-popover trigger="hover" placement="bottom" arrow-point-at-right overlayClassName="fc-header-popover">
          <a-button icon="more" style="width: 40px; height: 40px; margin-left: 14px;" />
          <template slot="content">
            <a-menu slot="overlay">
              <a-menu-item>
                <export-csv
                  :type="'signoff'"
                  :selectedChecklistIds="selectedChecklistIds"
                  :dropdown="true"
                  :start_date="getStartDate"
                  :end_date="getEndDate"
                  :isPendingTab="false"
                  :q="initSearchText"
                />
              </a-menu-item>
              <a-menu-item>
                <nuxt-link to="/features/checklists/config_export_fields">
                  <a-icon type="setting" />
                  Configure CSV Export Fields
                </nuxt-link>
              </a-menu-item>
            </a-menu>
          </template>
        </a-popover>
        <nuxt-link to="/features/checklists/upcoming_calendar">
          <a-button type="primary" icon="calendar" class="fc-group-button blue">
            Calendar
          </a-button>
        </nuxt-link>
        <nuxt-link to="/features/checklists/upcoming_checklists">
          <a-button type="primary" icon="check" class="fc-group-button blue">
            Upcoming
          </a-button>
        </nuxt-link>
      </div>
    </div>
    <div class="checklist-table-tips-element" ref="fcPageTip">
      <div class="checklist-table-tips-element__description">
        <span>
          <a-icon :component="YellowHintIcon"></a-icon>
          All checklists which are awaiting review (i.e. in the 'Processing' state) will be listed here.
        </span>
      </div>
      <div class="checklist-table-tips-element__buttons">
        <bulk-add-remarks-checklists
          v-on:loadDataTable="fetch"
          :selectedChecklistIds="selectedChecklistIds"
          :selectedChecklistIdsSignOff="selectedChecklistIdsSignOff"
          v-on:resetCheckedRows="resetCheckedRows"
        />
      </div>
    </div>

    <div>
      <fc-table-v2
        :columns="dynamicColumns"
        :dataSource="checklistsSignOff"
        v-on:onTableChange="onTableChange"
        v-on:onSelectedRowChange="onSelectedRowChange"
        :loading="loading"
        :pagination="pageInformationSignOff"
        :row-selection="{
          selectedRowKeys: selectedChecklistIds,
          onChange: onSelectChange,
        }"
        :tableHeight="currentDynamicHeight"
        ref="tableExpenditures"
        :rememberFilter="forReviewChecklist"
      >
        <template slot="checklist_id_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <fc-filter-labels-v2
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
            }"
            :filterOptions="labels"
            ref="fcFilterLabels"
            :defaultSelectedLabels="selectedLabels"
          />
        </template>
        <template slot="status_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <fc-filter-sign-off-checklist
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
            }"
            ref="fcFilterSignOffChecklist"
          />
        </template>
        <template slot="created_at_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <a-range-picker
            class="range-filter"
            :format="dateFormat"
            v-model="createdAtDateRange"
            @change="(e) => handleRangePickerChange(e, 'created_at')"
            allowClear
          />
        </template>
        <template slot="updated_at_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <a-range-picker
            class="range-filter"
            :format="dateFormat"
            v-model="updatedAtDateRange"
            @change="(e) => handleRangePickerChange(e, 'updated_at')"
            allowClear
          />
        </template>
        <template
          slot="linked_assets_filter"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <fc-filter-checklist-linked-asset
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
              column,
            }"
            ref="fcFilterLinkedAssets"
            :searchable="true"
            :multipleChoice="true"
          />
        </template>
        <template
          slot="linked_location_tags_filter"
          slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <fc-filter-location-tags-v2
            v-bind="{
              setSelectedKeys,
              selectedKeys,
              confirm,
              clearFilters,
              column,
            }"
            :isVisible="locationModalVisible"
            @onCloseModal="onCloseLocationModal"
          />
        </template>
        <template slot="status" slot-scope="text, record">
          <div class="checklist-status-container">
            <div class="checklist-status-container__status">
              <fc-status-tag :status="record.status" :customTitle="getStatus(record)" />
              <a-button
                icon="down"
                theme="filled"
                @click="showUpdateStatusModal(record.id)"
                v-tooltip="{ content: 'Update Checklist Status' }"
              />
            </div>
            <checklist-due-date
              class="checklist-status-container__due-date"
              :checklist="record"
              checklistType="reviewing"
            />
          </div>
        </template>
        <div slot="checklists" slot-scope="text, record">
          <span>{{ record.name }}</span>
          <div v-if="record.notes" class="fc-linked-text">
            <span>Notes</span>
            <div v-html="record.notes"></div>
          </div>
          <div v-if="record.link_asset_id" class="fc-linked-text">
            <span>Linked Asset ID</span>
            <a href="#">{{ record.link_asset_id }}</a>
          </div>
          <div v-if="record.link_asset_ids.length" class="fc-linked-text">
            <span>Linked Asset IDs</span>
            <span>
              <span v-for="(asset_id, index) in record.link_asset_ids" :key="index">
                <fc-link-asset-detail :asset_id="asset_id" />
                <span v-if="index !== record.link_asset_ids.length - 1">, </span>
              </span>
            </span>
          </div>
          <div v-if="record.link_requests.length" class="fc-linked-text">
            <span>Linked Request IDs</span>
            <span>
              <span v-for="(request, index) in record.link_requests" :key="index">
                <a :href="`/features/requests?request_ids=[${request.id}]`" target="_blank" rel="nofollow noopener">{{
                  request.case_id
                }}</a>
                <span v-if="index !== record.link_requests.length - 1">, </span>
              </span>
            </span>
          </div>
          <div v-if="record && record.location_tags.length > 0" class="fc-linked-text">
            <span>Linked Location Tag(s)</span>
            <span>
              <span v-for="(linkToLocation, index) in record.location_tags" :key="linkToLocation.id">
                <fc-link-location-detail :data="linkToLocation" />
                <span v-if="index < record.location_tags.length - 1">,</span>
              </span>
            </span>
          </div>

          <checklist-setting-linked-customers :customers="record.customers" />
        </div>
        <span slot="format_time" slot-scope="text">
          <fc-decorator-time :time="text" />
        </span>
        <template slot="manager_remarks" slot-scope="text, record">
          <div v-for="(r, index) in record.remarks" :key="index">
            <span>{{ `${r.process_by}: ${r.remark}` }}</span>
          </div>
          <div v-for="(r, index) in record.sign_off_remarks" :key="index">
            <span>
              {{ `${r.manager_name}: ${r.remarks}` }}
              <a-icon type="edit" @click="onEditRemarks(r)" style="margin-left: 3px" />
            </span>
          </div>
        </template>
        <template slot="scoring" slot-scope="text, record">
          <span v-if="record.score && record.score.actual_score && record.score.max_score">
            {{ roundNumber(record.score.actual_score) }}/ {{ roundNumber(record.score.max_score) }} ({{
              getPercentScore(record.score.actual_score, record.score.max_score)
            }}%)</span
          >
        </template>
        <template slot="action" slot-scope="text, record">
          <table-actions :data="record" :actionVisible="record.id === chosenItem.id" />
        </template>
        <template slot="checklist_id" slot-scope="text, record">
          <div>{{ text }}</div>
          <fc-label-tags v-if="isExistLabel(record)" :labels="record.labels" />
        </template>
        <template slot="recurrence" slot-scope="text, record">
          <div v-if="record.recurrence.kind === 'custom_datetime'">Custom recurrence</div>
          <div v-else-if="isNoRecurrence(record)">No recurrence</div>
          <div v-else>
            <div class="inline_row">
              <span>Start date and time: </span>
              <span>{{ record.recurrence.start_datetime | formatTimeStamp }}</span>
            </div>
            <div class="inline_row">
              <span>Repeat Every: </span>
              <span>{{ getRepeatDescription(record) }}</span>
            </div>
            <div class="inline_row">
              <span>Ends: </span>
              <span>{{ getEndsDescription(record.recurrence) }}</span>
            </div>
          </div>
        </template>
        <template slot="scoring_filter" slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }">
          <fc-filter-score-range
            :setSelectedKeys="setSelectedKeys"
            :selectedKeys="selectedKeys"
            :confirm="confirm"
            :clearFilters="clearFilters"
          />
        </template>
      </fc-table-v2>
      <drawer-checklist-reviewing :dataSource="checklistData" />
      <modal-add-remarks
        :data="checklistData"
        :checklists="checklistsSignOff"
        :selectedRemarks="selectedRemarks"
        @onClose="onCloseModalAddRemarks"
        v-if="isVisible"
      />
      <modal-sign-off :data="checklistData" :checklists="checklistsSignOff" />
      <modal-update-status :data="checklistData" />
    </div>
  </div>
</template>

<script>
import _ from 'lodash';
import { mapState } from 'vuex';
import FcPageTip from '@/components/Shared/FcPageTip.vue';
import FcAttachmentLink from '@/components/Shared/FcAttachmentLink.vue';
import FcDecoratorTime from '@/components/Shared/FcDecoratorTime.vue';
import FcSearch from '@/components/Shared/FcSearch.vue';
import FcSelectEntries from '@/components/Shared/FcSelectEntries.vue';
import FcStatusTag from '@/components/Shared/FcStatusTag.vue';
import FcTableV2 from '@/components/Shared/FcTableV2.js';
import FcCustomizeColumnsTable from '@/components/Shared/FcCustomizeColumnsTable.vue';
import FcLinkAssetDetail from '@/components/Shared/FcLinkAssetDetail.vue';
import FcFilterLabelsV2 from '@/components/Shared/Label/FcFilterLabelsV2.vue';
import FcLabelTags from '@/components/Shared/Label/FcLabelTags.vue';
import FcFilterSignOffChecklist from '@/components/Shared/FcFilterSignOffChecklist.vue';

import TableActions from '@/components/Checklists/MainActions/ChecklistReviewingTableActions.vue';
import ExportCsv from '@/components/Checklists/MainActions/Actions/ExportCsvChecklist.vue';
import DrawerChecklistReviewing from '@/components/Checklists/MainActions/DrawerActions/Reviewing/DrawerChecklistReviewing.vue';
import ChecklistSettingLinkedCustomers from '@/components/Settings/ChecklistSettings/Actions/ChecklistSettingLinkedCustomers.vue';
import BulkAddRemarksChecklists from '@/components/Checklists/MainActions/Actions/Reviewing/BulkAddRemarksChecklists.vue';
import ModalUpdateStatus from '@/components/Checklists/MainActions/ModalActions/ModalUpdateStatus.vue';
import ChecklistDueDate from '@/components/Checklists/MainActions/Actions/ChecklistDueDate.vue';

import { YellowHintIcon, DropdownIcon } from '@/components/Icons/index';
import { RECURRENCE_FILTER, FILTER_REMEMBERED_PROPERTIES } from '@/utils/consts';
import { convertToFilteredColumn } from '@/utils/helper';
import { roundNumber, getPercentScore } from '@/utils/scoring-helper.js';
import FcFilterLocationTags from '@/components/Shared/FcTableColumnFilters/FcFilterLocationTags.vue';
import FcFilterLocationTagsV2 from '@/components/Shared/FcTableColumnFilters/FcFilterLocationTagsV2.vue';
import FcFilterScoreRange from '@/components/Shared/FcTableColumnFilters/FcFilterScoreRange.vue';

const forReviewChecklistFilterKey = `${FILTER_REMEMBERED_PROPERTIES.CHECKLIST}.${FILTER_REMEMBERED_PROPERTIES.FOR_REVIEW_CHECKLIST}`;
const statuses = ['processing', 'complete', 'canceled'];
const MULTI_SIGN_OFF_FILTER = ['process_step_ids'];

export default {
  props: {
    checklistsSignOff: {
      type: Array,
      default: [],
    },
    pageInformationSignOff: {
      type: Object,
    },
    labels: {
      type: Array,
      default: [],
    },
  },
  components: {
    ExportCsv,
    FcTableV2,
    FcSearch,
    FcStatusTag,
    FcAttachmentLink,
    FcSelectEntries,
    FcDecoratorTime,
    TableActions,
    DrawerChecklistReviewing,
    FcCustomizeColumnsTable,
    FcPageTip,
    FcLinkAssetDetail,
    BulkAddRemarksChecklists,
    ModalUpdateStatus,
    DropdownIcon,
    FcFilterLabelsV2,
    FcFilterSignOffChecklist,
    ChecklistSettingLinkedCustomers,
    FcFilterLocationTags,
    FcFilterLocationTagsV2,
    FcLabelTags,
    FcFilterScoreRange,
    ChecklistDueDate,
  },
  data() {
    return {
      statuses,
      columns: [
        {
          title: 'Checklist ID',
          dataIndex: 'checklist_id',
          sorter: true,
          disabled: true,
          defaultChecked: true,
          scopedSlots: {
            customRender: 'checklist_id',
            filterDropdown: 'checklist_id_filter',
          },
          filterKey: 'label_ids',
        },
        {
          title: 'Status',
          dataIndex: 'status',
          scopedSlots: {
            customRender: 'status',
            filterDropdown: 'status_filter',
          },
          sorter: true,
          disabled: true,
          defaultChecked: true,
          width: 180,
          filterKey: 'status',
          sorterKey: 'status',
        },
        {
          title: 'Details',
          dataIndex: 'checklists',
          scopedSlots: { customRender: 'checklists', filterDropdown: 'linked_assets_filter' },
          disabled: true,
          defaultChecked: true,
          filterKey: 'link_asset_ids',
        },
        {
          title: '',
          dataIndex: 'linked_location_tags',
          scopedSlots: { filterDropdown: 'linked_location_tags_filter' },
          disabled: true,
          defaultChecked: true,
          width: 30,
          filterKey: 'location_tag_ids',
          onFilterDropdownVisibleChange: () => {
            this.locationModalVisible = true;
          },
        },
        {
          title: 'Recurrence',
          dataIndex: 'recurrence',
          sorter: false,
          disabled: true,
          defaultChecked: true,
          scopedSlots: { customRender: 'recurrence' },
          filters: RECURRENCE_FILTER.map(({ id, title }) => ({
            text: title,
            value: id,
          })),
          filterKey: 'recurrence_kinds',
        },

        {
          title: 'Remarks',
          dataIndex: 'manager_remarks',
          scopedSlots: { customRender: 'manager_remarks' },
          disabled: false,
          defaultChecked: true,
          width: 300,
        },
        {
          title: 'Score',
          dataIndex: 'scoring',
          scopedSlots: { customRender: 'scoring', filterDropdown: 'scoring_filter' },
          disabled: false,
          defaultChecked: true,
          sorter: true,
          sorterKey: 'score_percent',
          filterKey: 'score_percent_range',
        },
        {
          title: 'Last Modified',
          dataIndex: 'updated_at',
          scopedSlots: { customRender: 'format_time', filterDropdown: 'updated_at_filter' },
          sorter: true,
          disabled: false,
          defaultChecked: true,
          filterKey: 'updatedAtDateRange',
        },
        {
          title: 'Created at',
          dataIndex: 'created_at',
          scopedSlots: { customRender: 'format_time', filterDropdown: 'created_at_filter' },
          sorter: true,
          disabled: false,
          defaultChecked: true,
          filterKey: 'createdAtDateRange',
        },
        {
          title: '',
          dataIndex: '',
          scopedSlots: { customRender: 'action' },
          className: 'fc-action-row',
          width: 0,
          disabled: true,
          defaultChecked: true,
        },
      ],
      dateFormat: 'DD/MM/YYYY',
      createdAtDateRange: [],
      updatedAtDateRange: [],
      loading: false,
      chosenItem: { id: 0 },
      perPage: 10,
      page: 1,
      q: '',
      orderSort: undefined,
      dataSort: undefined,
      filter: {
        status: statuses,
      },
      checklistData: {},
      dynamicColumns: [],
      selectedRemarks: {},
      selectedChecklistIds: [],
      selectedChecklistIdsSignOff: [],
      YellowHintIcon,
      dynamicHeight: '',
      selectedLabels: [],
      selectedRecurrences: [],
      selectedStatuses: [],
      forReviewChecklistFilterKey,
      selectedCompletedBy: [],
      selectedLinkedLocations: [],
      locationModalVisible: false,
      selectedProcessSteps: [],
      multiSignoffFilter: MULTI_SIGN_OFF_FILTER,
      selectedScorePercentRange: [],
    };
  },
  watch: {
    forReviewChecklist(newValue) {
      if (!newValue) {
        return;
      }
      if (!newValue.label_ids || newValue.label_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'checklist_id' && (column.filteredValue = null);
        });
      }
      if (!newValue.link_asset_ids || newValue.link_asset_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'checklists' && (column.filteredValue = null);
        });
      }
      if (!newValue.location_tag_ids || newValue.location_tag_ids.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'linked_location_tags' && (column.filteredValue = null);
        });
      }
      if (!newValue.score_percent_range || newValue.score_percent_range.length <= 0) {
        this.columns.forEach((column) => {
          column.dataIndex == 'scoring' && (column.filteredValue = null);
        });
      } else {
        this.columns.forEach((column) => {
          if (column.dataIndex == 'scoring') {
            column.filteredValue = newValue.score_percent_range;
          }
        });
      }
    },
  },
  created() {
    this.dynamicColumns = this.defaultColumns;
  },
  mounted() {
    this.selectedChecklistIds = [];
    this.calDynamicHeight();

    let start_created_at = this.forReviewChecklist?.start_created_at;
    let end_created_at = this.forReviewChecklist?.end_created_at;
    let start_updated_at = this.forReviewChecklist?.start_updated_at;
    let end_updated_at = this.forReviewChecklist?.end_updated_at;
    let labelIds = this.forReviewChecklist?.label_ids;
    let recurrenceKinds = this.forReviewChecklist?.recurrence_kinds;
    let status = this.forReviewChecklist?.status;
    let assetIds = this.pendingChecklistFilter?.link_asset_ids;
    let locationIds = this.pendingChecklistFilter?.location_tag_ids;

    if (start_created_at && end_created_at) {
      this.createdAtDateRange = [moment(start_created_at, 'DD/MM/YYYY"'), moment(end_created_at, 'DD/MM/YYYY"')];
    }

    if (start_updated_at && end_updated_at) {
      this.updatedAtDateRange = [moment(start_updated_at, 'DD/MM/YYYY"'), moment(end_updated_at, 'DD/MM/YYYY"')];
    }

    if (labelIds) {
      this.selectedLabels = labelIds.map(String);
    }
    if (assetIds && assetIds.length) {
      this.selectedLinkedAssets = JSON.parse(assetIds).map(String);
    }
    if (locationIds && locationIds.length) {
      this.selectedLinkedLocations = JSON.parse(locationIds).map(String);
    }
    this.selectedStatuses = status;
    this.selectedProcessSteps = this.forReviewChecklist?.process_step_ids;
    this.selectedRecurrences = recurrenceKinds;
    this.q = this.initSearchText;
    this.selectedScorePercentRange = this.forReviewChecklist?.score_percent_range || [];
  },
  methods: {
    getPercentScore,
    roundNumber,
    onClearFilter(payload) {
      this.locationModalVisible = false;
      this.$refs?.fcFilterLabels?.handleReset();
      this.$refs?.fcFilterLinkedAssets?.handleReset();
      this.$refs?.fcFilterLinkedLocations?.handleReset();
      this.$refs?.fcFilterSignOffChecklist?.handleReset();

      this.dateRange = [];
      this.selectedLinkedAssets = [];
      this.selectedLinkedLocations = [];
      this.selectedRecurrences = undefined;
      this.selectedStatuses = undefined;
      this.selectedLabels = undefined;
      this.selectedProcessSteps = undefined;
      this.selectedScorePercentRange = [];
      this.q = '';
      this.createdAtDateRange = [];
      this.updatedAtDateRange = [];
      this.fetch(payload, { isShowClearFilter: false });
    },
    handleSelectEntriesChange(perPage) {
      this.page = 1;
      this.perPage = perPage;
      this.fetch({}, { isShowClearFilter: null });
    },
    handleInputSearchSubmit(value) {
      this.page = 1;
      this.q = value;
      this.fetch();
    },
    handleRangePickerChange(value, filter) {
      this.page = 1;
      this.fetch({ page: 1 });
    },
    onCloseLocationModal() {
      this.locationModalVisible = false;
    },
    fetch(filterPayload = {}, { isShowClearFilter = true } = {}) {
      this.loading = true;
      let minScore = undefined;
      let maxScore = undefined;
      if (this.selectedScorePercentRange && this.selectedScorePercentRange.length === 2) {
        minScore = this.selectedScorePercentRange[0];
        maxScore = this.selectedScorePercentRange[1];
      }
      let payload = {
        page: this.page,
        per_page: this.perPage,
        q: this.q,
        data_sort: this.dataSort,
        order_sort: this.orderSort,
        ids: this.checklistIds,
        param_tracking_id: this.paramTrackingId,
        recurrence_kinds: this.selectedRecurrences,
        location_tag_ids: this.selectedLinkedLocations,
        link_asset_ids: this.selectedLinkedAssets,
        label_ids: this.selectedLabels,
        start_created_at: this.createdAtDateRange.length
          ? moment(this.createdAtDateRange[0]).format('DD/MM/YYYY')
          : undefined,
        end_created_at: this.createdAtDateRange.length
          ? moment(this.createdAtDateRange[1]).format('DD/MM/YYYY')
          : undefined,
        start_updated_at: this.updatedAtDateRange.length
          ? moment(this.updatedAtDateRange[0]).format('DD/MM/YYYY')
          : undefined,
        end_updated_at: this.updatedAtDateRange.length
          ? moment(this.updatedAtDateRange[1]).format('DD/MM/YYYY')
          : undefined,
        status: JSON.stringify(this.selectedStatuses),
        label_ids: JSON.stringify(this.selectedLabels),
        process_step_ids: JSON.stringify(this.selectedProcessSteps),
        min_score_percent: minScore !== null && minScore !== undefined ? minScore : undefined,
        max_score_percent: maxScore !== null && maxScore !== undefined ? maxScore : undefined,
      };
      delete payload.ids;
      // Handle isShowClearFilter logic

      if (_.isNull(isShowClearFilter)) {
        isShowClearFilter = this.forReviewChecklist?.isShowClearFilter;
      } else if (!isShowClearFilter) {
        // If isShowClearFilter is false, use only the payload without merging with the current filter
        payload = filterPayload;
      }

      let checkClearFilter = _.isEmpty(filterPayload) ? false : isShowClearFilter;

      this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
        property: forReviewChecklistFilterKey,
        value: {
          ...payload,
          label_ids: this.selectedLabels,
          link_asset_ids: this.selectedLinkedAssets,
          location_tag_ids: this.selectedLinkedLocations,
          score_percent_range: this.selectedScorePercentRange,
          isShowClearFilter: checkClearFilter,
          createdAtDateRange: JSON.stringify(this.createdAtDateRange),
          updatedAtDateRange: JSON.stringify(this.updatedAtDateRange),
        },
      });
      this.$store
        .dispatch('checklists/loadSignOff', { ...payload, isShowClearFilter: undefined })
        .then(() => (this.loading = false))
        .catch((error) => {
          this.$message.error(error.response.message);
          this.loading = false;
        });
    },
    isNoRecurrence(record) {
      let recurrence = record.recurrence;
      if (!recurrence.id) return true;

      return recurrence.enabled_number_limit && recurrence.number_limit <= 0;
    },
    getRepeatDescription(record) {
      let recurrence = record.recurrence;
      switch (recurrence.kind) {
        case 'day':
          return `${recurrence.number_repeat} Day`;
        case 'week':
          return `${recurrence.number_repeat} Week on ${this.getDaysOfWeek(recurrence.day_of_week)}`;
        case 'month':
          return `${recurrence.number_repeat} Month on the ${recurrence.day_of_month}`;
        case 'week_in_month':
          return `${recurrence.number_repeat} Month on the ${this.getWeekNumber(recurrence)}`;
      }
    },
    getDaysOfWeek(days) {
      return days
        .map((x) =>
          moment()
            .day(x)
            .format('ddd'),
        )
        .join(', ');
    },
    getWeekNumber(recurrence) {
      let week = '';
      let week_of_month = recurrence.week_of_month;
      if (week_of_month == 1) {
        week = 'First';
      } else if (week_of_month == 2) {
        week = 'Second';
      } else if (week_of_month == 3) {
        week = 'Third';
      } else if (week_of_month == 4) {
        week = 'Fourth';
      } else if (week_of_month == 5) {
        week = 'Last';
      }
      return `${week} ${moment()
        .day(recurrence.day_in_week)
        .format('dddd')}`;
    },
    getEndsDescription(recurrence) {
      if (recurrence.never_stop) {
        return 'Never';
      }
      if (recurrence.enabled_end_on) {
        return `On ${moment(recurrence.end_on, 'X').format('DD/MM/YYYY')}`;
      }
      if (recurrence.enabled_number_limit) {
        return `After ${recurrence.number_limit} occurrences`;
      }
    },
    onTableChange(value) {
      let filterPayload = this.decoratorFilter(value.filters);
      this.selectedLabels = filterPayload?.label_ids || undefined;
      this.selectedRecurrences = filterPayload?.recurrence_kinds || undefined;
      this.selectedStatuses = filterPayload?.status || undefined;
      this.selectedLinkedAssets = filterPayload?.linked_assets || undefined;
      this.selectedLinkedLocations = filterPayload?.linked_locations || undefined;
      this.selectedProcessSteps = filterPayload?.process_step_ids || undefined;
      this.selectedScorePercentRange = filterPayload?.score_percent_range || undefined;

      if (!_.isEmpty(filterPayload)) {
        this.columns = convertToFilteredColumn(this.columns, value.filters);
      }

      if (this.page != value.pagination.current) {
        this.page = value.pagination.current;
      } else {
        this.page = 1;
        if (value.sorter.field === 'scoring') {
          this.dataSort = 'score_percent';
        } else {
          this.dataSort = value?.sorter?.field || undefined;
        }
        this.orderSort = this.decoratorOrderSort(value?.sorter?.order);
      }
      this.fetch(filterPayload);
    },
    onSelectedRowChange(value) {
      this.chosenItem = value;
      if (value.id) this.checklistData = value;
    },
    decoratorOrderSort(data) {
      if (!data) return undefined;

      return data === 'ascend' ? 'asc' : 'desc';
    },
    decoratorFilter(data) {
      const result = {};
      if (data['recurrence']?.length) {
        result['recurrence_kinds'] = JSON.stringify(data['recurrence']);
      }
      if (data['checklist_id']?.length) {
        const labelIds = data?.checklist_id?.map((str) => Number(str));
        result['label_ids'] = labelIds;
      }
      if (data['status']?.length) {
        const { process_step_ids, status } = data['status'].reduce(
          (acc, item) => {
            let checkItem = `${item}`;
            if (!isNaN(checkItem) && checkItem.trim() !== '') {
              acc.process_step_ids.push(Number(checkItem));
            } else {
              acc.status.push(item);
            }
            return acc;
          },
          { process_step_ids: [], status: [] },
        );
        result['status'] = !_.isEmpty(status) ? status : undefined;
        result['process_step_ids'] = !_.isEmpty(process_step_ids) ? process_step_ids : undefined;
      }

      if (data['checklists']?.length) {
        const ids = data?.checklists?.map((str) => Number(str));
        result['linked_assets'] = JSON.stringify(ids);
      }
      if (data['linked_location_tags']?.length) {
        const ids = data?.linked_location_tags?.map((str) => Number(str));
        result['linked_locations'] = JSON.stringify(ids);
      }
      if (data['scoring']?.length === 2 && (data['scoring'][0] !== null || data['scoring'][1] !== null)) {
        result['score_percent_range'] = data['scoring'];
      }
      return result;
    },
    renameObject(oldProp, newProp, { [oldProp]: old, ...others }) {
      return {
        [newProp]: old,
        ...others,
      };
    },
    onClickAssetId(id) {
      this.$router.push({ path: '/features/assets', query: { asset_id: id } });
    },
    onEditRemarks(remarksObj) {
      this.selectedRemarks = remarksObj;
      this.$store.dispatch('checklists/setIsShowModalAddRemarks', true);
    },
    onCloseModalAddRemarks() {
      this.selectedRemarks = {};
      this.$store.dispatch('checklists/setIsShowModalAddRemarks', false);
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedChecklistIds = [...selectedRowKeys];
      let arrSignOff = selectedRows.filter((x) => x.status === 'processing').map((x) => x.id);
      this.selectedChecklistIdsSignOff = [...arrSignOff];
    },
    resetCheckedRows() {
      this.selectedChecklistIds = [];
      this.selectedChecklistIdsSignOff = [];
    },
    showUpdateStatusModal(id) {
      this.$store.dispatch('checklists/setCurrentChecklistId', id);
      this.$store.dispatch('checklists/setIsShowModalUpdateStatus', true);
    },
    isExistLabel(record) {
      return record?.labels?.length;
    },
    getStatus(record) {
      if (record.sign_off_type != 'multiple_steps') {
        return record.status;
      }
      let status = _.get(record, 'current_sign_off_step_status.status_name');
      if (_.isEmpty(status)) return record.status;
      return status;
    },
    calDynamicHeight() {
      this.$store.dispatch('dynamic_height/setIsCalculateDynamicHeight', true);
      this.$store.dispatch('dynamic_height/setCurrentSettings', {
        pageContainer: 'checklists-feature-container',
        isExistTabBar: true,
        isExistGroupOnTop: true,
        isExistPageTip: true,
      });
    },
  },
  computed: {
    decoratorCostCentre() {
      return this.costCentres.map(({ id, name }) => ({
        text: name,
        value: id.toString(),
      }));
    },
    decoratorAllStatus() {
      return this.allStatus.map(({ id, status }) => ({
        text: status,
        value: status,
      }));
    },
    defaultColumns() {
      return this.columns.filter((e) => e.defaultChecked);
    },
    getStartDate() {
      return this.createdAtDateRange.length ? moment(this.createdAtDateRange[0]).format('DD/MM/YYYY') : undefined;
    },
    getEndDate() {
      return this.createdAtDateRange.length ? moment(this.createdAtDateRange[1]).format('DD/MM/YYYY') : undefined;
    },
    initSearchText() {
      return this.forReviewChecklist?.q;
    },
    ...mapState({
      isVisible: (state) => state.checklists.isShowModalAddRemarks,
      currentChecklist: (state) => state.checklists.currentChecklist,
      checklistIds: (state) => state.checklists.checklistIds,
      paramTrackingId: (state) => state.param_trackings.paramTrackingId,
      currentDynamicHeight: (state) => state.dynamic_height.currentDynamicHeight,
      forReviewChecklist: (state) => _.get(state.filter_remembered, forReviewChecklistFilterKey),
    }),
  },
};
</script>

<style lang="less" scoped>
.checklist-table-tips-element {
  @media (max-width: 1320px) {
    &__description {
      width: 50%;
    }
  }
  padding: 12px 0 12px 34px;
  background: #fff;
  border-bottom: 0.8px solid #eef1f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.checklist-linked-location-tags {
  .linked-container {
    display: flex;
    flex-direction: column;
  }
}

.checklist-status-container {
  &__status {
    display: flex;
    align-items: center;
    justify-content: start;
  }
  .ant-btn {
    flex-shrink: 0;
    font-size: 12px;
  }

  .ant-btn-icon-only {
    width: 26px;
    height: 26px;
  }

  .ant-tag {
    white-space: normal;
  }
}
</style>
