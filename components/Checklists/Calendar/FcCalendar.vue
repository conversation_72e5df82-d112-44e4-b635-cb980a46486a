<template>
  <div class="fc-calendar">
    <FullCalendar :options="calendarOptions" ref="fullCalendar" :datesSet="handleMonthChange">
      <template #eventContent="arg">
        <div
          class="fc-calendar-custom-event"
          v-tooltip="{
            content: customTooltip(arg),
            placement: 'left-center',
            classes: 'calendar-tooltip',
          }"
        >
          <div class="fc-daygrid-event-dot" v-if="isRoleValidated"></div>
          <div class="fc-event-custom-title">{{ arg.event.title }}</div>
          <div class="fc-event-custom-time">{{ arg.timeText }}</div>
        </div>
      </template>
    </FullCalendar>
  </div>
</template>

<script>
import './index.less';
import FullCalendar, { Calendar } from '@fullcalendar/vue';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import Vue from 'vue';
import FcLabelTags from '@/components/Shared/Label/FcLabelTags.vue';
import FcDueDateTag from '@/components/Shared/FcDueDateTag.vue';

export default {
  props: {
    initialEvents: {
      type: Array,
      default: [],
    },
    initialDate: {
      type: String,
    },
    generalSettings: {
      type: Object,
    },
  },
  components: {
    FullCalendar,
    FcLabelTags,
    FcDueDateTag,
  },
  computed: {
    isRoleValidated: function() {
      const { role } = this.$store.state.auth.user;
      return role === 'admin' || role === 'manager';
    },
  },
  data() {
    return {
      calendarApi: null,
      calendarOptions: {
        timeZone: `${this.generalSettings.timezone}`,
        plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin],
        initialView: 'dayGridMonth',
        headerToolbar: {
          left: 'dayGridMonth,timeGridWeek,timeGridDay today',
          center: 'title',
          right: 'prev,next',
        },
        eventClick: this.handleDateClick,
        eventDidMount: (info) => {
          // Event dot color
          if (info?.event?.extendedProps?.data?.due_date?.label === 'Overdue') {
            info.el.firstChild.firstChild.style.borderColor = '#800080';
          } else if (info?.event?.extendedProps?.data?.is_pending) {
            info.el.firstChild.firstChild.style.borderColor = '#fadb15';
          }
        },
        events: this.initialEvents,
        eventTimeFormat: { hour: '2-digit', minute: '2-digit' },
        height: '80vh',
        dayMaxEvents: 4,
        datesSet: this.handleMonthChange,
      },
      search: null,
      createChildIcon:
        '<i aria-label="icon: switcher" class="anticon anticon-switcher has-tooltip" data-original-title="null" style="color: rgb(255, 99, 55);" aria-describedby="tooltip_wt2fpl9hte"><svg viewBox="64 64 896 896" data-icon="switcher" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M752 240H144c-17.7 0-32 14.3-32 32v608c0 17.7 14.3 32 32 32h608c17.7 0 32-14.3 32-32V272c0-17.7-14.3-32-32-32zM596 606c0 4.4-3.6 8-8 8H308c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h280c4.4 0 8 3.6 8 8v48zm284-494H264c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h576v576c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V144c0-17.7-14.3-32-32-32z"></path></svg></i>',
    };
  },
  watch: {
    initialEvents(newValue, oldValue) {
      this.calendarOptions.events = newValue;
    },
  },
  methods: {
    customTooltip(data) {
      const source = data.event.extendedProps.data;

      const labelsContainer = document.createElement('div');
      const StatusLabelsComponent = Vue.extend(FcLabelTags);
      const statusLabels = new StatusLabelsComponent({
        propsData: {
          labels: source.labels,
        },
      });
      statusLabels.$mount();
      labelsContainer.appendChild(statusLabels.$el);

      const dueDateContainer = document.createElement('div');
      const DueDateLabelComponent = Vue.extend(FcDueDateTag);
      const dueDateLabel = new DueDateLabelComponent({
        propsData: {
          dueDate: source.due_date,
        },
      });
      dueDateLabel.$mount();
      dueDateContainer.appendChild(dueDateLabel.$el);

      return `
          <div class="fc-calendar-custom-tooltip">
            <div class="custom-tooltip-container">
              <span class="custom-tooltip-title">
                ${source.is_pending ? 'Checklist ID' : 'Parent Checklist ID'}
              </span>
              <span class="custom-tooltip-content">${source.parent_checklist_id}</span>
            </div>

            <div class="custom-tooltip-container">
              <span class="custom-tooltip-title">Checklist Name</span>
              <span class="custom-tooltip-content">${source.checklist_name}</span>
            </div>

            <div class="custom-tooltip-container">
              <span class="custom-tooltip-title">Assign to</span>
              <span class="custom-tooltip-content">${source.staff_names}</span>
            </div>

            <div class="custom-tooltip-container">
              <span class="custom-tooltip-title">
                ${source.is_pending ? 'Created at' : 'To create on'}
              </span>
              <span class="custom-tooltip-content">${moment(source.last_sent_at).format('DD/MM/YYYY hh:mm A')}</span>
            </div>

            <div class="custom-tooltip-container">
              <span class="custom-tooltip-title">Status</span>
              <span class="custom-tooltip-content">
                ${
                  source.is_pending
                    ? '<span class="ant-tag ant-tag-orange">Pending</span>'
                    : '<span class="ant-tag ant-tag-has-color" style="background-color: rgb(255, 85, 0);" > Upcoming </span>'
                }
              </span>
            </div>

            ${
              source.due_date?.label
                ? `
              <div class="custom-tooltip-container">
                <span class="custom-tooltip-title">Due Date</span>
                ${dueDateContainer.innerHTML}
              </div>
              `
                : ''
            }

            ${
              !!source?.link_asset_ids?.length
                ? `
              <div class="custom-tooltip-container">
                <span class="custom-tooltip-title">
                  Linked Asset IDs
                  ${source.is_create_child_checklist_for_each_asset ? this.createChildIcon : ''}
                </span>
                <span class="custom-tooltip-content">
                  ${source.link_asset_ids.join(', ')}
                </span>
              </div>
              `
                : ''
            }

            ${
              !!source?.location_tags?.length
                ? `
              <div class="custom-tooltip-container">
                <span class="custom-tooltip-title">
                  Linked Location Tag(s)
                  ${source.is_create_child_checklist_for_each_location_tag ? this.createChildIcon : ''}
                </span>
                <span class="custom-tooltip-content">
                  ${source.location_tags.map((locationTag) => locationTag.name).join(', ')}
                </span>
              </div>
              `
                : ''
            }

            ${
              !!source?.labels?.length
                ? `
              <div class="custom-tooltip-container">
                <span class="custom-tooltip-title" style="margin-bottom: -10px">Labels</span>
                ${labelsContainer.innerHTML}
              </div>
              `
                : ''
            }

            ${
              !!source?.customers?.length
                ? `
              <div class="custom-tooltip-container">
                <span class="custom-tooltip-title">Linked Customer</span>
                <span class="custom-tooltip-content">
                  ${source.customers
                    .map(
                      (customer, index) =>
                        `<div>
                      <span>
                        <b>
                          ${index + 1}.
                        </b>
                      </span>
                      <span>
                        <b>
                         ${customer.name}
                        </b>
                      </span>
                      ${
                        !!customer?.addresses?.length
                          ? `
                      <div>
                        ${customer.addresses
                          .map(
                            (address) =>
                              `
                        <div style="margin-left: 3px">
                          <span>-</span>
                          <span>
                            ${address.address}
                          </span>
                        </div>
                        `,
                          )
                          .join('')}
                      </div> 
                        `
                          : ''
                      }
                    </div>`,
                    )
                    .join('')}
                </span>
              </div>
              `
                : ''
            }
           
          </div>
        `;
    },
    async calendarEvents(fetchInfo, successCallback, failureCallback) {
      this.$emit('onCurrentMonthChange', this.calendarApi ? this.calendarApi.getDate() : null);
      if (moment(fetchInfo.start) > moment()) {
        let queryString = {};
        queryString['start_time'] = moment(fetchInfo.start).format('DD/MM/YYYY');
        queryString['end_time'] = moment(fetchInfo.start)
          .add(3, 'months')
          .endOf('month')
          .format('DD/MM/YYYY');
        queryString['q'] = this.search;
        this.$router.push({ path: this.$route.path, query: queryString });
      }
      this.$nextTick(() => {
        successCallback(this.initialEvents);
      });
    },
    async handleDateClick(arg) {
      if (this.isRoleValidated) {
        const { id, is_pending } = arg.event.extendedProps.data;
        let routeData = {};
        if (is_pending) {
          routeData = this.$router.resolve({
            path: '/features/checklists',
            query: {
              ids: [id],
            },
          });
        } else {
          routeData = this.$router.resolve({
            path: '/settings/checklist_settings',
            query: {
              q: +id,
            },
          });
        }
        window.open(routeData.href, '_blank', 'noopener,noreferrer');
      }
    },
    handleMonthChange(fetchInfo) {
      this.$emit('onCurrentMonthChange', fetchInfo);
    },
  },
  mounted() {
    this.calendarApi = this.$refs.fullCalendar.getApi();
  },
};
</script>

<style lang="less"></style>
