<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center">
      <div class="fc-group__left">
        <fc-search @onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button
          v-if="isShowExportBtn"
          type="primary"
          class="fc-group-button yellow"
          @click="onClickExportCSV"
          :loading="isCSVLoading"
        >
          <a-icon :component="ExportIcon" />
          Export CSV
        </a-button>
        <fc-add-to-dashboard :type="'attendance_data_table'" v-if="!shownInDashboard" />
      </div>
    </div>
    <fc-table
      :tableHeight="this.dynamicHeight"
      :columns="columns"
      rowKey="id"
      :dataSource="tableData"
      @onTableChange="handleTableChange"
    >
      <a
        slot="check_in"
        slot-scope="text, record"
        :href="`/features/attendances?attendance_ids=[${record.check_in_ids}]`"
        target="_blank"
        rel="noopener noreferrer"
        @click="handleClick(record.check_in_ids)"
      >
        {{ text }}
      </a>
      <a
        slot="check_out"
        slot-scope="text, record"
        :href="`/features/attendances?attendance_ids=[${record.check_out_ids}]`"
        target="_blank"
        rel="noopener noreferrer"
        @click="handleClick(record.check_out_ids)"
      >
        {{ text }}
      </a>
    </fc-table>
  </div>
</template>

<script>
import { ExportIcon } from '@/components/Icons';
import FcSearch from '@/components/Shared/FcSearch.vue';
import { getElementAbsoluteHeight } from '@/utils/css-helper';
import { generateAndDownloadCsv } from '@/utils/helper';

export default {
  components: {
    FcSearch,
  },
  props: {
    apiResponse: {
      type: Object,
    },
    dateRangePayload: {
      type: Object,
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: false,
    isCSVLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      columns: [
        {
          title: 'Date',
          dataIndex: 'created_at',
          sorter: (a, b) => this.dateSorter(a.created_at, b.created_at),
          defaultSortOrder: 'descend',
        },
        {
          title: 'Site',
          dataIndex: 'site_name',
          sorter: (a, b) => this.nameSorter(a.site_name, b.site_name),
        },
        {
          title: 'No. Check In',
          dataIndex: 'sum_check_in',
          scopedSlots: { customRender: 'check_in' },
          sorter: (a, b) => a.sum_check_in - b.sum_check_in,
        },
        {
          title: 'No. Check Out',
          dataIndex: 'sum_check_out',
          scopedSlots: { customRender: 'check_out' },
          sorter: (a, b) => a.sum_check_out - b.sum_check_out,
        },
      ],
      ExportIcon,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      dynamicHeight: 'auto',
    };
  },
  mounted() {
    if (!this.shownInDashboard) {
      window.addEventListener('resize', this.changeDynamicHeight);
      this.changeDynamicHeight();
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.changeDynamicHeight);
  },
  methods: {
    handleInputSearchSubmit(value) {
      this.q = value.toLowerCase();
    },
    handleTableChange({ pagination, filters, sorter }) {
      if (!sorter.order) {
        this.sorter.data_sort = undefined;
        this.sorter.order_sort = undefined;
      } else {
        this.sorter.data_sort = sorter.field;
        this.sorter.order_sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      }
    },
    dateSorter(dateA, dateB) {
      // Use "DD/MM/YYYY" if date string is longer than 7, else "MM/YYYY"
      const dateFormat = dateA.length > 7 ? 'DD/MM/YYYY' : 'MM/YYYY';

      return moment(dateA, dateFormat) - moment(dateB, dateFormat);
    },
    nameSorter(nameA, nameB) {
      if (nameA == null) nameA = '';
      if (nameB == null) nameB = '';
      return nameA.localeCompare(nameB);
    },

    getSortedTableData() {
      if (!this.tableData || !this.tableData.length) {
        return [
          {
            created_at: null,
            site_name: null,
            sum_check_in: null,
            sum_check_out: null,
          },
        ];
      }

      return [...this.tableData].sort((a, b) => {
        if (!this.sorter.data_sort) return 0;
        const field = this.sorter.data_sort;
        const order = this.sorter.order_sort;

        if (field === 'created_at') {
          return order === 'asc'
            ? this.dateSorter(a.created_at, b.created_at)
            : this.dateSorter(b.created_at, a.created_at);
        }

        if (field === 'site_name') {
          return order === 'asc'
            ? this.nameSorter(a.site_name, b.site_name)
            : this.nameSorter(b.site_name, a.site_name);
        }

        return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
      });
    },
    onClickExportCSV() {
      const sortedData = this.getSortedTableData();
      const exportData = sortedData.map((item) => ({
        created_at: item.created_at,
        site_name: item.site_name,
        sum_check_in: item.sum_check_in,
        sum_check_out: item.sum_check_out,
      }));
      const headers = {
        created_at: 'Date',
        site_name: 'Site',
        sum_check_in: 'No. Check In',
        sum_check_out: 'No. Check Out',
      };

      generateAndDownloadCsv(exportData, 'attendance_statistics', headers);
    },

    handleClick(value) {
      this.$store.dispatch('attendance_statistics/setAttendanceIds', value);
    },
    changeDynamicHeight() {
      const pageContainer = document.querySelector('.attendance-statistics-container');
      const statisticsFilterHeight = pageContainer.querySelector('.attendance-statistics-filter').offsetHeight;

      const fcGroupOnTopHeight = pageContainer
        .querySelector('.attendance-statistics-table')
        .querySelector('.fc-group-on-top').offsetHeight;

      const antTableHeaderHeight = pageContainer
        .querySelector('.attendance-statistics-table')
        .querySelector('.ant-table-header').offsetHeight;

      // Some elements offsetHeight cannot get margin values
      const antTablePaginationHeight = getElementAbsoluteHeight(
        pageContainer.querySelector('#fc_table').querySelector('.ant-table-header'),
      );

      this.dynamicHeight = `calc(100vh
        - 64px
        - ${statisticsFilterHeight}px
        - ${fcGroupOnTopHeight}px
        - ${antTableHeaderHeight}px
        - ${antTablePaginationHeight}px
        - ${10 * 2}px
        - ${16 * 2}px
      )`;
    },
  },
  computed: {
    trackingData() {
      return Object.keys(this.apiResponse).length ? this.apiResponse.data : [];
    },
    tableData() {
      if (this.q.length <= 0) {
        return this.trackingData;
      }

      return this.trackingData.filter((row) => {
        const rowSearchText = [row.created_at, row.site_name, row.sum_check_in, row.sum_check_out].join().toLowerCase();
        return rowSearchText.includes(this.q);
      });
    },
  },
};
</script>

<style></style>
