<template>
  <div class="attendance-statistics-container">
    <div class="attendance-statistics-filter" style="padding: 0;">
      <attendance-statistics-filter
        @applyFilter="onApplyFilter"
        @setInitialDateRangeAndFrequencyPayload="setInitialDateRangeAndFrequencyPayload"
      />
    </div>
    <div class="attendance-statistics-table">
      <attendance-statistics-table
        :apiResponse="attendanceStatistics"
        :dateRangePayload="dateRangeAndFrequencyPayload"
        :isCSVLoading="isLoading"
      />
    </div>
  </div>
</template>

<script>
import './index.less';
import { mapState } from 'vuex';
import AttendanceStatisticsFilter from './AttendanceStatisticsFilter.vue';
import AttendanceStatisticsTable from './AttendanceStatisticsTable.vue';

export default {
  components: {
    AttendanceStatisticsFilter,
    AttendanceStatisticsTable,
  },
  data() {
    return {
      dateRangeAndFrequencyPayload: {},
      isLoading: false,
    };
  },
  computed: {
    ...mapState({
      attendanceStatistics: (state) => state.attendance_statistics.attendanceStatistics,
    }),
  },
  methods: {
    fetch(payload) {
      this.$store.dispatch('attendance_statistics/loadAttendanceStatistics', { ...payload });
    },
    setInitialDateRangeAndFrequencyPayload(childPayload) {
      this.dateRangeAndFrequencyPayload = childPayload;
    },
    onApplyFilter(childPayload) {
      this.dateRangeAndFrequencyPayload = childPayload;
      this.$store.dispatch('attendance_statistics/loadAttendanceStatistics', childPayload);
    },
  },
};
</script>
