<template>
  <div draggable="true" :class="className" @dragstart="handleDragStart" v-if="isShowTask">
    <div class="task-header">
      <fc-status-tag :status="checklist.status" />
      <schedule-tag customTitle="Scheduled" status="processing" :resourceId="resourceId" v-if="isScheduled" />
    </div>
    <div class="task-content">
      <span
        >Checklist ID:
        <span class="value">{{ checklist.checklist_id }}</span>
      </span>
      <span
        >Name:
        <span class="value">{{ checklist.name }}</span>
      </span>
      <span
        >Created at:
        <span class="value">{{ checklist.created_at | formatTimeStamp }}</span>
      </span>
      <span class="assign-to">
        <span>Assign to:</span>
        <span class="value">
          <span>{{ checklist.completed_by }}</span>
        </span>
      </span>
      <div>
        <div class="customer-list-container">
          <span>Customers:</span>
          <div v-if="isPopover">
            <a-button class="customer-icon-button" v-tooltip="{ content: 'Link Customer' }" @click="handleLinkCustomer">
              <customer-icon-outline />
            </a-button>
            <checklist-setting-modal-customer
              :isVisible="isVisible"
              v-on:onCloseModal="isVisible = false"
              isLinkingCustomer
              @handleSubmitSelectedCustomers="handleSubmitSelectedCustomers"
              :confirmLoading="confirmLoading"
            />
          </div>
        </div>

        <span class="value" v-if="customers.length">
          <div v-for="(item, index) in customers" :key="index">
            <div>Name: {{ item.name }}</div>
            <div v-if="isExistAddress(item)">
              Addresses:
              <span class="value">
                <span v-for="(add, index) in item.addresses" :key="index">
                  <span>{{ add.address }} - {{ add.postal_code }}</span
                  ><span v-if="index < item.addresses.length - 1">{{ `, ` }}</span>
                </span>
              </span>
            </div>
          </div>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { state } from '../../../../../store/schedules';
import FcStatusTag from '../../../../Shared/FcStatusTag.vue';
import ScheduleTag from '../ScheduleTag.vue';
import CustomerIconOutline from '@/components/Icons/CustomerIconOutline.vue';
import ChecklistSettingModalCustomer from '@/components/Settings/ChecklistSettings/Actions/ChecklistSettingModalCustomer.vue';
import { mapState } from 'vuex';

export default {
  components: {
    FcStatusTag,
    ScheduleTag,
    CustomerIconOutline,
    ChecklistSettingModalCustomer,
  },
  props: {
    checklist: {
      type: Object,
      default: {},
    },
    isPopover: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isVisible: false,
      confirmLoading: false,
    };
  },
  mounted() {
    if (this.isPopover) {
      this.$store.dispatch('billing_customers/setSelectedCustomers', this.customers);
    }
  },
  methods: {
    handleDragStart(event) {
      event.dataTransfer.effectAllowed = 'move';

      let item = {
        id: new Date(),
        type: 'range',
        content: `${this.checklist.name}`,
        kind: 'checklist',
        resource_id: this.checklist.id,
        assigned_to: this.checklist.assigned_staff_ids,
      };
      event.dataTransfer.setData('text', JSON.stringify(item));
    },
    isExistAddress(item) {
      return item?.addresses?.length;
    },
    handleLinkCustomer() {
      this.isVisible = true;
    },
    handleSubmitSelectedCustomers(selectedCustomers) {
      this.confirmLoading = true;
      let formData = new FormData();
      formData.append('checklist_customer_params', JSON.stringify(selectedCustomers) || JSON.stringify([]));
      this.$store
        .dispatch('checklists/linkCustomers', {
          id: this.resourceId,
          payload: formData,
        })
        .then(() => {
          this.$message.success('Linked Customers to Checklist');
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
  },
  computed: {
    ...mapState({
      tasks: (state) => state.schedules.tasks,
      checklistsScheduled: (state) => state.schedules.checklistsScheduled,
      isShowScheduledTasks: (state) => state.schedules.isShowScheduledTasks,
    }),
    customers() {
      return this.checklist.customers.filter((item) => item.id);
    },
    resources() {
      return this.checklistsScheduled;
    },
    isScheduled() {
      return this.resources.includes(this.checklist.id);
    },
    isShowTask() {
      return !this.isShowScheduledTasks ? true : !this.isScheduled;
    },
    className() {
      return this.isPopover ? 'checklist-task-item pop-over' : 'checklist-task-item';
    },
    resourceId() {
      return this.checklist.id;
    },
    customers() {
      return _.get(this.checklist, 'customers', []);
    },
  },
};
</script>

<style lang="less">
.checklist-task-item {
  padding: 10px;
  border-top: 0.8px solid #f2f3f6;
  border-bottom: 0.8px solid #f2f3f6;
  line-height: 1.8;

  .task-content {
    height: 90%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    padding-left: 5px;
    padding-top: 5px;
    color: #cacccf;

    .value {
      color: #52575c;
    }

    .assign-to {
      display: inline-block;
      max-height: 60px;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.checklist-task-item.pop-over {
  line-height: unset;
}

.customer-list-container {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style>
