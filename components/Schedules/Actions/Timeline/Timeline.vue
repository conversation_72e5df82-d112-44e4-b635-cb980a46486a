<template>
  <div class="timeline-wrapper">
    <a-spin v-if="isLoading" />
    <div v-else class="timeline-container">
      <div class="timeline-table">
        <div class="month-picker">
          <fc-page-tip
            content="To navigate the calendar below, click and drag to pan and ctrl-scroll to zoom in and out."
          />
          <time-range
            :isRendered="isRendered"
            @initVisibleTimeline="initVisibleTimeline"
            @rangechange="handleRangeChange"
            class="time-range-picker"
          />
          <a-button
            icon="left"
            :disabled="isDisablePrev"
            @click="handleLoadPrevMonth"
            v-tooltip="{ content: 'Load Previous Month Schedules' }"
            style="margin-right: 5px"
          />
          <a-button
            icon="right"
            @click="handleLoadNextMonth"
            v-tooltip="{ content: 'Load Next Month Schedules' }"
            style="margin-right: 5px"
          />
          <a-month-picker
            v-model="currentMonth"
            placeholder="Select month"
            :disabledDate="disabledMonth"
            :allowClear="false"
            @change="handleSelectMonth"
            style="margin-left: 5px"
          />
        </div>
        <div class="top-actions">
          <div style="display: flex;">
            <a-button
              size="large"
              :icon="isCollapsed ? 'menu-unfold' : 'menu-fold'"
              v-tooltip="{ content: `${isCollapsed ? 'Show Tasks ' : 'Hide Tasks'}` }"
              @click="handleCollapseMenu"
              style="margin-right: 15px;"
            />
            <div>
              <a-button
                v-if="isShowHiddenResponder"
                size="large"
                icon="eye"
                @click="handleShowHiddenResponder"
                v-tooltip="{ content: 'Show Hidden Responders' }"
              >
              </a-button>
              <a-button
                v-else
                size="large"
                icon="eye-invisible"
                @click="handleHideHiddenResponder"
                v-tooltip="{
                  content: 'Hidden Responders are currently shown. Click to allow hiding of hidden Responders',
                }"
              >
              </a-button>
            </div>
          </div>
          <div style="margin-top: 10px">
            <fc-search
              class="fc-search"
              :placeholderText="'Search Responders'"
              @onInputSearchSubmit="handleInputSearchSubmit"
            />
          </div>
        </div>
        <div id="visualization"></div>
        <div class="popover-container" @mouseleave="handleBlurPopoverDetail">
          <a-popover
            v-model="isVisible"
            title="Task Detail"
            placement="leftBottom"
            :getPopupContainer="(trigger) => trigger.parentNode"
            style="display: none"
          >
            <template slot="content">
              <div v-if="isLoadingHover">
                <a-spin />
              </div>
              <div v-else>
                <request-task :request="taskDetailProp" :isPopover="true" v-if="taskDetailProp.kind == 'request'" />
                <checklist-task
                  :checklist="taskDetailProp"
                  :isPopover="true"
                  v-if="taskDetailProp.kind == 'checklist' || taskDetailProp.kind == 'pending_task'"
                />
              </div>
            </template>
          </a-popover>
        </div>
        <div class="popconfirm-container">
          <a-modal
            :visible="isVisiblePopConfirm"
            title="Confirm Assignment"
            @ok="handleConfirmAssign"
            @cancel="handleCancelAssign"
          >
            <span
              >This Responder has not been assigned to this {{ currentKind }}. Do you want to assign
              {{ currentResponder }} now?</span
            >
          </a-modal>
        </div>
        <div class="popremove-container">
          <a-modal
            :width="620"
            :visible="isVisiblePopRemove"
            title="Confirm Unassignment"
            @cancel="handleCancelUnassign"
          >
            <span
              >Are you sure you want to remove this {{ currentKind }} from the schedule of
              {{ currentResponder }} ?</span
            >
            <template slot="footer">
              <div class="schedules-unassign">
                <a-button key="back" @click="handleCancelUnassign">
                  Cancel
                </a-button>
                <a-button key="not_unassign" type="primary" @click="handleConfirmUnassign($event, false)">
                  Remove but do not unassign Responder
                </a-button>
                <a-button key="unassign" type="primary" @click="handleConfirmUnassign($event, true)">
                  Remove and unassign responder
                </a-button>
              </div>
            </template>
          </a-modal>
        </div>
        <div class="eye-tooltip">
          <a-tooltip
            placement="leftBottom"
            :visible="isVisibleTooltip"
            :getPopupContainer="(trigger) => trigger.parentNode"
            style="display: none"
          >
            <template slot="title">
              {{ promptText }}
            </template>
          </a-tooltip>
        </div>
      </div>
      <div class="schedules-annotation-container">
        <div class="request">
          <div class="request-box"></div>
          <span class="text">Requests</span>
        </div>
        <div class="checklist">
          <div class="checklist-box"></div>
          <span class="text">Checklists</span>
        </div>

        <!-- temporarily commented this feature -->
        <!-- <div class="pending">
          <div class="pending-box"></div>
          <span class="text">Auto-Scheduled</span>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { Timeline } from 'vis-timeline/peer';
import 'vis-timeline/styles/vis-timeline-graph2d.css';
import { mapState } from 'vuex';
import RSVP from 'rsvp';

import RequestTask from '../../Core/Tasks/Request/RequestTask.vue';
import ChecklistTask from '../../Core/Tasks/Checklist/ChecklistTask.vue';
import TimeRange from '../TimeRange.vue';
import { FILTER_REMEMBERED_PROPERTIES } from '@/utils/consts';

const { SCHEDULES } = FILTER_REMEMBERED_PROPERTIES;

export default {
  components: {
    RequestTask,
    ChecklistTask,
    TimeRange,
  },
  props: {
    isReady: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      items: [],
      groups: [],
      options: {
        editable: true,
        verticalScroll: true,
        zoomKey: 'ctrlKey',
        orientation: 'top',
        height: '65vh',
        min: '2023-01-01',
        groupOrder: 'order',
        groupHeightMode: 'fitItems',
        zoomMin: 10800000,
        zoomMax: 31104000000,
        groupEditable: { order: true },
        onMoveGroup: this.handleMoveGroup,
        groupOrderSwap: this.handleGroupOrderSwap,
        groupTemplate: this.handleGenGroupTemplate,
        template: this.handleGenTemplate,
        onAdd: this.handleAddTask,
        onMove: this.handleMoveTask,
        onRemove: this.handleRemoveTask,
      },
      timeline: undefined,
      isVisiblePopover: false,
      isLoadingHover: false,
      isVisiblePopConfirm: false,
      isVisiblePopRemove: false,
      isVisibleTooltip: false,
      promptText: '',
      isSelecting: false,
      dateFormat: 'YYYY-MM-DD',
      monthFormat: 'YYYY-MM',
      dateTimeFormat: 'YYYY-MM-DD HH:mm',
      taskDetail: {},
      isShowHiddenResponder: true,
      currentItem: {},
      currentResource: {},
      minDate: '2023-01-01',
      curDate: '',
      currentMonth: null,
      monthLoaded: [],
      startTime: '',
      endTime: '',
      isSetDefault: false,
      isRendered: false,
      SCHEDULES,
    };
  },
  mounted() {
    if (this.isReady) {
      this.initTimeline();
      this.isShowHiddenResponder = !!this.schedulesFilter.isShowHiddenResponder;
      this.handleHiddenResponder();
    }
  },
  methods: {
    initTimeline() {
      const container = document.getElementById('visualization');
      this.items = this.getItems();
      this.groups = this.getGroups();
      this.timeline = new Timeline(container, this.items, this.groups, this.options);
      this.setItems(this.items);
      this.setGroups(this.groups);
      this.timeline.on('drop', this.handleOnDrop);
      this.timeline.on('itemover', this.handleItemOver);
      this.timeline.on('itemout', this.handleItemOut);
      this.timeline.on('select', this.handleSelectItem);
      this.timeline.on('rangechanged', this.handleOnRangeChanged);
      this.timeline.on('doubleClick', this.handleOnDoubleClick);

      this.curDate = moment()
        .startOf('month')
        .format(this.dateFormat);
      this.currentMonth = moment(this.curDate, this.dateFormat);
      this.$store.dispatch('schedules/setIsLoading', false);
    },
    handleMoveGroup(event) {
      this.handleUpdateRespoderOrder();
    },
    handleGroupOrderSwap(a, b, group) {
      let v = a.order;
      a.order = b.order;
      b.order = v;

      let aIndex = this.groups.findIndex((item) => item.id === a.id);
      let bIndex = this.groups.findIndex((item) => item.id === b.id);

      this.groups[aIndex] = { ...this.groups[aIndex], ...{ order: a.order } };
      this.groups[bIndex] = { ...this.groups[bIndex], ...{ order: b.order } };
    },
    handleGenGroupTemplate(group) {
      let container = document.createElement('div');
      container.setAttribute('class', 'group-container');
      container.innerHTML = group.content;

      let visibleContainer = document.createElement('div');
      let visibleSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      let dragAndDropSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

      let disableViewPath =
        '<path d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"></path><path d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"></path>';
      let allowViewPath =
        '<path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"></path>';
      let dragAndDropPath = '<path d="M3 0L0 4H2V11H4V4H6L3 0Z"></path><path d="M10 1H8V8H6L9 12L12 8H10V1Z"></path>';

      let isOnline = group?.responder.availability_status == 'online';
      const titleContainer = document.createElement('div');
      titleContainer.innerHTML = `
        <div class="title-${group.id} title" >
          <span class="avatar">${group?.responder.name[0]}
            <div class="badge-container">
              <div id="statusBadge" class="${isOnline ? 'online' : 'offline'} badge"></div>
            </div>
          </span>
          <span class="name">${group?.responder.name}</span>
        </div>
      `;
      titleContainer.addEventListener('mouseover', (event) => {
        this.isVisibleTooltip = true;
        let text = isOnline ? 'User is Available' : 'User is Busy';
        this.handleShowTooltip(event, text);
      });
      titleContainer.addEventListener('mouseout', (event) => {
        this.isVisibleTooltip = false;
      });
      container.insertAdjacentElement('beforeEnd', titleContainer);

      visibleSvg.setAttribute('width', '20px');
      visibleSvg.setAttribute('height', '20px');
      visibleSvg.setAttribute('viewBox', '64 64 896 896');
      visibleSvg.setAttribute('class', `eye-${group.id} eye-icon`);
      visibleSvg.innerHTML = group.isVisible ? disableViewPath : allowViewPath;
      visibleSvg.addEventListener('mouseover', (event) => {
        this.isVisibleTooltip = true;
        let text = event.target.closest('.invisible-icon')
          ? 'This Responder is currently not hidden. Click to hide this Responder'
          : 'This Repsonder is currently hidden. Click to show this Responder';
        this.handleShowTooltip(event, text);
      });
      visibleSvg.addEventListener('mouseout', (event) => {
        this.isVisibleTooltip = false;
      });

      dragAndDropSvg.setAttribute('width', '20px');
      dragAndDropSvg.setAttribute('height', '20px');
      dragAndDropSvg.setAttribute('viewBox', '0 0 14 14');
      dragAndDropSvg.setAttribute('class', `dnd-${group.id} sort-icon`);
      dragAndDropSvg.innerHTML = dragAndDropPath;
      dragAndDropSvg.addEventListener('mouseover', (event) => {
        this.isVisibleTooltip = true;
        let text = 'Drag to re-order responders';
        this.handleShowTooltip(event, text);
      });
      dragAndDropSvg.addEventListener('mouseout', (event) => {
        this.isVisibleTooltip = false;
      });

      visibleContainer.setAttribute('class', group.isVisible ? 'invisible-icon' : 'visible-icon');
      visibleContainer.insertAdjacentElement('beforeEnd', visibleSvg);
      visibleContainer.insertAdjacentElement('beforeEnd', dragAndDropSvg);
      visibleContainer.addEventListener('click', (event) => {
        if (event.target.closest('.invisible-icon')) {
          this.handleUpdateResponderView(group.id, 'disable');
        } else {
          this.handleUpdateResponderView(group.id, 'allow');
        }
      });
      container.insertAdjacentElement('beforeEnd', visibleContainer);
      return container;
    },
    handleGenTemplate(item, element, data) {
      let color = { pending: '#f5e342', processing: '#42e0f5', complete: '#52c41a', canceled: '#ff1803' };
      let content = item.content ? item.content : item.resource_id;
      let container = document.createElement('div');
      let contentHtml = document.createElement('span');
      contentHtml.innerHTML = `${content}`;
      if (item.status && color[`${item.status}`]) {
        let customIcon = document.createElement('div');
        customIcon.style.setProperty('width', '8px');
        customIcon.style.setProperty('height', '8px');
        customIcon.style.setProperty('border-radius', '10px');
        customIcon.style.setProperty('background-color', color[`${item.status}`]);
        customIcon.style.setProperty('margin-right', '2px');
        container.insertAdjacentElement('beforeEnd', customIcon);
      }
      container.insertAdjacentElement('beforeEnd', contentHtml);
      container.style.setProperty('display', 'flex');
      container.style.setProperty('align-items', 'center');
      return container;
    },
    handleAddTask(item, callback) {
      if (item?.type == 'range') {
        this.currentItem = { ...item, ...{ end: moment(item.start).add(this.taskPeriod, 'hours') } };
        this.handleCheckAssignment(item)
          .then((res) => {
            this.isVisiblePopConfirm = true;
          })
          .catch((err) => {
            this.handleAssignTask(this.currentItem);
          });
      }
      callback(null);
    },
    handleMoveTask(item) {
      if (item.group != item.responder_id) {
        this.handleReassignTask(item);
      } else {
        this.reScheduleTask(item);
      }
    },
    handleRemoveTask(item, callback) {
      this.isVisiblePopover = false;

      this.isVisiblePopRemove = true;
      this.currentItem = { ...item };
    },
    handleOnDrop(event) {},
    handleItemOver({ event, item }) {
      this.isVisiblePopover = true;
      let itemType = typeof item;
      let task =
        itemType == 'number'
          ? this.tasks.find((t) => t.id == item)
          : itemType == 'string'
          ? {
              responder_id: item.split('-')[0],
              resource_id: item.split('-')[1],
              kind: 'pending_task',
            }
          : undefined;

      task &&
        this.handleGetItemDetail(task.resource_id, task.kind)
          .then((res) => {
            this.taskDetail = { ...res, ...{ kind: task.kind } };
            setTimeout(() => {
              let container = document.querySelector('.features-schedules-container');
              let popOver = document.querySelector('.popover-container .ant-popover');
              let item = document.querySelector(`.item-${task.responder_id}-${task.resource_id}-${task.kind}`);
              let itemRect = item.getBoundingClientRect();
              let containerRect = container.getBoundingClientRect();

              let itemStyles = getComputedStyle(item);
              let matrix = new WebKitCSSMatrix(itemStyles.transform);

              let left = itemRect.left - containerRect.left - popOver.offsetWidth;
              left = matrix.m41 < 0 ? left - matrix.m41 : left;
              let top = itemRect.top - containerRect.top - popOver.offsetHeight;
              popOver.style.setProperty('left', `${left}px`, 'important');
              popOver.style.setProperty('top', `${top}px`, 'important');
            }, 200);
          })
          .catch((err) => {
            this.$message.error(err.response.data.message);
          });
    },
    handleItemOut({ event, item }) {
      // this.isVisiblePopover = false;
    },
    handleBlurPopoverDetail() {
      this.isVisiblePopover = false;
    },
    handleSelectItem({ event, item }) {
      this.isSelecting = event.firstTarget.className == 'vis-item-overflow' ? true : false;
    },
    async handleOnRangeChanged(event) {
      await this.handleResetMaxHeight();
      this.handleAlignLegend();

      if (!this.isSetDefault) {
        this.setDefaultWindow();
        this.isRendered = true;
        this.isSetDefault = true;
      } else {
        this.$store.dispatch('schedules/updateVisibleWndow', {
          start_window: moment(event.start).format(this.dateTimeFormat),
          end_window: moment(event.end).format(this.dateTimeFormat),
        });
      }

      this.handleLoadTaskInWindow(event.start, event.end);
    },
    handleOnDoubleClick(event) {
      if (event.what?.length && event.what == 'item' && event.item) {
        let item = this.items.find((item) => item.id == event.item);
        let hrefLink =
          item.kind == 'request'
            ? `/features/requests?request_ids=${JSON.stringify([item.resource_id])}`
            : `/features/checklists?ids=${JSON.stringify([item.resource_id])}`;

        let el = document.createElement('a');
        el.setAttribute('href', hrefLink);
        el.setAttribute('target', '_blank');
        el.setAttribute('rel', 'noopener nofollow noreferrer');
        el.click();
      }
    },
    handleCheckAssignment(task) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('schedules/checkAssignment', {
            responder_id: task.group,
            resource_id: task.resource_id,
            kind: task.kind,
          })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject();
          });
      });
    },
    handleCheckScheduling(task) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('schedules/checkScheduling', {
            responder_id: task.group,
            resource_id: task.resource_id,
            kind: task.kind,
          })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            this.$message.error(err.response.data.message);
            reject();
          });
      });
    },
    handleAssignTask(task) {
      this.$store
        .dispatch('schedules/assignTask', {
          responder_id: task.group,
          resource_id: task.resource_id,
          kind: task.kind,
          start_period: moment(task.start).format(this.dateTimeFormat),
          end_period: moment(task.end).format(this.dateTimeFormat),
          content: task.content,
        })
        .then((res) => {
          this.$message.success('Scheduled successfully.');
          this.handleRefreshTasks(task);
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleUnassignTask(task) {
      this.$store
        .dispatch('schedules/unAssignTask', {
          responder_id: task.responder_id,
          task_id: task.task_id,
        })
        .then((res) => {
          this.$message.success('Descheduled successfully.');
          this.isVisiblePopRemove = false;
          this.handleRefreshTasks(task);
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleReassignTask(task) {
      RSVP.all([
        this.$store.dispatch('schedules/unAssignTask', {
          responder_id: task.responder_id,
          task_id: task.task_id,
        }),
        this.$store.dispatch('schedules/assignTask', {
          responder_id: task.group,
          resource_id: task.resource_id,
          kind: task.kind,
          start_period: moment(task.start).format(this.dateTimeFormat),
          end_period: moment(task.end).format(this.dateTimeFormat),
          content: task.content,
        }),
      ]).then((res) => {
        this.$message.success('Reassigned successfully.');
        this.handleRefreshTasks(task);
      });
    },
    reScheduleTask(task) {
      this.$store
        .dispatch('schedules/reScheduleTask', {
          id: task.task_id,
          payload: {
            responder_id: task.group,
            resource_id: task.resource_id,
            kind: task.kind,
            start_period: moment(task.start).format(this.dateTimeFormat),
            end_period: moment(task.end).format(this.dateTimeFormat),
          },
        })
        .then((res) => {
          this.$message.success('Rescheduled successfully.');
          this.handleRefreshTasks(task);
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleGetItemDetail(id, kind) {
      return new RSVP.Promise((resolve, reject) => {
        this.isLoadingHover = true;
        let dispatch = kind == 'request' ? 'schedules/loadRequest' : 'schedules/loadChecklist';
        this.$store
          .dispatch(dispatch, { id: id, payload: {} })
          .then((res) => {
            this.isLoadingHover = false;
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    handleUpdateResponderView(id, type) {
      let ids = type == 'disable' ? [...this.disableViewIds, id] : this.disableViewIds.filter((item) => item != id);

      this.$store
        .dispatch('schedules/updateResponderDisableView', {
          responder_allow_view_ids: JSON.stringify(ids),
        })
        .then((res) => {
          this.$message.success('Updated successfully.');
          this.handleRefreshGroups(this.isShowHiddenResponder);
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleUpdateRespoderOrder() {
      let orders = this.groups.map((item) => ({
        id: item.id,
        order: item.order,
      }));

      this.$store
        .dispatch('schedules/updateResponderOrder', {
          responder_order: JSON.stringify(orders),
        })
        .then((res) => {
          this.$message.success('Order Changed Successfully.');
          this.handleRefreshGroups(this.isShowHiddenResponder);
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleRefreshTasks(task) {
      this.$store
        .dispatch('schedules/loadMoreTasks', {
          start_date: moment(task.start)
            .startOf('month')
            .format(this.dateFormat),
          end_date: moment(task.end)
            .endOf('month')
            .format(this.dateFormat),
        })
        .then((res) => {
          this.$store.dispatch('schedules/loadChecklistScheduled');
          this.$store.dispatch('schedules/loadRequestsScheduled');
          this.items = this.getItems();
          this.setItems(this.items);
        });
    },
    handleRefreshGroups(isHide) {
      this.$store.dispatch('schedules/loadTaskSchedulingConfig').then((res) => {
        this.groups = this.getGroups(isHide);
        this.setGroups(this.groups);
      });
    },
    getItems() {
      return this.tasks.map((item, index) => ({
        ...item,
        id: item.kind == 'pending_task' ? `${item.responder_id}-${item.resource_id}` : item.id,
        task_id: item.id,
        group: item.responder_id,
        editable: item.kind == 'pending_task' ? { remove: true, updateGroup: false, updateTime: false } : true,
        start: moment.unix(item.start_period).format(this.dateTimeFormat),
        end:
          item.kind == 'pending_task'
            ? moment.unix(item.start_period).add(this.taskPeriod, 'hours')
            : moment.unix(item.end_period).format(this.dateTimeFormat),
        className: `item-${item.responder_id}-${item.resource_id}-${item.kind}`,
        style:
          item.kind == 'request'
            ? 'background-color: rgb(255, 99, 55)'
            : item.kind == 'checklist'
            ? 'background-color: #007eff'
            : 'background-color: #feb700',
      }));
    },
    setItems(items) {
      this.timeline.setItems(items);
    },
    getGroups(isHide = true) {
      let responderOrders = this.responderOrders.map((item) => item.order).filter((item) => item);
      let maxOrder = responderOrders.length ? Math.max(...responderOrders) : 0;
      let groups = this.responders.map((item, index) => {
        let isOnline = item.availability_status == 'online' ? true : false;
        let obj = {
          id: item.id,
          content: ``,
          className: 'group-item',
          isVisible: this.disableViewIds.includes(item.id) ? false : true,
          visible: true,
          ...(isHide && { visible: this.disableViewIds.includes(item.id) ? false : true }),
        };
        let orderItem = this.responderOrders.find((o) => o.id == item.id);
        let isExistOrder = orderItem && orderItem?.order;

        maxOrder = isExistOrder ? maxOrder : maxOrder + 1;
        return isExistOrder
          ? { ...obj, ...{ order: orderItem.order }, responder: item }
          : { ...obj, ...{ order: maxOrder }, responder: item };
      });
      return groups;
    },
    async setGroups(groups) {
      if (this.timeline) {
        await this.handleResetMaxHeight();
        await this.handleSetTimelineHeight(groups);
        this.handleAlignLegend();
      }
    },
    handleShowTooltip(event, value) {
      setTimeout(() => {
        let isHover =
          event.target.closest('.eye-icon') || event.target.closest('.sort-icon') || event.target.closest('.title');
        if (isHover) {
          let className = isHover.className.baseVal || isHover.className;
          let container = document.querySelector('.features-schedules-container');
          let containerRect = container.getBoundingClientRect();
          let item = document.querySelector(`.${className.split(' ')[0]}`);
          let itemRect = item.getBoundingClientRect();

          let tooltip = document.querySelector('.eye-tooltip .ant-tooltip');
          let left = itemRect.left - containerRect.left - tooltip.offsetWidth;
          let top = itemRect.top - containerRect.top - 10;
          tooltip.style.setProperty('left', `${left}px`, 'important');
          tooltip.style.setProperty('top', `${top}px`, 'important');
          this.promptText = value;
        }
      }, 50);
    },
    handleInputSearchSubmit(value) {
      this.$store.dispatch('schedules/loadAllResponder', {
        q: value,
        role: 'staff',
      });
    },
    handleHiddenResponder() {
      this.groups = this.getGroups(this.isShowHiddenResponder);
      this.setGroups(this.groups);
    },
    handleShowHiddenResponder() {
      this.isShowHiddenResponder = false;
      this.groups = this.getGroups(false);
      this.setGroups(this.groups);
      this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
        property: FILTER_REMEMBERED_PROPERTIES.SCHEDULES,
        value: {
          ...this.schedulesFilter,
          isShowHiddenResponder: this.isShowHiddenResponder,
        },
      });
    },
    handleHideHiddenResponder() {
      this.isShowHiddenResponder = true;
      this.groups = this.getGroups(true);
      this.setGroups(this.groups);
      this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
        property: FILTER_REMEMBERED_PROPERTIES.SCHEDULES,
        value: {
          ...this.schedulesFilter,
          isShowHiddenResponder: this.isShowHiddenResponder,
        },
      });
    },
    handleCollapseMenu() {
      this.$store.dispatch('schedules/setIsCollapsed', !this.isCollapsed);
    },
    handleGetResource(task, type) {
      return new Promise((resolve, reject) => {
        if (type == 'add') resolve({ assigned_to: task.assigned_to });

        let isRequest = task.kind == 'request';
        let dispatch = isRequest ? 'schedules/loadRequest' : 'schedules/loadChecklist';
        this.$store
          .dispatch(dispatch, {
            id: task.resource_id,
            payload: {},
          })
          .then((res) => {
            if (isRequest) resolve({ assigned_to: res.assign_manager_ids });
            resolve({
              assigned_to: res.assigned_staff_ids,
              completed_by: res.completed_by,
            });
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    handleAssignToResource(task, type) {
      if (task.kind == 'request') {
        this.handleAssignRequestResource(task, type);
      } else {
        this.handleAssignChecklistResource(task, type);
      }
    },
    handleAssignRequestResource(task, type) {
      this.handleGetResource(task, type).then((res) => {
        let ids = res.assigned_to;
        ids = type == 'add' ? [...ids, task.group] : ids.filter((item) => item != task.group);
        const formData = new FormData();
        formData.append('manager_ids', JSON.stringify(ids));
        const payload = {
          id: task.resource_id,
          formData: formData,
        };

        this.$store
          .dispatch('requests/assignManagerRequest', payload)
          .then(() => {
            this.$store.dispatch('schedules/loadAllRequests', { statuses: JSON.stringify(['pending']) });
          })
          .catch((error) => {
            this.$message.error(error.response.data.message);
          });
      });
    },
    handleAssignChecklistResource(task, type) {
      this.handleGetResource(task, type).then((res) => {
        let ids = res.assigned_to;
        ids = type == 'add' ? [...ids, task.group] : ids.filter((item) => item != task.group);

        this.$store
          .dispatch('checklists/assignStaff', {
            id: task.resource_id,
            payload: {
              staff_ids: JSON.stringify(ids),
            },
          })
          .then(() => {
            this.$store.dispatch('schedules/loadAllChecklists', { tab: 'pending' });
          })
          .catch((err) => {
            this.$message.error(err.response.data.message);
          });
      });
    },
    handleCancelAssign() {
      this.isVisiblePopConfirm = false;
      this.currentItem = {};
    },
    handleConfirmAssign() {
      this.handleAssignToResource(this.currentItem, 'add');
      this.handleAssignTask(this.currentItem);
      this.isVisiblePopConfirm = false;
    },
    handleCancelUnassign() {
      this.isVisiblePopRemove = false;
      this.currentItem = {};
    },
    handleConfirmUnassign(event, is_unassign) {
      if (this.currentItem.kind == 'pending_task') {
        this.$store
          .dispatch('checklists/assignStaff', {
            id: this.currentItem.resource_id,
            payload: {
              staff_ids: JSON.stringify(
                this.currentItem.assigned_staff_ids.filter((item) => item != this.currentItem.responder_id),
              ),
            },
          })
          .then((res) => {
            this.isVisiblePopRemove = false;
            this.$message.success('Unassigned successfully.');
            this.handleRefreshTasks();
          })
          .catch((err) => {
            this.$message.error(err.response.data.message);
          });
      } else {
        this.handleUnassignTask(this.currentItem);
        is_unassign && this.handleAssignToResource(this.currentItem, 'remove');
      }
    },
    handleResetMaxHeight() {
      return new Promise((resolve, reject) => {
        let vslz = document.getElementById('visualization');
        vslz.style.setProperty('max-height', 'unset');
        resolve();
      });
    },
    handleSetTimelineHeight(groups) {
      return new Promise((resolve, reject) => {
        this.timeline.setGroups(groups);
        let visibleGroups = this.groups.filter((item) => item.visible);
        let height = visibleGroups.length <= 0 ? 300 : 0;
        this.timeline.setOptions({ minHeight: `${height}px` });
        resolve();
      });
    },
    handleAlignLegend() {
      let legend = document.querySelector('.schedules-annotation-container');
      legend.style.setProperty('display', 'none');
      setTimeout(() => {
        let vslz = document.getElementById('visualization');

        //minus the transformY: -80px
        let maxHeight = vslz.offsetHeight - 80;
        vslz.style.setProperty('max-height', `${maxHeight}px`);
        legend.style.setProperty('display', 'flex');
      }, 50);
    },
    disabledMonth(value) {
      return value < moment(this.minDate, this.dateFormat);
    },
    handleLoadPrevMonth() {
      let dateObj = moment(this.curDate, this.dateFormat).subtract(1, 'month');
      this.curDate = dateObj.format(this.dateFormat);
      this.currentMonth = moment(this.curDate, this.dateFormat);
      let endDate = dateObj.endOf('month').format(this.dateFormat);
      this.timeline.setWindow(this.curDate, endDate, { animation: true }, () => {});

      this.handleLoadTaskInRange(this.curDate, endDate);
    },
    handleLoadNextMonth() {
      let dateObj = moment(this.curDate, this.dateFormat).add(1, 'month');
      this.curDate = dateObj.format(this.dateFormat);
      this.currentMonth = moment(this.curDate, this.dateFormat);
      let endDate = dateObj.endOf('month').format(this.dateFormat);
      this.timeline.setWindow(this.curDate, endDate, { animation: true }, () => {});

      this.handleLoadTaskInRange(this.curDate, endDate);
    },
    handleSelectMonth(date, dateString) {
      this.curDate = date.startOf('month').format(this.dateFormat);
      this.currentMonth = moment(this.curDate, this.dateFormat);
      let endDate = date.endOf('month').format(this.dateFormat);
      this.timeline.setWindow(this.curDate, endDate, { animation: true }, () => {});
      this.handleLoadTaskInRange(this.curDate, endDate);
    },
    handleLoadTaskInRange(startDate, endDate) {
      let isLoaded = this.monthLoaded.includes(moment(startDate, this.dateFormat).format('YYYY-MM'));
      !isLoaded &&
        this.$store
          .dispatch('schedules/loadMoreTasks', {
            start_date: startDate,
            end_date: endDate,
          })
          .then((res) => {
            this.items = this.getItems();
            this.setItems(this.items);
            this.monthLoaded.push(moment(startDate, this.dateFormat).format('YYYY-MM'));
          });
    },
    setDefaultWindow() {
      let curDay, nextDay;
      if (this.isValidWindowRange) {
        curDay = this.currentWindow.start_window;
        nextDay = this.currentWindow.end_window;
      } else {
        curDay = moment()
          .startOf('day')
          .format(this.dateFormat);
        nextDay = moment()
          .startOf('day')
          .add(1, 'week')
          .format(this.dateFormat);
      }

      this.timeline.setWindow(curDay, nextDay, { animation: true }, () => {});
    },
    initVisibleTimeline(value) {
      let startHidden = moment()
        .set('hour', value.endHour)
        .set('minute', 0)
        .format(this.dateTimeFormat);
      let endHidden = moment()
        .add(1, 'day')
        .set('hour', value.startHour)
        .set('minute', 0)
        .format(this.dateTimeFormat);
      this.timeline.setOptions({
        hiddenDates: {
          start: startHidden,
          end: endHidden,
          repeat: 'daily',
        },
      });
    },
    handleRangeChange(value) {
      this.initVisibleTimeline(value);
      this.$store
        .dispatch('schedules/updateVisibleTimeline', {
          start_visible: value.startHour,
          end_visible: value.endHour,
        })
        .then((res) => {
          this.$message.success('Update Visible Times Succesfully.');
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleLoadTaskInWindow(startRange, endRange) {
      let curWindow = moment(startRange).startOf('month');
      let endWindow = moment(endRange).startOf('month');

      let monthDiff = endWindow.diff(curWindow, 'month');
      if (monthDiff > 0) {
        (async () => {
          for (let i = 0; i <= monthDiff; i++) {
            let startDate = curWindow.format(this.dateFormat);
            let endDate = curWindow
              .clone()
              .endOf('month')
              .format(this.dateFormat);
            await this.handleLoadTaskInRange(startDate, endDate);
            curWindow.add(1, 'month');
          }
        })();
      }
    },
  },
  computed: {
    ...mapState({
      currentRequest: (state) => state.schedules.currentRequest,
      responders: (state) => state.schedules.responders,
      tasks: (state) => state.schedules.tasks,
      requests: (state) => state.schedules.requests,
      checklists: (state) => state.schedules.checklists,
      schedulingConfig: (state) => state.schedules.schedulingConfig,
      isLoading: (state) => state.schedules.isLoading,
      isCollapsed: (state) => state.schedules.isCollapsed,
      schedulesFilter: (state) => state.filter_remembered[SCHEDULES],
    }),
    isVisible() {
      return this.isVisiblePopover && !this.isSelecting;
    },
    taskDetailProp() {
      return this.taskDetail;
    },
    disableViewIds() {
      return this.schedulingConfig.responder_allow_view_ids;
    },
    responderOrders() {
      return this.schedulingConfig.responder_order;
    },
    taskPeriod() {
      return this.schedulingConfig.task_period;
    },
    currentResponder() {
      return Object.keys(this.currentItem).length
        ? this.responders.find((item) => item.id == this.currentItem.group)?.name
        : 'responder';
    },
    currentKind() {
      return Object.keys(this.currentItem).length && this.currentItem.kind != 'pending_task'
        ? this.currentItem.kind
        : 'checklist';
    },
    isDisablePrev() {
      return this.currentMonth
        ? this.currentMonth.format(this.monthFormat) == moment(this.minDate, this.dateFormat).format(this.monthFormat)
        : true;
    },
    currentWindow() {
      return this.schedulingConfig.visible_window;
    },
    startWindow() {
      return this.currentWindow.start_window;
    },
    endWindow() {
      return this.currentWindow.end_window;
    },
    isValidWindowRange() {
      return this.startWindow && this.endWindow;
    },
  },
  watch: {
    responders(newVal, oldVal) {
      if (this.isReady) {
        this.groups = this.getGroups(!this.isShowHiddenResponder);
        this.setGroups(this.groups);
      }
    },
  },
};
</script>

<style lang="less">
#visualization {
  border-radius: 8px;
  border-color: #e8e8e8 !important;
  transform: translateY(-80px);

  .vis-background {
    background: #ffffff;
  }

  .vis-panel.vis-background.vis-horizontal {
    background: unset;
  }

  .vis-panel.vis-background.vis-vertical {
    background: unset;
  }

  .vis-timeline {
    border-radius: 8px;
    border: 8px solid white;
    border-top: 0px;
    border-bottom: 2px solid white;
    border-left: 8px solid white;
    border-right: 8px solid white;
  }

  .vis-labelset {
    .group-item {
      min-height: 80px;
      background: white;
    }

    .vis-label {
      // width: 350px !important;
      border-bottom: 2px solid #e8e8e8;

      .vis-inner {
        height: 100%;
        width: 100%;

        .group-container {
          height: inherit;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            display: flex;
            align-items: center;
            height: 100%;
            .badge-container {
              position: absolute;
              bottom: 0;
              right: 0;
              transform: translate(50%, 50%);
            }
            .badge {
              display: inline-block;
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #000; /* Black filled circle */
            }

            /* Styles for the online state */
            .online {
              background-color: #4caf50; /* Green filled circle for online */
            }

            /* Styles for the offline state */
            .offline {
              background-color: transparent; /* Black filled circle */
              border: #000 2px solid;
            }
            .name {
              margin-left: 15px;
              max-width: 160px;
              word-wrap: break-word;
              display: flex;
              align-items: center;
              overflow-x: auto;
            }
            .avatar {
              display: flex;
              width: 32px;
              height: 32px;
              border-radius: 50%;
              font: 12px / 32px Sarabun;
              align-items: center;
              justify-content: center;
              text-align: center;
              user-select: none;
              background-color: rgb(248, 150, 30);
              color: rgb(255, 255, 255);
              position: relative;
            }
          }

          .visible-icon,
          .invisible-icon {
            height: 100%;
            margin-right: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
          }
        }
      }
    }
  }

  .vis-content {
    &::after {
      content: ' ';
      display: block;
      height: 40px;
      background-color: transparent;
    }
  }

  .vis-itemset {
    .group-item {
      min-height: 80px;
      border-bottom: 1px solid #e8e8e8;
    }

    .vis-background {
      background: unset;
    }
  }

  .vis-panel.vis-top {
    background: white;
    border-color: #e8e8e8;
  }

  .vis-panel.vis-center {
    border-color: #e8e8e8;
  }

  .vis-panel.vis-left {
    border-color: #e8e8e8;
    width: 250px !important;
    height: calc(100% - 107px) !important;
    transition: width 0.2s ease-in-out;
  }

  .vis-time-axis {
    .vis-major {
      font-size: 18px;
      height: 80px !important;
      background: white;
      text-align: center;
      display: flex;
      align-items: center;
      border-left: 0.8px solid #e8e8e8;

      div {
        margin-left: 15px;
      }
    }
    .vis-minor.vis-odd {
      background: ghostwhite;
    }
    .vis-minor.vis-even {
      background: white;
    }
    .vis-minor.vis-h0-h4 {
      background: white;
    }
  }

  .vis-item {
    border-radius: 8px;
    border: 0px;
    color: white;

    a {
      color: inherit;
    }
  }
}

.ant-popover-inner-content {
  padding: 6px 8px;

  .request-task-item,
  .checklist-task-item {
    border-top: 0px;
    border-bottom: 0px;
    padding: 5px;
    height: auto;
    max-width: 250px;

    .assign-to {
      display: inline !important;
      max-height: unset !important;
      overflow-y: unset !important;
    }
  }
}

.schedules-unassign {
  display: flex;
}
</style>
