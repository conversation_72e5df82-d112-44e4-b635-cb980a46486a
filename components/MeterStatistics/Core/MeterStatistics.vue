<template>
  <div class="statistics-mqtt-container">
    <a-tabs
      :defaultActiveKey="currentTab"
      :activeKey="currentTab"
      :destroyInactiveTabPane="true"
      id="mqtt-data-statistics-tab"
      @change="onChangeTab"
    >
      <a-tab-pane tab="Aggregated" key="tracking">
        <tracking />
      </a-tab-pane>
      <a-tab-pane tab="Raw" key="meter_data">
        <meter-data />
      </a-tab-pane>
      <a-tab-pane tab="Chart" key="chart">
        <chart />
      </a-tab-pane>
      <a-tab-pane key="chart_generator" v-if="enable_generative_ai">
        <template #tab>
          <span>
            Chart Generator
            <a-tag color="orange">Beta</a-tag>
          </span>
        </template>
        <chart-generator />
      </a-tab-pane>
    </a-tabs>

    <multiple-meter />
  </div>
</template>

<script>
import './index.less';
import MeterData from './Meter/Meter.vue';
import Tracking from './Tracking/Tracking.vue';
import Chart from './Chart/Chart.vue';
import ChartGenerator from './ChartGenerator/ChartGenerator.vue';
import MultipleMeter from './Meter/MeterStatisticsChart/MultipleMeter.vue';

import { mapState } from 'vuex';

export default {
  components: {
    MeterData,
    Tracking,
    Chart,
    ChartGenerator,
    MultipleMeter,
  },
  methods: {
    onChangeTab(key) {
      switch (key) {
        case 'tracking':
          this.$router.push({
            path: this.$route.path,
            query: {
              tab: 'tracking',
            },
          });
          break;
        case 'meter_data':
          this.$router.push({
            path: this.$route.path,
            query: {
              tab: 'meter_data',
            },
          });
          break;
        case 'chart':
          this.$router.push({
            path: this.$route.path,
            query: {
              tab: 'chart',
            },
          });
          break;
        case 'chart_generator':
          this.$router.push({
            path: this.$route.path,
            query: {
              tab: 'chart_generator',
            },
          });
          break;
      }
      this.$store.dispatch('meter_statistics/setCurrentTab', key);
    },
    to() {
      this.$router.push('/integrations/mqtt_integrations');
    },
  },
  computed: {
    ...mapState({
      currentTab: (state) => state.meter_statistics.currentTab,
      isVisibleMeterChart: (state) => state.meter_statistics.isVisibleMeterChart,
      selectedMeter: (state) => state.meter_statistics.selectedMeter,
      dateRange: (state) => state.meter_statistics.dateRange,
      enable_generative_ai: (state) => state.managers.profile?.agent_management?.enable_generative_ai,
    }),
  },
  watch: {
    isVisibleMeterChart(newVal, oldVal) {
      if (!newVal) {
        this.$store.dispatch('meter_statistics/setChartType', 'reading_timeseries');
      }
    },
  },
};
</script>

<style lang="less">
.statistics-mqtt-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: auto;

  #mqtt-data-statistics-tab {
    .ant-tabs-extra-content {
      padding: 6px 30px 0 0;
    }

    .ant-tabs-tab {
      padding-top: 18px;
      margin-bottom: 8px;
    }
  }
}
</style>
