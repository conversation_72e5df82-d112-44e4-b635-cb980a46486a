<template>
  <a-layout-sider
    v-model="collapsed"
    :trigger="null"
    collapsible
    :theme="theme"
    :width="244"
    :style="{
      overflow: 'auto',
      height: '100vh',
      position: 'sticky',
      left: 0,
      top: 0,
    }"
    :class="foldClass"
  >
    <div class="fc_portal--logo">
      <transition name="slide-fade">
        <div class="fc_portal--logo-title" v-if="!collapsed">
          <p class="fc_portal--logo-title-main">
            <span>{{ $auth.user.role }}</span>
            <span>Portal</span>
          </p>
          <p class="fc_portal--logo-title-sub">
            {{ $auth.user.agent_name }}
          </p>
        </div>
      </transition>
      <div class="fc_portal--logo-collapsed">
        <a-icon
          class="trigger"
          :component="collapsed ? SideMenuFoldIcon : SideMenuUnFoldIcon"
          @click="handleSideMenuClick"
        />
      </div>
    </div>
    <a-menu
      :theme="theme"
      mode="inline"
      class="fc_portal--menu"
      :selectedKeys="default_menu_selected"
      v-if="isCustomRole"
    >
      <a-menu-item v-for="item in nav_items" :key="item.key" :class="item.key === 'sign_out' ? 'fc-menu-space' : ''">
        <nuxt-link :to="item.path">
          <a-icon :component="item.icon" />
          <span>{{ item.name }}</span>
        </nuxt-link>
      </a-menu-item>
    </a-menu>
    <a-menu
      :theme="theme"
      mode="inline"
      class="fc_portal--menu"
      :selectedKeys="default_menu_selected"
      v-else-if="$auth.user.agent_active"
    >
      <a-menu-item v-for="item in nav_items" :key="item.key" :class="item.key === 'settings' ? 'fc-menu-space' : ''">
        <nuxt-link :to="item.path">
          <a-icon :component="item.icon" style="display: inline-block; vertical-align: center;" />
          <span>{{ item.name }}</span>
        </nuxt-link>
      </a-menu-item>
    </a-menu>
    <a-menu :theme="theme" mode="inline" class="fc_portal--menu" :selectedKeys="default_menu_selected" v-else>
      <a-menu-item
        v-for="item in NAV_NOT_ACTIVE_ACCOUNT"
        :key="item.key"
        :class="item.key === 'sign_out' ? 'fc-menu-space' : ''"
      >
        <nuxt-link :to="item.path">
          <a-icon :component="item.icon" />
          <span>{{ item.name }}</span>
        </nuxt-link>
      </a-menu-item>
    </a-menu>
  </a-layout-sider>
</template>
<script>
import { SideMenuFoldIcon, SideMenuUnFoldIcon, RequestIcon, ExpendituresIcon } from '~/components/Icons/index.js';
import { mapState } from 'vuex';
import { SignOutIcon } from '~/components/Icons/index.js';
import { NAV_NOT_ACTIVE_ACCOUNT } from '../../../utils/meta_data';
import { routes_by_role } from '../../../utils/routes';
export default {
  data() {
    return {
      collapsed: true,
      SideMenuFoldIcon,
      SideMenuUnFoldIcon,
      RequestIcon,
      ExpendituresIcon,
      theme: 'dark',
      NAV_NOT_ACTIVE_ACCOUNT,
    };
  },
  components: {
    SignOutIcon,
  },
  computed: {
    foldClass: function() {
      return this.collapsed ? 'fc-sider--fold' : 'fc-sider--unfold';
    },
    ...mapState({
      nav_items: (state) => state.menu.nav_items,
      current_menu: (state) => state.menu.current_menu,
      default_page_redirect: (state) => state.menu.default_page_redirect,
    }),
    default_menu_selected() {
      if (!this.current_menu) return [this.default_page_redirect['manager']];
      return Object.keys(this.current_menu).length ? [this.current_menu.key] : [this.default_page_redirect['manager']];
    },
    userInfo() {
      return this.$auth.user;
    },
    isCustomRole() {
      return this.userInfo.custom_permissions && Object.keys(this.userInfo.custom_permissions).length;
    },
  },
  watch: {
    userInfo(newValue, oldValue) {
      this.$store.dispatch('menu/findNavItems', {
        role: newValue.role,
        is_custom_role: this.isCustomRole,
        available_role_routes: this.routes_by_role(newValue),
        agent_management: newValue.agent_management,
        feature_config: newValue.feature_config,
      });
    },
  },
  mounted() {
    this.$store.dispatch('menu/findNavItems', {
      role: this.$auth.user.role,
      is_custom_role: this.isCustomRole,
      available_role_routes: this.routes_by_role(this.$auth.user),
      agent_management: this.$auth.user.agent_management,
      feature_config: this.$auth.user.feature_config,
    });
  },
  methods: {
    routes_by_role,
    handleSideMenuClick() {
      this.collapsed = !this.collapsed;
      this.$store.dispatch('menu/setSiderWidth', this.collapsed);
    },
    onBreakpoint(broken) {
      this.$store.dispatch('menu/setSiderWidth', broken);
    },
  },
};
</script>
<style lang="less">
.fc-sider-bottom {
  position: absolute !important;
  bottom: 1em;
  right: 0;
}
.fc_portal--logo {
  background-color: #53575b;
}
.fc-sider--fold {
  .fc_portal--logo-collapsed {
    -webkit-transition: background-color 0.5s linear;
    -ms-transition: background-color 0.5s linear;
    transition: background-color 0.5s linear;
  }
  .ant-menu-dark.ant-menu-vertical .ant-menu-item {
    border-radius: 8px;
    margin: 0 20px 4px 20px;
    padding: 0px 12px !important;
    margin-bottom: 4px;
    transition: none;
  }
  .ant-menu.ant-menu-dark .ant-menu-item-selected {
    border-radius: 8px;
    margin: 0 20px 4px 20px;
    padding: 0px 12px !important;
  }
}

.fc-sider--unfold {
  .ant-menu.ant-menu-dark .ant-menu-item {
    border-radius: 8px;
    margin: 0 12px;
    width: auto;
    margin-bottom: 4px;
    padding-left: 12px !important;
    transition: none;
    transform: none;
  }
  .ant-menu.ant-menu-dark .ant-menu-item-selected {
    border-radius: 8px;
    margin: 0 12px;
    width: auto;
    margin-bottom: 4px;
    padding-left: 12px !important;
  }
}

.fc_portal--logo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;

  .fc_portal--logo-title {
    margin-left: 2em;
    width: 100%;

    .fc_portal--logo-title-main {
      font-size: 16px;
      margin-top: 10px;
      margin-bottom: 0;
      span:first-child {
        color: #ff6337;
        text-transform: capitalize;
      }
      span:last-child {
        color: #25282b;
      }
    }
    .fc_portal--logo-title-sub {
      color: #a0a4a8;
      font-size: 12px;
    }
  }
  .fc_portal--logo-collapsed {
    width: 79px;
    display: flex;
    justify-content: center;
    height: 64px;
    align-items: center;
  }
}

.ant-layout-sider-light {
  .fc_portal--logo {
    border-bottom: 1px solid #eef1f5;
    border-right: 1px solid #eef1f5;
  }
}
.ant-layout-sider-dark {
  background: #32373d;
  .ant-menu-dark,
  .ant-menu-dark .ant-menu-sub {
    background: #32373d;
  }
  .fc_portal--logo .fc_portal--logo-title .fc_portal--logo-title-main span:last-child {
    color: #ffffff;
  }
}

.fc_portal--menu {
  padding: 18px 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
  .fc-menu-space {
    margin-top: auto !important;
  }
}
/* Enter and leave animations can use different */
/* durations and timing functions.              */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0;
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active below version 2.1.8 */ {
  transform: translateX(10px);
  opacity: 0;
}
</style>
