<template>
  <div>
    <div class="statistic-filter" style="padding: 0;">
      <desk-data-filter
        @applyFilter="onApplyFilter"
        @setInitialDateRangeAndFrequencyPayload="setInitialDateRangeAndFrequencyPayload"
      />
    </div>
    <div class="tracking-table">
      <desk-data-table
        :apiResponse="desks_data"
        @onTableChange="onTableChange"
        @onTableSearch="onTableSearch"
        :dateRangePayload="dateRangeAndFrequencyPayload"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

import DeskDataFilter from '../../Actions/DeskDataFilter.vue';
import DeskDataTable from '../../Actions/DeskData/DeskDataTable.vue';

export default {
  components: {
    DeskDataFilter,
    DeskDataTable,
  },
  data() {
    return {
      dateRangeAndFrequencyPayload: {},
      sortingPayload: {},
      searchingPayload: {},
      filterMeterPayload: {},
    };
  },
  computed: {
    ...mapState({
      desks_data: (state) => state.desk_data_statistics.desks_data,
      pageInformation: (state) => state.desk_data_statistics.page_information,
    }),
    fetchPayload() {
      return {
        ...this.dateRangeAndFrequencyPayload,
        ...this.sortingPayload,
        ...this.searchingPayload,
        ...this.filterMeterPayload,
      };
    },
  },
  methods: {
    setInitialDateRangeAndFrequencyPayload(childPayload) {
      this.dateRangeAndFrequencyPayload = childPayload;
      this.$store.dispatch('desk_data_statistics/setDateRange', { ...childPayload });
    },
    fetch(payload) {
      this.$store.dispatch('desk_data_statistics/loadDeskData', { ...payload });
    },
    onApplyFilter(childPayload) {
      this.dateRangeAndFrequencyPayload = childPayload;
      this.$store.dispatch('desk_data_statistics/setDateRange', { ...childPayload });
      this.fetchPayload.page = 1;
      if (!this.fetchPayload.per_page) {
        this.fetchPayload.per_page = 10;
      }
      this.fetch(this.fetchPayload);
    },
    onTableChange(childPayload) {
      this.sortingPayload = childPayload;
      this.fetch(this.fetchPayload);
    },
    onTableSearch(childPayload) {
      this.searchingPayload = childPayload;
      this.fetch(this.fetchPayload);
    },
  },
};
</script>
