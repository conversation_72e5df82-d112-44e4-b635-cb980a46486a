<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center">
      <div class="fc-group__left">
        <fc-search @onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button v-if="isShowExportBtn" type="primary" class="fc-group-button yellow" @click="onClickExportCSV">
          <a-icon :component="ExportIcon" />
          Export CSV
        </a-button>
        <fc-add-to-dashboard :type="'desk_data_table'" v-if="!shownInDashboard" />
      </div>
    </div>
    <div>
      <fc-table
        :tableHeight="this.dynamicHeight"
        :columns="columns"
        rowKey="id"
        :dataSource="apiResponse"
        :loading="loading"
        @onTableChange="onTableChange"
      >
        <a
          slot="booking"
          slot-scope="text, record"
          :href="`/features/desk_bookings?desk_booking_ids=[${record.desk_booking_ids}]`"
          target="_blank"
          rel="noopener noreferrer"
          @click="handleClick(record.desk_booking_ids)"
        >
          {{ text }}
        </a>
      </fc-table>
    </div>
  </div>
</template>

<script>
const columns = [
  {
    title: 'Date',
    dataIndex: 'date',
    sorter: (a, b) => {
      return moment(a.date, 'DD/MM/YYYY') - moment(b.date, 'DD/MM/YYYY');
    },
  },
  {
    title: 'Zone Name',
    dataIndex: 'zone_name',
    sorter: (a, b) => a.zone_name.localeCompare(b.zone_name),
  },
  {
    title: 'No. of Desks',
    dataIndex: 'number_of_desks',
    sorter: (a, b) => a.number_of_desks - b.number_of_desks,
  },
  {
    title: 'No. of Bookings',
    dataIndex: 'number_of_bookings',
    scopedSlots: { customRender: 'booking' },
    sorter: (a, b) => a.number_of_bookings - b.number_of_bookings,
  },
  {
    title: 'Utilization',
    dataIndex: 'utilization',
    sorter: (a, b) => a.utilization - b.utilization,
  },
];

import { ExportIcon } from '@/components/Icons';
import FcSearch from '@/components/Shared/FcSearch.vue';
import { getElementAbsoluteHeight } from '@/utils/css-helper';
import { generateAndDownloadCsv } from '@/utils/helper';

export default {
  components: {
    FcSearch,
  },
  props: {
    apiResponse: {
      type: Array,
      default: [],
    },
    pageInformation: {
      type: Object,
      default: () => {},
    },
    dateRangePayload: {
      type: Object,
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: false,
  },
  data() {
    return {
      ExportIcon,
      columns,
      dataSource: [],
      dataSort: undefined,
      orderSort: undefined,
      loading: false,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      perPage: 10,
      page: 1,
      dynamicHeight: '100%',
    };
  },
  mounted() {
    if (!this.shownInDashboard) {
      window.addEventListener('resize', this.changeDynamicHeight);
      this.changeDynamicHeight();
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.changeDynamicHeight);
  },
  methods: {
    handleClick(value) {
      this.$store.dispatch('desk_data_statistics/setDeksBookingIds', value);
    },
    handleInputSearchSubmit(value) {
      this.q = value;
      this.$emit('onTableSearch', { q: this.q });
    },
    onTableChange({ pagination, filters, sorter }) {
      if (this.page != pagination.current) {
        this.page = pagination.current;
      }

      if (!sorter.order) {
        this.sorter.data_sort = undefined;
        this.sorter.order_sort = undefined;
      } else {
        this.sorter.data_sort = sorter.field;
        this.sorter.order_sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      }

      const payload = {
        data_sort: this.sorter.data_sort,
        order_sort: this.sorter.order_sort,
        per_page: this.perPage,
        page: this.page,
      };
      this.$emit('onTableChange', payload);
    },
    changeDynamicHeight() {
      const pageContainer = document.querySelector('.statistics-mqtt-container');

      const statisticsFilterHeight = pageContainer.querySelector('.statistic-filter').offsetHeight;

      const fcGroupOnTopHeight = pageContainer.querySelector('.tracking-table').querySelector('.fc-group-on-top')
        .offsetHeight;

      const antTableHeaderHeight = pageContainer.querySelector('.tracking-table').querySelector('.ant-table-header')
        .offsetHeight;

      // Some elements offsetHeight cannot get margin values
      const antTablePaginationHeight = getElementAbsoluteHeight(
        pageContainer.querySelector('#fc_table').querySelector('.ant-table-header'),
      );

      this.dynamicHeight = `calc(100vh
        - 64px
        - ${statisticsFilterHeight}px
        - ${fcGroupOnTopHeight}px
        - ${antTableHeaderHeight}px
        - ${antTablePaginationHeight}px
        - ${10 * 2}px
        - ${16 * 2}px
      )`;
    },
    getSortedTableData() {
      if (!this.apiResponse || !this.apiResponse.length) {
        return [
          {
            date: null,
            zone_name: null,
            number_of_desks: null,
            number_of_bookings: null,
            utilization: null,
          },
        ];
      }

      return [...this.apiResponse].sort((a, b) => {
        if (!this.sorter.data_sort) return 0;
        const field = this.sorter.data_sort;
        const order = this.sorter.order_sort;

        if (field === 'date') {
          return order === 'asc'
            ? moment(a.date, 'DD/MM/YYYY') - moment(b.date, 'DD/MM/YYYY')
            : moment(b.date, 'DD/MM/YYYY') - moment(a.date, 'DD/MM/YYYY');
        }

        if (field === 'zone_name') {
          return order === 'asc' ? a.zone_name.localeCompare(b.zone_name) : b.zone_name.localeCompare(a.zone_name);
        }

        return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
      });
    },
    onClickExportCSV() {
      const sortedData = this.getSortedTableData();
      const exportData = sortedData.map((item) => ({
        date: item.date,
        zone_name: item.zone_name,
        number_of_desks: item.number_of_desks,
        number_of_bookings: item.number_of_bookings,
        utilization: item.utilization,
      }));
      const headers = {
        date: 'Date',
        zone_name: 'Zone Name',
        number_of_desks: 'No. of Desks',
        number_of_bookings: 'No. of Bookings',
        utilization: 'Utilization',
      };

      generateAndDownloadCsv(exportData, 'desk_data_statistics', headers);
    },
  },
};
</script>

<style></style>
