<template>
  <div class="settings-request-fault-types-container">
    <div class="fc-group-on-top">
      <div class="fc-group__left">
        <div class="fc-breadcrumb fc-request-type-breadcrumb">
          <a-breadcrumb>
            <span slot="separator"><BreadcrumSeparatorIcon /></span>
            <a-breadcrumb-item>
              <nuxt-link to="/settings/request_settings"> Request Types </nuxt-link>
            </a-breadcrumb-item>
            <a-breadcrumb-item> Manage Items </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <fc-clear-filter
          :filteredKey="FILTER_REMEMBERED_PROPERTIES.ORDER_ITEMS"
          @onClearFilter="onClearFilter"
        ></fc-clear-filter>
      </div>
      <div class="fc-group__right">
        <new-order-item />
      </div>
    </div>
    <fc-table-v2
      :columns="columns"
      :dataSource="order_items"
      v-on:onSelectedRowChange="onSelectedRowChange"
      :loading="loading"
      :tableHeight="this.dynamicHeight"
      :pagination="pageInformation"
      :rememberFilter="orderItemsRememberedFilter"
      @onTableChange="onTableChange"
    >
      <div class="sub-tag-wrapper" slot="fault_sub_type_tags" slot-scope="item">
        <a-tag v-for="subTag in item" :key="subTag.id" class="sub-tag">
          {{ subTag.name }}
        </a-tag>
      </div>
      <template slot="action" slot-scope="text, record">
        <table-actions :data="record" :actionVisible="record.id === chosenItem.id" />
      </template>
    </fc-table-v2>
    <drawer-order-item :data="orderItem" />
    <order-item-create-modal />
  </div>
</template>

<script>
import { mapState } from 'vuex';

import { YellowHintIcon, SortIcon } from '@/components/Icons/index';
import FcTableV2 from '@/components/Shared/FcTableV2.js';
import FcPageTip from '@/components/Shared/FcPageTip.vue';
import TableActions from '@/components/RequestSettings/MainActions/OrderItem/OrderItemTableActions.vue';
import DrawerOrderItem from '@/components/RequestSettings/MainActions/OrderItem/DrawerActions/DrawerOrderItem.vue';
import OrderItemCreateModal from '@/components/RequestSettings/MainActions/OrderItem/OrderItemCreateModal.vue';
import FcClearFilter from '@/components/Shared/FcClearFilter.vue';
import { FILTER_REMEMBERED_PROPERTIES } from '@/utils/consts';

import { getElementAbsoluteHeight } from '@/utils/css-helper';
import NewOrderItem from '@/components/RequestSettings/Core/OrderItems/NewOrderItem.vue';

export default {
  components: {
    FcTableV2,
    FcPageTip,
    TableActions,
    YellowHintIcon,
    SortIcon,
    DrawerOrderItem,
    OrderItemCreateModal,
    NewOrderItem,
    FcClearFilter,
  },
  provide() {
    return {
      data: this,
    };
  },
  data() {
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        sorter: true,
      },
      {
        title: 'Description',
        dataIndex: 'description',
        sorter: true,
      },
      {
        title: `Price (${this.getCurrencyCode()})`,
        dataIndex: 'price',
        sorter: true,
      },
      {
        title: () => {
          return (
            <a-tooltip title={() => this.hidePriceColumnTooltip()}>
              <a-button
                icon={this.show_item_order_price ? 'eye' : 'eye-invisible'}
                onClick={() => this.hidePriceButtonClick()}
              ></a-button>
            </a-tooltip>
          );
        },
        width: 70,
      },
      {
        title: 'Max Quantity',
        dataIndex: 'maximum_quantity',
        sorter: true,
      },
      {
        title: '',
        dataIndex: '',
        scopedSlots: { customRender: 'action' },
        className: 'fc-action-row',
        width: 0,
      },
    ];
    return {
      columns,
      chosenItem: { id: 0 },
      loading: false,
      orderItem: {},
      dynamicHeight: 'auto',
      FILTER_REMEMBERED_PROPERTIES,
    };
  },
  mounted() {
    window.addEventListener('resize', this.changeDynamicHeight);
    this.changeDynamicHeight();
  },
  destroyed() {
    window.removeEventListener('resize', this.changeDynamicHeight);
  },
  methods: {
    hidePriceColumnTooltip() {
      return this.show_item_order_price
        ? 'This column is currently not hidden. Click to hide this column'
        : 'This column is currently hidden. Requestors will not see this column. Click to show this column';
    },
    hidePriceButtonClick() {
      this.$store
        .dispatch('agents/updateShowItemOrderPriceConfig', {
          show_item_order_price: !this.show_item_order_price,
        })
        .then((res) => {
          if (res.code === 200 && res.message === 'Successful') {
            this.$store.dispatch('managers/setShowItemOrderPrice', !this.show_item_order_price);
          }
        });
    },
    getCurrencyCode() {
      return this.$auth.user.currency_code.toUpperCase();
    },
    onSelectedRowChange(value) {
      this.chosenItem = value;
      if (value.id) this.orderItem = value;
    },
    changeDynamicHeight() {
      // const pageContainer = document?.querySelector(
      //   ".settings-request-container"
      // ) || 0;
      // const navBarContainerHeight = pageContainer.querySelector(
      //   ".ant-tabs-bar.ant-tabs-top-bar"
      // ).offsetHeight;
      // const fcGroupOnTopHeight =
      //   pageContainer?.querySelector(".fc-group-on-top")?.offsetHeight || 0;
      // const fcTableContainer = pageContainer?.querySelector("#fc_table") || 0;
      // const tableHeaderHeight = getElementAbsoluteHeight(
      //   fcTableContainer.querySelector(".ant-table-header")
      // );
      // // Sometime we cannot select class element
      // const tablePaginationHeight = 64;
      // this.dynamicHeight = `calc(100vh
      //   - 64px
      //   - ${navBarContainerHeight}px
      //   - ${fcGroupOnTopHeight}px
      //   - ${tableHeaderHeight}px
      //   - ${tablePaginationHeight}px
      // )`;
    },
    fetch(value, { isShowClearFilter = true } = {}) {
      this.loading = true;

      this.$store
        .dispatch('order_items/loadAll', {
          ...this.orderItemsRememberedFilter,
          ...value,
        })
        .then(() => {
          this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
            property: FILTER_REMEMBERED_PROPERTIES.ORDER_ITEMS,
            value: {
              ...value,
              isShowClearFilter: isShowClearFilter,
            },
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onTableChange(value) {
      let order_sort = this.decoratorOrderSort(value?.sorter?.order);

      this.fetch({
        page: value.pagination.current,
        per_page: value.pagination.pageSize,
        ...(order_sort ? { data_sort: value?.sorter?.field || undefined } : {}),
        ...(order_sort ? { order_sort } : {}),
      });
    },
    onClearFilter(payload) {
      this.fetch(payload, { isShowClearFilter: false });
    },
    decoratorOrderSort(data) {
      if (!data) return undefined;

      return data === 'ascend' ? 'asc' : 'desc';
    },
  },
  computed: {
    ...mapState({
      order_items: (state) => state.order_items.order_items,
      show_item_order_price: (state) => state.managers.profile.agent_management.show_item_order_price,
      pageInformation: (state) => state.order_items.page_information,
      orderItemsRememberedFilter: (state) => state.filter_remembered.settings?.requests?.order_items || {},
    }),
  },
};
</script>

<style lang="less">
.fc-request-type-breadcrumb {
  border-bottom: none;
  padding: 0;
}
</style>
