<template>
  <div>
    <a-tag v-if="!isShowDueDate" :class="dueDateSetClass" @click="showModal">
      <span>Set Due Date</span>
    </a-tag>
    <fc-due-date-tag v-else :dueDate="dueDate" :disabled="isReadOnly" @click="showModal" />
    <a-modal
      title="Set Due Date for request"
      :visible="visible"
      width="350px"
      okText="Save"
      cancelText="Cancel"
      @cancel="handleCancel"
    >
      <div slot="footer" class="fc-flex-center-space">
        <a-button key="remove" type="danger" @click="handleRemove">Remove</a-button>
        <div>
          <a-button key="back" @click="handleCancel">
            Cancel
          </a-button>
          <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleOk">
            Submit
          </a-button>
        </div>
      </div>
      <a-form :form="form" class="fc-reduce-space-form" :colon="false">
        <a-form-item label="Due Date">
          <a-date-picker
            :format="dateFormat"
            :disabled-date="disabledDate"
            :show-time="{ format: 'HH:mm' }"
            :autoFocus="true"
            v-decorator="[
              'time',
              {
                rules: [{ required: true, message: 'Please select due date' }],
                initialValue: initDueDate,
              },
            ]"
            size="large"
            placeholder="Select Date"
            class="w-100"
          />
        </a-form-item>
        <a-form-item>
          <span slot="label">
            Set due date reminder
            <a-tooltip title="Email reminders will be sent to all accounts assigned to this request.">
              <tip-icon class="tip" />
            </a-tooltip>
          </span>
          <a-select
            v-decorator="[
              'reminder',
              {
                rules: [{ required: true, message: 'Please select reminder' }],
                initialValue: initReminder,
              },
            ]"
            size="large"
            placeholder="Select Form type"
            :getPopupContainer="(trigger) => trigger.parentNode"
          >
            <a-select-option v-for="due in dueDateReminders" :key="due.value">
              {{ due.title }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import FcDueDateTag from '@/components/Shared/FcDueDateTag.vue';

export default {
  components: {
    FcDueDateTag,
  },
  props: {
    request: {
      type: Object,
    },
    isReadOnly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      dateFormat: 'DD/MM/YYYY hh:mm A',
    };
  },
  methods: {
    showModal() {
      if (!this.isReadOnly) {
        this.visible = true;
      }
    },
    handleOk(e) {
      this.confirmLoading = true;
      this.form.validateFields((err, values) => {
        if (!err) {
          const payload = {
            time: moment(values.time).format(this.dateFormat),
            reminder: values.reminder,
          };

          this.$store
            .dispatch('requests/setDueDateReminder', {
              id: this.request.id,
              payload: payload,
            })
            .then((res) => {
              this.$store.dispatch('requests/getRequest', this.request.id).then((request) => {
                this.$store.dispatch('requests/updateRequestInRequests', request);
              });
              this.confirmLoading = false;
              this.visible = false;
              this.$message.success('Successfully Updated!');
            })
            .catch((error) => {
              this.confirmLoading = false;
              this.$message.error(error.response.data.message);
            });
        }
        this.confirmLoading = false;
      });
    },
    handleCancel(e) {
      this.visible = false;
    },
    handleRemove(e) {
      this.$store.dispatch('requests/removeDueDateReminder', this.request.id).then(() => {
        this.$message.success('Successfully Removed!');
        this.$store.dispatch('requests/getRequest', this.request.id);
        this.visible = false;
      });
    },
    disabledDate(current) {
      return (
        current <=
        moment()
          .endOf('day')
          .subtract(1, 'd')
      );
    },
  },
  computed: {
    ...mapState({
      dueDateReminders: (state) => state.requests.due_date_reminders,
    }),
    dueDate() {
      return this.request.due_date;
    },
    initDueDate() {
      return this.dueDate.time ? moment.unix(this.request.due_date.time) : null;
    },
    initReminder() {
      return this.dueDate.reminder ? this.dueDate.reminder : this.dueDateReminders[0]?.value;
    },
    isShowDueDate() {
      return this.dueDate.time && this.dueDate.reminder;
    },
    dueDateSetClass() {
      return this.isReadOnly ? 'disabled-due-date-tag' : '';
    },
  },
};
</script>

<style lang="less">
.disabled-due-date-tag {
  display: none;
}
</style>
