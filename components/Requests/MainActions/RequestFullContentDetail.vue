<template>
  <fragment>
    <ul class="full-content-detail-list">
      <template v-if="request.kind === 'report_fault'">
        <li v-if="request.request_type">
          <span>Request type</span>
          <span>{{ request.request_type }}</span>
        </li>
        <li v-if="request.subject">
          <span>Subject</span>
          <span>{{ request.subject }}</span>
        </li>
        <li v-if="request.fault_type">
          <span>Fault type</span>
          <span>{{ request.fault_type }}</span>
        </li>
        <li v-if="request.requestor_name">
          <span>Requestor name</span>
          <span>{{ request.requestor_name }}</span>
        </li>
        <li v-if="request.fault_sub_type_tag">
          <span>Fault Sub-Type</span>
          <span>{{ request.fault_sub_type_tag }}</span>
        </li>
        <li v-if="request.content">
          <span>Description</span>
          <span>
            {{ requestContent }}
          </span>
        </li>
        <li v-if="request.location">
          <span>Location</span>
          <span>{{ request.location }}</span>
        </li>
        <li v-if="request.location_tag">
          <span>Location tag</span>
          <span>{{ request.location_tag }}</span>
        </li>
      </template>
      <template v-else-if="request.kind === 'custom_fault'">
        <li>
          <span>Request type</span>
          <span>{{ request.request_type }}</span>
        </li>
        <li v-for="(block, index) in decoratorBlocks" :key="index">
          <template
            v-if="
              block.type === 'checkbox' ||
                block.type == 'single_choice_dropdown' ||
                block.type == 'multiple_choice_dropdown'
            "
          >
            <span v-html="block.title"></span>
            <span>{{ pluckTrueContent(block.content) }}</span>
          </template>
          <template v-else-if="block.type === 'radio'">
            <span v-html="block.title"></span>
            <span>{{ pluckTrueContent(block.content) }}</span>
          </template>
          <template v-else-if="block.type === 'toggle'">
            <span v-html="block.title"></span>
            <span>
              <a-icon type="check-circle" theme="twoTone" two-tone-color="#1DD69A" v-if="block.content" />
              <a-icon type="close-circle" theme="twoTone" two-tone-color="#FB4E4E" v-else />
            </span>
          </template>
          <template v-else-if="block.type === 'pdf'">
            <span v-html="block.title"></span>
            <span><fc-preview-pdf :url="block.content" :fileName="block.file_name"/></span>
          </template>
          <template v-else-if="block.type === 'image'">
            <span v-html="block.title"></span>
            <span>
              <request-media :data="block" />
            </span>
          </template>
          <template v-else-if="block.type === 'video'">
            <span v-html="block.title"></span>
            <span>
              <request-media :data="block" />
            </span>
          </template>
          <template v-else-if="block.type === 'media'">
            <span v-html="block.title"></span>
            <span>
              <request-media :data="block" />
            </span>
          </template>
          <template v-else-if="block.type === 'excel'">
            <span v-html="block.title"></span>
            <span><fc-attachment-link :url="block.content" file_name="Download Excel"/></span>
          </template>
          <template v-else-if="block.type === 'order_items'">
            <span v-html="block.title"></span>
            <span>
              <p style="margin: 0">{{ block.content }}</p>
              <strong v-if="shouldShowTotalPrice(block.options)">
                >Total Price ({{ getCurrencyCode }}): {{ orderItemsTotalResult(block.options) }}</strong
              >
            </span>
          </template>
          <template v-else-if="block.type === 'fault_signature'">
            <span v-html="block.title"></span>
            <span>
              <viewer ref="viewer" :images="[block.content]" @inited="inited">
                <img :src="block.content" alt="fault_signature" class="fault_signature_image" />
              </viewer>
            </span>
          </template>
          <template v-else-if="block.type === 'fault_type'">
            <span v-html="block.title"></span>
            <span>{{ request.fault_type }}</span>
          </template>
          <template v-else>
            <span v-html="block.title"></span>
            <span v-if="block.content && block.content.length > 0">{{ block.content }}</span>
            <span v-if="block.content && block.type === 'payment'">
              ID:
              <a
                :href="`/features/payments?ids=[${block.content.id}]`"
                rel="noopener nofollow noreferrer"
                target="_blank"
              >
                {{ block.content.payment_id }}
              </a>
              <span style="margin-left: 10px">From: {{ block.content.from }}</span>
              <span style="margin-left: 10px"
                >Amount: {{ block.content.amount + ' ' + $auth.user.currency_code.toUpperCase() }}</span
              >
            </span>
            <span v-if="!block.content || block.content.length === 0">None</span>
          </template>
        </li>
      </template>
      <template v-else-if="request.kind === 'talk_to_agent'">
        <li>
          <span>Content</span>
          <span>{{ request.content }}</span>
        </li>
      </template>
      <template v-if="$auth.user.role !== 'requestor'">
        <li>
          <span>Request by</span>
          <span>
            <a :href="requestorLink" rel="noopener nofollow noreferrer" target="_blank">
              {{ request.guest.full_name }}
            </a>
          </span>
        </li>
        <li v-if="request.mail_message_id">
          <span></span>
          <span
            style="color: #007eff; cursor: pointer;"
            @click="handleDownloadEmail(request.mail_message_id, request.subject, request.created_at)"
          >
            &nbsp;&nbsp;<a-icon type="mail" /> View Email</span
          >
          <request-email-viewer
            @closeEmailViewer="handleCloseEmailViewer"
            :visible="emlViewerOpen"
            :emlContent="emlContent"
            :emlTitle="emlTitle"
          />
        </li>
      </template>
      <li v-if="request.linked_requests.length">
        <span>Linked to Case ID</span>
        <span>
          <span v-for="(linked, index) in request.linked_requests" class="linked-request" :key="index">
            <a href="javascript:void(0)" @click="handleLinkRequestClick(linked.id)">{{ linked.case_id }}</a>
          </span>
        </span>
      </li>
      <li v-if="request.linked_asset_trackings.length">
        <span>Linked Asset ID</span>
        <span>
          <span v-for="(asset, i) in request.linked_asset_trackings" :key="asset.id">
            <fc-link-asset-detail :asset_id="asset.asset_id" />
            <span style="margin-left: -3px; color: #299bff;" v-if="i !== request.linked_asset_trackings.length - 1"
              >,&nbsp;</span
            >
          </span>
        </span>
      </li>
      <li v-if="request.link_to_checklist.id">
        <span>Linked Checklist</span>
        <span>
          <fc-checklist-detail
            :checklist_id="request.link_to_checklist.id"
            :text="request.link_to_checklist.checklist_id"
          />
        </span>
      </li>
      <li v-if="request.processing_remarks">
        <span>Processing Remarks</span>
        <request-processing-remark :request="request" :isReadOnly="isReadOnly" />
      </li>
      <li v-if="request.completion_remarks">
        <span>Completion Remarks</span>
        <request-complete-remark :request="request" />
        <!-- <span>{{request.completion_remarks}}</span> -->
      </li>
      <li v-if="getProblemCode">
        <span>Problem Code</span>
        <span>{{ getProblemCode }}</span>
      </li>
      <li v-if="getCauseCode">
        <span>Cause Code</span>
        <span>{{ getCauseCode }}</span>
      </li>
      <li v-if="getRemedyCode">
        <span>Remedy Code</span>
        <span>{{ getRemedyCode }}</span>
      </li>
    </ul>
    <template v-if="$auth.user.role !== 'requestor'">
      <request-update-status-form :request="request" :isReadOnly="isReadOnly" />
      <a-divider style="margin: 0" />
      <request-show-attachment-editable :request="request" :isReadOnly="isReadOnly" />
    </template>
    <template v-if="fullImage.length">
      <a-divider style="margin: 0">Fault Images</a-divider>
      <div class="full-content-images card-request-images">
        <viewer :images="fullImage">
          <img v-for="src in fullImage" :src="src" :key="src" />
        </viewer>
      </div>
    </template>
    <template v-if="fullVideo.length">
      <a-divider style="margin: 0">Fault Videos</a-divider>
      <div class="fc-request-videos">
        <request-video v-for="(video, index) in fullVideo" :key="index" :file="video" />
      </div>
    </template>
    <template v-if="Object.keys(request.request_remarks).length">
      <a-divider style="margin: 0">Notes</a-divider>
      <div class="request-note-attachment">
        <div class="fc-asset-note" v-for="note in notes" :key="note.id">
          <request-note-item
            :note="note"
            :isEditable="note.has_edit_remarks"
            @noteEdit="handleEditNote"
            @noteDelete="handleDeleteNote"
            @toggleBroadcast="handleToggleBroadcast"
          />
        </div>
      </div>
    </template>
    <template v-if="Object.keys(signatureImages).length">
      <a-divider style="margin: 0">Signatures</a-divider>
      <viewer class="fc-signature-images fc-request-signature-card" :images="signatureImages">
        <a-card class="fc-signature-card" v-for="img in signatureImages" :key="img.id">
          <img slot="cover" alt="signature" :src="img.url" />
          <a-card-meta>
            <span slot="title">
              <template v-if="img.signatory">
                Signatory: <span>{{ img.signatory }} </span>
                <br />
              </template>
              Submitted By: <a href="#">{{ img.signed_by }} </a>
            </span>
            <template slot="description">
              {{ img.created_at | formatTimeStamp }}
            </template>
          </a-card-meta>
        </a-card>
      </viewer>
    </template>
    <template v-if="Object.keys(request.sor_submissions).length">
      <a-divider style="margin: 0">Schedule Of Rates</a-divider>
      <request-sor-detail :data="request.sor_submissions" />
    </template>
    <a-divider style="margin: 0" />
  </fragment>
</template>

<script>
import _ from 'lodash';
import FcAttachmentLink from '@/components/Shared/FcAttachmentLink.vue';
import RequestUpdateStatusForm from '@/components/Requests/MainActions/RequestUpdateStatusForm/RequestUpdateStatusForm.vue';
import RequestShowAttachmentEditable from '@/components/Requests/MainActions/RequestUploadFiles/RequestShowAttachmentEditable.vue';
import FcPreviewPdf from '@/components/Shared/FcPreviewPdf.vue';
import NoteItem from '@/components/Shared/NoteAndAttachment/NoteItem.vue';
import RequestSorDetail from '@/components/Requests/MainActions/RequestSorDetail.vue';
import FcLinkAssetDetail from '@/components/Shared/FcLinkAssetDetail.vue';
import FcChecklistDetail from '@/components/Shared/FcChecklistDetail.vue';
import RequestProcessingRemark from '@/components/Requests/MainActions/RequestProcessingRemark.vue';
import RequestCompleteRemark from '@/components/Requests/MainActions/RequestCompleteRemark.vue';
import RequestVideo from '@/components/Requests/MainActions/RequestVideo.vue';
import RequestEmailViewer from '@/components/Requests/MainActions/RequestEmailViewer.vue';
import RequestMedia from '@/components/Requests/MainActions/RequestMedia.vue';

export default {
  data() {
    return {
      emlViewerOpen: false,
      emlContent: '',
      emlTitle: 'Email Viewer',
    };
  },
  props: {
    request: Object,
    isReadOnly: {
      type: Boolean,
      default: false,
    },
  },

  components: {
    RequestUpdateStatusForm,
    FcAttachmentLink,
    RequestShowAttachmentEditable,
    FcPreviewPdf,
    NoteItem,
    RequestSorDetail,
    FcLinkAssetDetail,
    FcChecklistDetail,
    RequestProcessingRemark,
    RequestCompleteRemark,
    RequestVideo,
    RequestEmailViewer,
    RequestMedia,
  },
  methods: {
    pluckTrueContent(content) {
      let block_content = content.filter((e) => e.value).map((e) => e.title);
      return block_content.length > 0 ? block_content.join(', ') : 'None';
    },
    handleLinkRequestClick(value) {
      this.$store.dispatch('requests/setChosenRequest', Number(value));
    },
    orderItemsTotalResult(items) {
      let total_price = 0;
      if (!items || !items.length) return total_price;
      for (const item of items) {
        total_price += item.quantity * item.price;
      }
      return total_price;
    },

    downloadAndOpenEml(base64Data, emailName) {
      const decodedData = Buffer.from(base64Data, 'base64');
      this.emlContent = decodedData;
      this.emlTitle = emailName;
      this.emlViewerOpen = true;
    },

    handleDownloadEmail(maidId, subject, createAt) {
      const emailName = `${subject && subject + ' - '}${moment.unix(createAt).format('DD/MM/YYYY')}`;
      this.$message.loading({ content: 'Loading Email...', key: 'updatable' });
      this.$store
        .dispatch('requests/getRequestMailData', maidId)
        .then((base64Response) => {
          base64Response.data && this.downloadAndOpenEml(base64Response.data, emailName);
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },

    handleCloseEmailViewer() {
      this.emlViewerOpen = false;
    },

    customContentMedia(type) {
      let fault_media_data = [];
      const media_blocks = this.request?.custom_content?.blocks?.filter((e) => e.type === 'media') || [];
      media_blocks.forEach((e) => {
        if (e.content) {
          try {
            const fault_media = JSON.parse(e.content)[type];
            fault_media.forEach((media) => {
              fault_media_data.push(type === 'fault_videos' ? { url: media } : media);
            });
          } catch (error) {
            console.error(error);
          }
        }
      });
      return fault_media_data;
    },
    inited(viewer) {
      this.$viewer = viewer;
    },

    handleEditNote({ noteId, message }) {
      this.$store
        .dispatch('requests/sendRequestRemarks', {
          id: this.request.id,
          payload: {
            remark: message,
            request_remark_id: noteId,
          },
        })
        .then(() => {
          this.$message.success('Note updated sucessfully!');
        });
    },
    handleDeleteNote(noteId) {
      this.$store
        .dispatch('requests/deleteRequestRemark', {
          id: this.request.id,
          payload: {
            request_remark_id: noteId,
          },
        })
        .then((res) => {
          this.$message.success('Note deleted sucessfully!');
          this.$store.dispatch('requests/getRequest', this.request.id);
        });
    },
    handleToggleBroadcast({ noteId, value }) {
      this.$store
        .dispatch('requests/toggleRequestRemarkBroadcast', {
          id: noteId,
          payload: { is_disable_broadcast: value },
        })
        .then(() => {
          this.$message.success('Broadcast setting updated!');
          this.$store.dispatch('requests/getRequest', this.request.id);
        })
        .catch((error) => {
          this.$message.error(error.response.data.message);
        });
    },
    shouldShowTotalPrice(options) {
      return this.orderItemsTotalResult(options) > 0 && this.show_item_order_price;
    },
  },
  computed: {
    decoratorBlocks() {
      if (!this.request?.custom_content?.blocks) return [];

      const emptyContentTypes = [
        'pdf',
        'excel',
        'order_items',
        'media',
        'email',
        'datetime',
        'video',
        'select_tags',
        'fault_signature',
        'text',
        'description',
      ];

      const choiceTypes = ['radio', 'checkbox', 'single_choice_dropdown', 'multiple_choice_dropdown'];

      return this.request.custom_content.blocks.filter((e) => {
        if (['image', 'instruction', 'instruction_image'].includes(e.type)) {
          return false;
        }

        if (emptyContentTypes.includes(e.type) && (!e.content || e.content.length === 0)) {
          return false;
        }

        if (e.type === 'fault_type' && (!this.request.fault_type || this.request.fault_type.length === 0)) {
          return false;
        }

        if (choiceTypes.includes(e.type) && this.pluckTrueContent(e.content) === 'None') {
          return false;
        }

        if (e.type === 'text' && (!e.content || e.content.length === 0)) {
          return false;
        }
        if (e.type === 'description' && (!e.content || e.content.length === 0)) {
          return false;
        }

        return true;
      });
    },
    faultImages() {
      return this.request?.fault_image_urls;
    },
    customContentImage() {
      return (
        this.request?.custom_content?.blocks
          ?.filter((e) => e.type === 'image' && e.content.length)
          ?.map((e) => e.content) || []
      );
    },

    customContentMediaImage() {
      return this.customContentMedia('fault_images');
    },
    customContentMediaVideo() {
      return this.customContentMedia('fault_videos');
    },
    fullImage() {
      return [...this.faultImages, ...this.customContentImage, ...this.customContentMediaImage];
    },
    fullVideo() {
      const customContentVideo =
        this.request?.custom_content?.blocks
          ?.filter((e) => e.type === 'video' && e.content.length)
          ?.map((e) => {
            return { url: e.content, name: e.file_name };
          }) || [];

      return [...this.request?.fault_videos, ...customContentVideo, ...this.customContentMediaVideo];
    },
    signatureImages() {
      return this.request.fault_signatures;
    },
    notes() {
      return this.request.request_remarks.map((remark) => {
        return {
          ...remark,
          attachments: remark.attachment,
        };
      });
    },
    requestContent() {
      return this.request.content.split('| ').join('\n');
    },
    requestorLink() {
      return this.request?.guest.role_guest == 'manager'
        ? `/settings/accounts_settings?account_ids=${JSON.stringify([this.request.created_by_manager_id])}`
        : `/settings/accounts_settings?tab=requestor_settings&guest_ids=${JSON.stringify([this.request.guest.id])}`;
    },
    getCurrencyCode() {
      return this.$auth.user.currency_code.toUpperCase();
    },
    getProblemCode() {
      return (
        this.request?.linked_failure_codes
          ?.filter((code) => code.kind === 'problem')
          ?.map((item) => item.code)
          .join(', ') || ''
      );
    },
    getCauseCode() {
      return (
        this.request?.linked_failure_codes
          ?.filter((code) => code.kind === 'cause')
          ?.map((item) => item.code)
          .join(', ') || ''
      );
    },
    getRemedyCode() {
      return (
        this.request?.linked_failure_codes
          ?.filter((code) => code.kind === 'remedy')
          ?.map((item) => item.code)
          .join(', ') || ''
      );
    },
  },
};
</script>

<style lang="less">
ul.full-content-detail-list {
  padding: 20px 35px;
  list-style: none;
  margin-bottom: 0px;
  li {
    display: flex;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  li span:first-child {
    color: #cacccf;
    flex: 1;
    padding-right: 0.2rem;
  }
  li span:last-child {
    color: #7a7a7a;
    flex: 3;
  }
}
.full-content-images {
  padding: 15px 35px;
  img {
    height: 140px !important;
    width: 140px !important;
    object-fit: cover;
    object-position: center;
    margin: 8px;
    border-radius: 8px;
    cursor: pointer;
  }
}
.fc-request-signature-card {
  .fc-signature-card {
    height: inherit;
    .ant-card-meta-title {
      text-overflow: inherit;
      white-space: inherit;
    }
  }
}
.request-note-attachment {
  padding: 15px 35px;
}

.linked-request::after {
  content: ', ';
}
.linked-request:last-child::after {
  content: '';
}

.fault_signature_image {
  width: 50%;
  height: auto;
}
</style>
