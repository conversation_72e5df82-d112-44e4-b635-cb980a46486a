<template>
  <div :key="dataSource.id" class="custom-content-form-items">
    <div v-for="(block, block_index) in metaData" :key="block.type + block_index">
      <div v-if="block.type === 'instruction' && isVisible(block)">
        <a-alert type="info" close-text="Close" style="margin-bottom: 24px;" show-icon>
          <span v-html="block.title" slot="message" />
        </a-alert>
      </div>
      <a-form-item
        class="new-request-card"
        :label="block.title || 'Fault Type'"
        v-if="block.type === 'fault_type' && isVisible(block)"
        :required="false"
      >
        <i v-if="block.compulsory" style="display: flex; float: right"><required-icon /></i>
        <fcb-select-v-3
          size="large"
          :placeholder="getPlaceholder(block)"
          dispatchType="v2/fault_types/loadAndAddFaultTypes"
          queryDispatchType="v2/fault_types/loadFaultTypes"
          :form="form"
          :vDecorator="[
            'fault_type_id',
            {
              rules: [{ required: block.compulsory, message: 'Please select fault type' }],
            },
          ]"
          @onChange="handleFaultTypeChange($event, block.id, block.content)"
          :getPopupContainer="(trigger) => trigger.parentNode"
          :showSearch="true"
          :allowClear="!block.compulsory"
          type="FAULT_TYPES"
        >
          <a-select-option v-for="fault_type in faultTypes" :key="fault_type.id" :value="fault_type.id">
            {{ fault_type.name }}
          </a-select-option>
        </fcb-select-v-3>
      </a-form-item>

      <a-form-item
        class="new-request-card"
        v-if="block.type === 'fault_type' && faultSubType.length > 0 && isVisible(block)"
        label="Fault Sub Type"
        :required="false"
      >
        <i v-if="block.compulsory" style="display: flex; float: right"><required-icon /></i>
        <a-select
          size="large"
          placeholder="Select fault sub type"
          v-decorator="[
            'fault_sub_type_tag_id',
            {
              rules: [{ required: block.compulsory, message: 'Please select fault sub type' }],
              initialValue: undefined,
            },
          ]"
          showSearch
          allowClear
          :getPopupContainer="(trigger) => trigger.parentNode"
          @change="handleFaultSubTypeChange($event, block.id, block.content)"
        >
          <a-select-option v-for="fault_type in faultSubType" :key="fault_type.id">
            {{ fault_type.name }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item class="new-request-card" v-if="block.type === 'select_tags' && isVisible(block)" :required="false">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <a-icon type="environment" theme="filled" />
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <location-tags-tree-select-v-2
          class="service-request-location-tags-select-tree"
          :multiple="true"
          size="large"
          :placeholder="getPlaceholder(block)"
          :showSearch="true"
          :getPopupContainer="(trigger) => trigger.parentNode"
          dispatchType="fault_locations/loadAllFaultLocationsV2"
          queryDispatchType="fault_locations/loadAllFaultLocationsV2"
          :form="form"
          :vDecorator="[
            getFormItemName(block_index),
            {
              rules: [{ required: block.compulsory, message: `Please select ${block.title}` }],
            },
          ]"
          type="LOCATION_TAGS"
          :payload="{ data_sort: 'name', order_sort: 'asc' }"
        >
        </location-tags-tree-select-v-2>
      </a-form-item>
      <a-form-item class="new-request-card" v-if="block.type === 'datetime' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <a-icon type="calendar" theme="filled" />
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-date-picker
          show-time
          :placeholder="getPlaceholder(block)"
          size="large"
          class="fc-date-picker"
          format="DD/MM/YYYY hh:mm A"
          :show-time="{ format: 'HH:mm' }"
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [
                { required: block.compulsory, message: `Please select ${block.title}` },
                { validator: (rule, value, callback) => validateField(rule, value, callback, block) },
              ],
              initialValue: null,
            },
          ]"
          :getCalendarContainer="(trigger) => trigger.parentNode"
        />
      </a-form-item>

      <a-form-item class="new-request-card" v-if="block.type === 'radio' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <single-choice-dropdown-icon></single-choice-dropdown-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-radio-group
          class="fc-radio-group"
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [{ required: block.compulsory, message: `Please select ${block.title}` }],
            },
          ]"
          @change="handleChangeSingleChoice($event, block.id, block.content)"
        >
          <div style="display: block;">
            <a-radio class="fc-radio-item" :value="index" v-for="(item, index) in block.content" :key="index">
              {{ item.title }}
            </a-radio>
          </div>
        </a-radio-group>
      </a-form-item>

      <fc-create-request-form-upload-files
        class="new-request-card"
        v-if="block.type === 'image' && isVisible(block)"
        :fileSize="10"
        :fieldName="getFormItemName(block_index)"
        :block="block"
        :fromCustomContentFormItems="fromCustomContentFormItems"
        :icon="'file-image'"
      >
        <span slot="requiredIcon">
          <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        </span>
      </fc-create-request-form-upload-files>

      <fc-create-request-form-upload-attachments
        v-if="block.type === 'video' && isVisible(block)"
        :block="block"
        class="new-request-card"
        :fileLength="1"
        :multiple="true"
        :fieldName="getFormItemName(block_index)"
        :isAllowVideo="true"
        :fileAccept="'video'"
        :fileSize="0"
        icon="video-camera"
      />

      <fc-create-request-form-upload-attachments
        v-if="block.type === 'multiple_images_videos' && isVisible(block)"
        :block="block"
        class="new-request-card"
        :fileLength="10"
        :multiple="true"
        :fieldName="getFormItemName(block_index)"
        :isAllowVideo="true"
        :fileAccept="'attachment'"
        :fileSize="0"
        icon="paper-clip"
      />

      <a-form-item class="new-request-card" v-if="block.type === 'checkbox' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <a-tooltip title="Bulk select all options">
              <a-checkbox
                class="checkbox-bulk-select"
                :indeterminate="multipleChoiceCheckedSome[block.id]"
                :checked="multipleChoiceCheckedAll[block.id]"
                @change="handleCheckAllMultipleChoice($event, block.content, block.id, block_index)"
              />
            </a-tooltip>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-checkbox-group
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [{ required: block.compulsory, message: `Please select ${block.title}` }],
            },
          ]"
          @change="handleChangeMultipleChoice($event, block.content, block.title, block.id)"
          style="width: 100%;"
        >
          <a-row>
            <a-col :span="8" v-for="(item, index) in block.content" :key="index">
              <a-checkbox
                style="display: block;"
                :value="JSON.stringify(item)"
                :checked="isMultipleChoiceItemChecked(block, item)"
              >
                {{ item.title }}
              </a-checkbox>
            </a-col>
            <br />
          </a-row>
        </a-checkbox-group>
      </a-form-item>

      <fc-create-request-form-upload-files
        class="new-request-card"
        v-if="block.type === 'excel' && isVisible(block)"
        fileAccept="excel"
        :fileSize="10"
        :fieldName="getFormItemName(block_index)"
        :block="block"
        :fromCustomContentFormItems="fromCustomContentFormItems"
        :icon="'file-excel'"
      >
        <span slot="requiredIcon">
          <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        </span>
      </fc-create-request-form-upload-files>

      <fc-create-request-form-upload-files
        class="new-request-card"
        v-if="block.type === 'pdf' && isVisible(block)"
        fileAccept="pdf"
        :fileSize="10"
        :fieldName="getFormItemName(block_index)"
        :block="block"
        :fromCustomContentFormItems="fromCustomContentFormItems"
        :icon="'file-pdf'"
      >
        <span slot="requiredIcon">
          <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        </span>
      </fc-create-request-form-upload-files>

      <a-form-item class="new-request-card" v-if="block.type === 'text' && isVisible(block)" :key="'text_' + block.id">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <create-request-form-short-answer-icon></create-request-form-short-answer-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-input
          size="large"
          :placeholder="getPlaceholder(block)"
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [
                { required: block.compulsory, message: `Please enter ${block.title}` },
                { validator: (rule, value, callback) => validateField(rule, value, callback, block) },
              ],
              initialValue: null,
            },
          ]"
        />
      </a-form-item>
      <div
        v-if="block.type === 'instruction_image' && isVisible(block)"
        class="fc-list-images"
        style="margin-bottom: 24px;"
      >
        <viewer :images="[block.title]" class="fc-tooltip-list-images">
          <img :src="block.title" />
        </viewer>
      </div>
      <a-form-item class="new-request-card" v-if="block.type === 'toggle' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <create-request-form-toggle-icon></create-request-form-toggle-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label" style="float: left; padding-left: 10px">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-switch v-decorator="[getFormItemName(block_index), { valuePropName: 'checked' }]" :checked="block.content" />
      </a-form-item>

      <div class="new-request-card" v-if="block.type === 'order_items' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <create-request-form-item-order-icon></create-request-form-item-order-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title"> </label></div
          ></slot>
        </div>
        <order-items :formItemName="getFormItemName(block_index)" :form="form" />
      </div>

      <a-form-item class="new-request-card" v-if="block.type == 'email' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <create-request-form-email-icon></create-request-form-email-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <div class="fc-email-adder-container">
          <fc-email-adder :max="getMaximumEmail(block)" @onEmailChange="handleEmailChange($event, block_index)" />
        </div>

        <a-input style="display: none;" v-decorator="[getFormItemName(block_index)]" />
      </a-form-item>

      <a-form-item class="new-request-card" v-if="block.type == 'single_choice_dropdown' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <single-choice-dropdown-icon></single-choice-dropdown-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-select
          size="large"
          showArrow
          showSearch
          :filterOption="filterOptionSingleChoice"
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [
                {
                  required: block.compulsory,
                  message: `Please select ${block.title}`,
                },
              ],
              initialValue: [],
            },
          ]"
          :placeholder="getPlaceholder(block)"
          :getPopupContainer="(trigger) => trigger.parentNode"
          @change="handleChangeSingleChoiceDropdown($event, block.id, block.content)"
        >
          <a-select-option v-for="(item, index) in block.content" :key="index">
            {{ item.title }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item class="new-request-card" v-if="block.type == 'multiple_choice_dropdown' && isVisible(block)">
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <multiple-choice-dropdown-icon></multiple-choice-dropdown-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-select
          size="large"
          showSeach
          mode="multiple"
          showArrow
          :filterOption="filterOptionMultipleChoice"
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [
                {
                  required: block.compulsory,
                  message: `Please select ${block.title}`,
                },
              ],
              initialValue: [],
            },
          ]"
          :placeholder="getPlaceholder(block)"
          :getPopupContainer="(trigger) => trigger.parentNode"
          @change="handleChangeMultipleChoiceDropdown($event, block.id, block.content)"
        >
          <a-select-option v-for="(item, index) in block.content" :key="index">
            {{ item.title }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <div class="new-request-card" v-if="block.type === 'payment' && isVisible(block)">
        <payment-form-items
          :form="form"
          :formItemName="getFormItemName(block_index)"
          :block="block"
          :requestTypeId="dataSource.id"
        />
      </div>

      <a-form-item
        class="new-request-card"
        v-if="block.type === 'description' && isVisible(block)"
        :key="'description_' + block.id"
      >
        <i style="display: flex; float: right" v-if="block.compulsory"><required-icon /></i>
        <div class="block-title">
          <div class="block-title__icon">
            <i class="block-title__icon__svg">
              <create-request-form-short-answer-icon></create-request-form-short-answer-icon>
            </i>
          </div>
          <slot name="title"
            ><div class="ant-form-item-label">
              <label v-html="block.title" /></div
          ></slot>
        </div>
        <a-input
          size="large"
          v-decorator="[
            getFormItemName(block_index),
            {
              rules: [{ required: block.compulsory, message: `Please enter ${block.title}` }],
              initialValue: null,
            },
          ]"
          :placeholder="getPlaceholder(block)"
        />
      </a-form-item>
      <fault-signature-form-items
        v-if="block.type === 'fault_signature' && isVisible(block)"
        :block="block"
        :form="form"
        :formItemName="getFormItemName(block_index)"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import FcCreateRequestFormUploadFiles from '~/components/Shared/FcCreateRequestFormUploadFiles.vue';
import OrderItems from './OrderItems.vue';
import PaymentFormItems from './PaymentFormItems.vue';
import FcbSelectV3 from '~/components/Shared/FcbSelectV3.vue';
import FaultSignatureFormItems from './FaultSignatureFormItems.vue';
import LocationTagsTreeSelect from '@/components/Shared/LocationTagsTreeSelect.vue';
import LocationTagsTreeSelectV2 from '@/components/Shared/LocationTagsTreeSelectV2.vue';
import _ from 'lodash';
import { TEXT_BLOCK, DATETIME_BLOCK } from '../../../../utils/meta_data_block';
import { validateTextResponse, validateDatetimeResponse } from '@/utils/validator_helper';
import FcEmailAdder from '~/components/Shared/FcEmailAdder.vue';

import {
  RequiredIcon,
  CreateRequestFormToggleIcon,
  SingleChoiceDropdownIcon,
  MultipleChoiceDropdownIcon,
  CreateRequestFormEmailIcon,
  CreateRequestFormShortAnswerIcon,
  CreateRequestFormItemOrderIcon,
} from '~/components/Icons';

export default {
  data() {
    return {
      fromCustomContentFormItems: true,
      faultTypeId: undefined,
      selectedSingleChoice: {},
      selectedMultipleChoice: {},
      selectedSingleChoiceDropdown: {},
      selectedMultipleChoiceDropdown: {},
      selectedFaultType: {},
      selectedFaultSubType: undefined,
      blockCopy: [],
      validationErrors: {},
      multipleChoiceCheckedAll: {},
      multipleChoiceCheckedSome: {},
    };
  },
  props: {
    dataSource: {
      type: Object,
    },
    form: {
      type: Object,
    },
  },
  mounted() {
    this.blockCopy = _.cloneDeep(this.dataSource?.meta_data?.blocks);
  },
  methods: {
    getFormItemName(title) {
      return `custom_content[${this.dataSource.id}_${title}]`;
    },
    filterOptionSingleChoice(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    filterOptionMultipleChoice(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    handleFaultTypeChange(e, id, choices) {
      this.faultTypeId = e;
      this.selectedFaultType[id] = e;
      this.form.setFieldsValue({
        fault_sub_type_tag_id: undefined,
      });
      this.checkLogicCondition();
    },
    handleFaultSubTypeChange(e, id, choices) {
      this.selectedFaultSubType = e;
      this.checkLogicCondition();
    },
    handleChangeSingleChoice(e, id, choices) {
      let index = e.target.value;
      this.selectedSingleChoice[id] = [choices[index]?.id];
      this.checkLogicCondition();
    },
    isMultipleChoiceItemChecked(block, item) {
      return this.selectedMultipleChoice[block.id]?.includes(JSON.stringify(item)) || item.value;
    },
    handleChangeMultipleChoice(e, allOptions, title, id) {
      const selectedOptions = e.map((item) => JSON.parse(item)?.id);
      this.selectedMultipleChoice[id] = selectedOptions;

      if (selectedOptions.length === 0) {
        this.multipleChoiceCheckedAll[id] = false;
        this.multipleChoiceCheckedSome[id] = false;
      } else if (selectedOptions.length === allOptions.length) {
        this.multipleChoiceCheckedAll[id] = true;
        this.multipleChoiceCheckedSome[id] = false;
      } else {
        this.multipleChoiceCheckedAll[id] = false;
        this.multipleChoiceCheckedSome[id] = true;
      }
      this.checkLogicCondition();
    },
    handleCheckAllMultipleChoice(e, allOptions, id, block_index) {
      const isCheckAll = !this.multipleChoiceCheckedAll[id];
      this.selectedMultipleChoice[id] = isCheckAll ? allOptions.map((item) => JSON.stringify(item)) : [];
      this.form.setFieldsValue({
        [this.getFormItemName(block_index)]: isCheckAll ? allOptions.map((item) => JSON.stringify(item)) : [],
      });

      this.multipleChoiceCheckedAll[id] = isCheckAll;
      this.multipleChoiceCheckedSome[id] = isCheckAll
        ? false
        : this.selectedMultipleChoice[id].length > 0 && this.selectedMultipleChoice[id].length < allOptions.length;
      this.checkLogicCondition();
    },
    handleChangeSingleChoiceDropdown(e, id, choices) {
      this.selectedSingleChoiceDropdown[id] = [choices[e].id];
      this.checkLogicCondition();
    },
    handleChangeMultipleChoiceDropdown(e, id, choices) {
      let selectedOptions = choices
        .filter((item, index) => {
          return e.includes(index);
        })
        .map((item) => {
          return item.id;
        });
      this.selectedMultipleChoiceDropdown[id] = selectedOptions;
      this.checkLogicCondition();
    },
    handleEmailChange(emails, block_index) {
      this.form.setFieldsValue({
        [`${this.getFormItemName(block_index)}`]: emails,
      });
    },
    compareArray(array1 = [], array2 = [], kind = 'all_of') {
      if (kind === 'all_of') {
        return (
          array1.length === array2.length &&
          array1.every((item) => array2.includes(item)) &&
          array2.every((item) => array1.includes(item))
        );
      } else if (kind === 'any_of') {
        return array1.some((item) => array2.includes(item));
      }
      return false;
    },
    checkLogicCondition() {
      for (const block of this.blockCopy) {
        if (!this.logicActionIds.includes(block.id)) {
          continue;
        }

        let blockShouldBeVisible = false;

        this.logics.forEach((logic) => {
          if (!this.logicActionObject[logic.id].includes(block.id)) {
            return;
          }

          let isLogicValid = true;

          let isConditionHidden = logic.conditions.some((condition) => {
            return this.findBlockById(condition.id)?.hidden === true;
          });

          if (!isConditionHidden) {
            for (let condition of logic.conditions) {
              let isConditionValid = false;
              let conditionResponses = condition.response.map((e) => e.id);

              switch (condition.question_type) {
                case 'radio':
                  let selectedOption = this.selectedSingleChoice[condition.id];
                  if (selectedOption) {
                    isConditionValid = conditionResponses.includes(...selectedOption);
                  }
                  break;
                case 'checkbox':
                  isConditionValid = this.compareArray(
                    this.selectedMultipleChoice[condition.id],
                    conditionResponses,
                    condition.option_kind,
                  );
                  break;
                case 'single_choice_dropdown':
                  if (_.isEmpty(this.selectedSingleChoiceDropdown[condition.id])) {
                    break;
                  }
                  isConditionValid = conditionResponses.includes(this.selectedSingleChoiceDropdown[condition.id][0]);
                  break;
                case 'multiple_choice_dropdown':
                  isConditionValid = this.compareArray(
                    this.selectedMultipleChoiceDropdown[condition.id],
                    conditionResponses,
                    condition.option_kind,
                  );
                  break;
                case 'fault_type':
                  let selectedFaultType = this.selectedFaultType[condition.id];

                  let fault_sub_types = condition.fault_sub_type;

                  if (fault_sub_types && fault_sub_types.length > 0) {
                    const hasSelectedSubType = !!this.selectedFaultSubType;
                    const isTypeMatch = selectedFaultType === condition.response[0].id;
                    const isSubTypeMatch = fault_sub_types.includes(this.selectedFaultSubType?.toString());

                    isConditionValid = hasSelectedSubType && isTypeMatch && isSubTypeMatch;
                  } else {
                    isConditionValid = selectedFaultType === condition.response[0].id;
                  }

                  break;
              }
              if (!isConditionValid) {
                isLogicValid = false;
                break;
              }
            }
          }

          if (isLogicValid && !isConditionHidden) {
            blockShouldBeVisible = true;
          }

          block.hidden = !blockShouldBeVisible;
        });
      }
    },

    isVisible(block) {
      return !block.hidden;
    },
    findBlockById(id) {
      return this.blockCopy.find((block) => block.id === id);
    },
    getPlaceholder(block) {
      if (block.placeholder) {
        return block.placeholder;
      }
      switch (block.type) {
        case 'fault_type':
          return 'Select fault type';
        case 'select_tags':
          return 'Select Location Tags to add';
        case 'text':
          return 'Type your answer';
        case 'email':
          return 'Type the email address';
        case 'single_choice_dropdown':
          return 'Select a single choice';
        case 'multiple_choice_dropdown':
          return 'Select multiple choices';
        case 'description':
          return 'Type your answer';
        case 'datetime':
          return 'Choose Date & Time';
        default:
          return '';
      }
    },
    validateField(rule, value, callback, block) {
      // Field is not required and empty => valid
      if (!block.compulsory && (!value || value === '')) {
        callback();
        return;
      }

      if (block.validator) {
        let validationResult;
        switch (block.type) {
          case TEXT_BLOCK.type:
            validationResult = validateTextResponse(value, block.validator);
            break;
          case DATETIME_BLOCK.type:
            validationResult = validateDatetimeResponse(value, block.validator);
            break;
          default:
            validationResult = { isValid: false, message: 'Invalid block type for validation' };
        }

        if (validationResult.isValid) {
          callback();
          // Clear previous error
          this.$set(this.validationErrors, this.getFormItemName(this.metaData.indexOf(block)), '');
        } else {
          callback(new Error(validationResult.message));
          // Set error message
          this.$set(
            this.validationErrors,
            this.getFormItemName(this.metaData.indexOf(block)),
            validationResult.message,
          );
        }
      } else {
        callback();
      }
    },
    getMaximumEmail(block) {
      return _.get(block, 'max_email_limit', 1);
    },
  },
  computed: {
    ...mapState({
      faultLocations: (state) => state.v2.locations.locations,
      faultTypes: (state) => state.v2.fault_types.fault_types,
      profile: (state) => state.managers.profile,
    }),
    logics() {
      return this.dataSource?.logic;
    },
    metaData() {
      return this.blockCopy;
    },
    logicActionIds() {
      const allActionIds = this.logics.flatMap((logic) => logic.action.map((action) => action.id));
      return [...new Set(allActionIds)];
    },
    logicActionObject() {
      return this.logics.reduce((acc, logic) => {
        acc[logic.id] = logic.action.map((action) => action.id);
        return acc;
      }, {});
    },
    faultSubType() {
      return this.faultTypes.find((fault) => fault.id === this.faultTypeId)?.fault_sub_type_tags || [];
    },
  },
  components: {
    FcCreateRequestFormUploadFiles,
    OrderItems,
    RequiredIcon,
    CreateRequestFormToggleIcon,
    SingleChoiceDropdownIcon,
    MultipleChoiceDropdownIcon,
    CreateRequestFormEmailIcon,
    CreateRequestFormShortAnswerIcon,
    CreateRequestFormItemOrderIcon,
    PaymentFormItems,
    FcbSelectV3,
    FaultSignatureFormItems,
    LocationTagsTreeSelect,
    LocationTagsTreeSelectV2,
    FcEmailAdder,
  },
};
</script>

<style lang="less">
.custom-content-form-items {
  .ant-radio-wrapper {
    display: block;
  }
  .ant-checkbox-wrapper {
    display: block;
    width: 200px;
  }
  .checkbox-bulk-select.ant-checkbox-wrapper {
    width: unset;
  }

  .ant-col.ant-col-8 {
    width: 100%;
  }

  .block-title {
    display: flex;
    align-items: center;
    .ant-form-item-label {
      color: #ff6337;
    }
    font-size: 16px;
    font-weight: 500;
    padding-bottom: 10px;
    .ant-form-item-label {
      padding: 0px;
      padding-left: 10px;
    }
    .block-title__icon {
      display: flex;
      color: #ff6337;
    }
  }

  .block-title__icon__svg svg {
    transform: translateY(2px);
    fill: #ff6337;
    font-size: 16px;
  }

  .fc-email-adder-container {
    .fc-email-max-text {
      margin-top: 0px;
    }
  }
}
</style>
