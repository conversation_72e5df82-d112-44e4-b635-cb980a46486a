<template>
  <div class="request-sticky-bottom">
    <a-form layout="vertical" :form="form" class="fc-reduce-space-form">
      <div class="request-drawer__body">
        <a-alert
          message="The previous Request Type may have been deleted. Please choose another Request Type."
          type="info"
          show-icon
          style="margin-bottom: 8px"
          v-if="isDeletedRequestType"
        />
        <a-spin :spinning="!isRequestTypesLoaded">
          <a-form-item class="new-request-card" label="Request Type" :required="false">
            <i style="display: flex; float: right"><required-icon /></i>
            <fc-select-with-link
              :options="combineAllRequestTypes"
              v-on:handleSelect="handleRequestTypeChange"
              placeholder="Select Request Type"
              :vDecorator="[
                'request_type_id',
                {
                  rules: [{ required: true, message: 'Please select request type' }],
                  initialValue: initialRequestTypeId(),
                },
              ]"
              :isLoadMore="isAllowCustomFaults"
              dispatchTrigger="request-types/loadMoreRequestTypes"
              :pageTrigger="page"
              type="REQUEST_TYPES"
              :disabled="!!paymentModalData.request_type_id"
            />
          </a-form-item>
          <request-form-items
            :request="request"
            :chosenRequestType="chosenRequestType"
            :key="chosenRequestType.id"
            :form="form"
          />
          <template v-if="$auth.user.role !== 'requestor' && allowSelectRequestor">
            <div class="new-request-card">
              <fc-user-picker-form-items :guestId="guestId" />
            </div>
          </template>
        </a-spin>
      </div>
      <div class="request-drawer__footer">
        <div class="request-drawer__footer-left">
          <template v-if="isLinkedFaultReport">
            <p>Linked to Case ID</p>
            <span>{{ request.case_id }}</span>
          </template>
        </div>
        <div class="request-drawer__footer-right">
          <a-button size="large" @click="handleCancel" v-if="!isLinkedFaultReport">Cancel</a-button>
          <a-button
            type="primary"
            size="large"
            :loading="isSubmitLoading"
            v-if="showSubmitFormRequest"
            @click="handleFormSubmit"
          >
            Submit
          </a-button>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import FcPlatformTag from '~/components/Shared/FcPlatformTag.vue';
import FcUserPickerFormItems from '../../Shared/FcUserPickerFormItems.vue';
import RequestFormItems from './RequestFormItems/RequestFormItems.vue';
import { RequiredIcon } from '~/components/Icons';
import FcSelectWithLink from '../../Shared/FcSelectWithLink.vue';
import _ from 'lodash';

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export default {
  components: { FcPlatformTag, FcUserPickerFormItems, RequestFormItems, FcPlatformTag, RequiredIcon, FcSelectWithLink },
  props: {
    isLinkedFaultReport: {
      type: Boolean,
      default: false,
    },
    request: {
      type: Object,
    },
    allowSelectRequestor: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      formLayout: 'horizontal',
      chosenRequestType: {},
      form: this.$form.createForm(this),
      isSubmitLoading: false,
      isShowFullNameField: true,
      isDeletedRequestType: false,
      page: 2,
      isLoading: false,
      isCompleted: false,
    };
  },
  computed: {
    formItemLayout() {
      const { formLayout } = this;
      return formLayout === 'horizontal'
        ? {
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
          }
        : {};
    },
    buttonItemLayout() {
      const { formLayout } = this;
      return formLayout === 'horizontal'
        ? {
            wrapperCol: { span: 14, offset: 4 },
          }
        : {};
    },
    isAllowCustomFaults() {
      return this.feature_settings.custom_fault_feature?.enable_custom_fault === true;
    },
    combineAllRequestTypes() {
      if (this.isAllowCustomFaults) {
        return [...this.getRequestStaticData, ...this.requestTypes];
      } else {
        return this.getRequestStaticData;
      }
    },
    guestId() {
      return this.request ? this.request.guest.id : 0;
    },
    getRequestStaticData() {
      let staticData = [];

      const requestType = this.request?.request_type;
      const requestKind = this.request?.kind;
      const reportFaultEnabled = this.feature_settings.fault_reporting_feature?.enable_report_fault;
      const talkToAgentEnabled = this.feature_settings.chat_with_staff_feature?.enable_chat_with_staff;

      if (reportFaultEnabled || requestType === 'Report Fault' || requestKind === 'report_fault') {
        staticData.push({
          id: 22222,
          kind: 'report_fault',
          name: 'Report Fault',
        });
      }
      if (talkToAgentEnabled || requestType === 'Talk To Agent' || requestKind === 'talk_to_agent') {
        staticData.push({
          id: 11111,
          kind: 'talk_to_agent',
          name: 'Chat with Staff',
        });
      }

      return staticData;
    },
    ...mapState({
      requestTypes: (state) => state['request-types'].requestTypes,
      faultLocations: (state) => state['request-types'].faultLocations,
      isRequestTypesLoaded: (state) => state['request-types'].isRequestTypesLoaded,
      currentOrderItems: (state) => state['request-types'].currentOrderItems,
      reportFaultImages: (state) => state['requests'].reportFaultImages,
      guests: (state) => state['guests'].guests,
      feature_settings: (state) => state.agents.feature_settings,
      paidStatus: (state) => state.payments.paidStatus,
      emailPayment: (state) => state.payments.emailPayment,
      paymentIntent: (state) => state.payments.paymentIntent,
      showSubmitFormRequest: (state) => state.payments.showSubmitFormRequest,
      paymentId: (state) => state.payments.paymentId,
      paymentModalData: (state) => state.payments.paymentModalData,
    }),
    isRequestTypesLoadedMoreComplete: {
      get() {
        return this.$store.getters['request-types/getIsRequestTypesLoadedMoreComplete'];
      },
      set(newValue) {
        return this.$store.dispatch('request-types/setIsRequestTypesLoadedMoreComplete', newValue);
      },
    },
  },
  methods: {
    handleFormLayoutChange(e) {
      this.formLayout = e.target.value;
    },
    handleRequestTypeChange(value) {
      this.chosenRequestType = this.combineAllRequestTypes.find((r) => r.id === value);
      this.$store.dispatch('payments/setShowSubmitFormRequest', true);
    },
    handleFormSubmit(e) {
      e.preventDefault();
      this.isSubmitLoading = true;
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = new FormData();
          let request_type = JSON.parse(JSON.stringify(this.chosenRequestType));

          if (this.isLinkedFaultReport) {
            formData.append('linked_request_id', this.request.id);
          }

          if (this.$auth.user.role !== 'requestor' && this.allowSelectRequestor) {
            formData.append('guest_id', values.guest_id);
            formData.append('full_name_manually', values.full_name_manually);
          }

          if (request_type.kind === 'report_fault') {
            const falut_images = values.image ? values.image : [];
            formData.append('kind', request_type.kind);
            formData.append('facility_id', values.fault_type_id || '');
            formData.append('fault_sub_type_tag_id', values.fault_sub_type_tag_id || '');
            formData.append('description', values.description || '');
            formData.append('location', values.location || '');
            formData.append('fault_location_ids', JSON.stringify(values.fault_location_ids));

            this.uploadImage(falut_images).then((res) => {
              formData.append('fault_image_ids', JSON.stringify(this.reportFaultImages));
              this.submitData(formData);
            });
          } else if (request_type.kind === 'talk_to_agent') {
            formData.append('kind', request_type.kind);
            formData.append('description', values.description || '');

            this.submitData(formData);
          } else {
            formData.append('kind', request_type.kind);
            formData.append('request_type_id', request_type.id);

            const pure_data = request_type.meta_data.blocks;
            let custom_content = values.custom_content;

            this.combineBase64(pure_data, custom_content).then((res) => {
              let shouldExit = false;
              pure_data.map((data, index) => {
                if (shouldExit) {
                  return;
                }
                if (data.type == 'order_items') {
                  data['post_content'] = JSON.stringify(this.currentOrderItems);
                }

                if (data.type === 'fault_type') {
                  data['post_content'] = JSON.stringify({
                    facility_id: values.fault_type_id,
                    fault_sub_type_tag_id: values.fault_sub_type_tag_id,
                  });
                }

                let custom_data = custom_content?.[this.chosenRequestType.id + '_' + index];
                if (_.isNil(custom_data)) {
                  if (data.type === 'payment') {
                    if (!this.paidStatus) {
                      this.$message.error('Please paid first before submit');
                      shouldExit = true;
                      return;
                    }

                    data['options']['email'] = this.emailPayment;
                    data['post_content'] = JSON.stringify({
                      payment_id: this.paymentId,
                    });

                    formData.append('payment_type_id', data.options.payment_type);
                    formData.append('email_payment', this.emailPayment);
                    if (this.paymentIntent) formData.append('payment_intent_id', this.paymentIntent);
                  }
                  return;
                }
                if (data.type === 'image' || data.type === 'video' || data.type === 'excel' || data.type === 'pdf') {
                  data['post_content'] = res[index];
                } else if (data.type === 'multiple_images_videos') {
                  data['type'] = 'media';
                  data['post_content'] = JSON.stringify(res[index]);
                } else if (data.type === 'select_tags') {
                  data['post_content'] = JSON.stringify(custom_data);
                } else if (data.type === 'checkbox') {
                  data['post_content'] = JSON.stringify(
                    custom_data.map((e) => {
                      let data = JSON.parse(e);
                      data.value = true;
                      return data;
                    }),
                  );
                } else if (data.type === 'radio') {
                  let data_clone = data.content;
                  data_clone.map((e, i) => {
                    e.value = i === custom_data;
                  });

                  data['post_content'] = JSON.stringify(data_clone);
                } else if (data.type === 'datetime') {
                  data['post_content'] = custom_data.format('DD/MM/YYYY hh:mm A');
                } else if (data.type === 'single_choice_dropdown' || data.type === 'multiple_choice_dropdown') {
                  if (data.type == 'single_choice_dropdown') custom_data = [custom_data];
                  data['post_content'] = JSON.stringify(
                    data.content.map((item, index) => {
                      return custom_data.includes(index) ? { ...item, ...{ value: true } } : item;
                    }),
                  );
                } else if (data.type === 'email') {
                  if (_.isArray(custom_data) && custom_data.length > 0) {
                    data['post_content'] = JSON.stringify(custom_data);
                    data['is_multiple'] = true;
                  } else {
                    data['post_content'] = custom_data;
                    data['is_multiple'] = false;
                  }
                } else {
                  data['post_content'] = typeof custom_data === 'string' ? custom_data : JSON.stringify(custom_data);
                }
              });

              if (shouldExit) {
                this.isSubmitLoading = false;
                return;
              }
              formData.append('custom_content', JSON.stringify({ blocks: pure_data }));
              this.submitData(formData);
            });
          }
        } else {
          this.isSubmitLoading = false;
        }
      });
    },
    handleCancel(e) {
      this.$emit('onCancelCreateRequest');
    },

    async combineBase64(pure_data, custom_content) {
      let response = await Promise.all(
        pure_data.map(async (data, index) => {
          let custom_data = custom_content?.[this.chosenRequestType.id + '_' + index];

          if (custom_data === undefined || custom_data == null) return;
          if (data.type === 'multiple_images_videos') {
            const mediaIds = await this.processMultipleMedias(custom_data);
            return mediaIds;
          }
          if (data.type === 'image' || data.type === 'video' || data.type === 'excel' || data.type === 'pdf') {
            if (custom_data[0] === undefined) return;
            return getBase64(custom_data[0].originFileObj);
          }
        }),
      );

      return response;
    },

    async processMultipleMedias(mediaFiles) {
      const uploadPromises = mediaFiles.map((file) => {
        let formDataImage = new FormData();
        formDataImage.append('upload', file.originFileObj);
        return this.$store
          .dispatch('internal_tools/uploadAttachment', formDataImage)
          .then((res) => ({ success: true, id: res.id }))
          .catch(() => ({ success: false, error: 'Error uploading media' }));
      });

      const results = await Promise.all(uploadPromises);
      const mediaIds = results.filter((result) => result.success).map((result) => result.id);

      results.forEach((result) => {
        if (!result.success) {
          this.$message.error(result.error);
        }
      });

      return mediaIds;
    },

    submitData(formData) {
      this.$store
        .dispatch('requests/createRequest', formData)
        .then(() => {
          this.$store.dispatch('requests/setIsCreateRequestLoading', true);
          this.$emit('onSubmitCreateRequest');
          this.$store.dispatch('requests/setReportFaultImages');
          this.$store.dispatch('payments/setEmailPayment', '');
          this.$store.dispatch('payments/setPaidStatus', false);
          this.$store.dispatch('payments/setRequestOrderNotComplete', {});
          this.isSubmitLoading = false;
        })
        .catch((error) => {
          this.$store.dispatch('requests/setReportFaultImages');
          this.$message.error(error.response.data.message);
          this.isSubmitLoading = false;
        });
    },
    uploadImage(images) {
      return Promise.all(
        images.map((image) => {
          if (image.id) {
            return this.$store.dispatch('requests/pushReportFaultImages', image.id);
          } else if (image.status === 'done') {
            const formData = new FormData();
            formData.append('file', image.originFileObj);
            return this.$store.dispatch('requests/uploadFaultImages', formData);
          }
        }),
      );
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    defaultRequestTypeId(id) {
      if (id) {
        if (this.requestTypes.some((e) => e.id === id)) {
          return id;
        } else {
          this.isDeletedRequestType = true;
          return this.getRequestStaticData[0].id;
        }
      } else {
        let chosenRequestType = this.getRequestStaticData.find((r) => r.kind === this.request.kind);
        return chosenRequestType.id;
      }
    },
    infiniteHandler($state) {
      this.$store
        .dispatch('request-types/loadMoreRequestTypes', {
          page: this.page,
        })
        .then(({ data }) => {
          if (data.length) {
            this.page += 1;
            $state.loaded();
          } else {
            $state.complete();
            this.isRequestTypesLoadedMoreComplete = true;
          }
        });
    },
    async fetchRequestTypes() {
      this.isLoading = true;
      this.$store
        .dispatch('request-types/loadAllRequestTypes', {
          restrict_by_account_tags: true,
        })
        .finally(() => {
          if (this.request) {
            // For Edit Request
            if (this.combineAllRequestTypes.length > 0) {
              if (this.paymentModalData.request_type_id) {
                this.chosenRequestType =
                  this.combineAllRequestTypes.find((r) => r.id == this.paymentModalData.request_type_id) || {};
              } else {
                this.chosenRequestType =
                  this.combineAllRequestTypes.find((r) => r.id === this.request.request_type_id) ||
                  this.combineAllRequestTypes.find((r) => r.kind === this.request.kind) ||
                  {};
              }
            } else {
              this.chosenRequestType = {};
            }
          } else {
            // For Create Request
            if (this.combineAllRequestTypes.length > 0) {
              if (this.paymentModalData.request_type_id) {
                this.chosenRequestType =
                  this.combineAllRequestTypes.find((r) => r.id == this.paymentModalData.request_type_id) || {};
              } else {
                this.chosenRequestType = this.combineAllRequestTypes[0];
              }
            } else {
              this.chosenRequestType = {};
            }
          }
          this.isLoading = false;
        });
    },
    initialRequestTypeId() {
      if (this.request) {
        return this.defaultRequestTypeId(this.paymentModalData.request_type_id || this.request.request_type_id);
      } else if (this.paymentModalData.request_type_id) {
        return this.paymentModalData.request_type_id;
      } else if (this.combineAllRequestTypes.length > 0) {
        return this.combineAllRequestTypes[0].id;
      } else {
        return null;
      }
    },
  },
  mounted() {
    this.$store.dispatch('payments/setShowSubmitFormRequest', true);
    if (this.isAllowCustomFaults) {
      this.fetchRequestTypes();
    } else {
      this.chosenRequestType = this.combineAllRequestTypes[0];
    }
  },
};
</script>

<style lang="less">
.request-sticky-bottom {
  overflow-y: auto;
}
.request-drawer__footer {
  position: absolute;
  bottom: 0;
  right: 0;
  background: white;
  width: 100%;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.1);

  .request-drawer__footer-left {
    p {
      margin-bottom: 0px;
      color: #52575c;
    }
    span {
      color: #ff6337;
    }
  }
  .request-drawer__footer-right {
    button:first-child {
      margin-right: 12px;
    }
  }
}
.request-v2-fault-report {
  padding: 24px 0 24px 35px;
  &.request-sticky-bottom {
    height: calc(100vh - 288px);
  }
  .request-drawer__body {
    margin-right: 35px;
    margin-bottom: 70px;
  }
}

.new-request-card {
  margin: 16px 0;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 0.75px solid #dbdde0;
  .ant-form-item-label {
    display: flex;
    float: left;
    // color: #ff6337;
    width: 90%;
    label {
      color: #ff6337;
    }
  }
}
</style>
