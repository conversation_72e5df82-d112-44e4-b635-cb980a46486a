<template>
  <fragment>
    <div>
      <a-badge :count="requestCount" :overflow-count="999">
        <a-button
          size="large"
          style="width: 40px; height: 40px; padding: 0;"
          @click="showDrawer"
          :class="activeButtonClass"
        >
          <a-badge dot v-if="isExistFilters">
            <a-icon :component="FilterIcon" />
          </a-badge>
          <a-icon :component="FilterIcon" v-else />
        </a-button>
      </a-badge>
    </div>
    <a-drawer
      title="Request Filters"
      :width="600"
      :visible="visible"
      @close="onClose"
      :getContainer="'.fc-main-drawer'"
      destroyOnClose
    >
      <a-form :form="form" layout="vertical">
        <a-row :gutter="[16, 16]">
          <a-col :span="12">
            <a-form-item label="Status" :colon="false" style="margin-bottom: 0">
              <a-select
                size="large"
                style="width: 100%"
                allowClear
                showSearch
                placeholder="All Status"
                mode="multiple"
                v-decorator="['statuses', { initialValue: this.defaultArrayByKey('statuses') }]"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option v-for="option in requestAllStatus" :key="option.value">
                  <FcStatusTag :status="option.title" />
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Priority" :colon="false" style="margin-bottom: 0">
              <a-select
                size="large"
                style="width: 100%"
                allowClear
                showSearch
                placeholder="All Priorities"
                mode="multiple"
                v-decorator="['priorities', { initialValue: this.defaultArrayByKey('priorities') }]"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option v-for="(option, index) in requestAllPriorities" :key="index" :value="option">
                  <v-2-request-priority :priority="option" />
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item label="Request Type" :colon="false" style="margin-bottom: 0">
              <a-select
                size="large"
                style="width: 100%"
                allowClear
                showSearch
                placeholder="All Request Types"
                :filterOption="filterOption"
                mode="multiple"
                v-decorator="['request_type_ids', { initialValue: this.defaultArrayByKey('request_type_ids') }]"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option v-for="option in requestAllRequestType" :key="option.value">
                  {{ option.title }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0 !important">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Fault Types</span>
                  <a-checkbox :checked="this.hiddenFields.fault_type" @change="handleChangeHiddenFields('fault_type')">
                    <span class="request-filter-form-label-text">
                      No Fault Type
                    </span>
                  </a-checkbox>
                </div>
              </template>
              <fault-types-tree-select
                v-if="!hiddenFields.fault_type"
                :multiple="true"
                size="large"
                placeholder="All Fault Types"
                :showSearch="true"
                :getPopupContainer="(trigger) => trigger.parentNode"
                dispatchType="fault_types/loadAllNoPagination"
                queryDispatchType="fault_types/loadAllNoPagination"
                :form="form"
                :vDecorator="[
                  'facility_ids',
                  {
                    initialValue: this.defaultArrayByKey('facility_ids'),
                  },
                ]"
                type="FAULT_TYPES"
                :payload="{ data_sort: 'name', order_sort: 'asc' }"
              >
              </fault-types-tree-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]" class="request-filter-corrective">
          <a-col :span="24">
            <a-form-item>
              <a-checkbox
                v-decorator="[
                  'has_corrective_fault_report_and_requests',
                  {
                    valuePropName: 'checked',
                    initialValue: this.defaultArrayByKey('has_corrective_fault_report_and_requests'),
                  },
                ]"
              >
                <span>
                  Show only Corrective Fault Reports / Requests
                </span>
              </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Assigned to</span>
                  <a-checkbox
                    :checked="this.hiddenFields.assigned_to"
                    @change="handleChangeHiddenFields('assigned_to')"
                  >
                    <span class="request-filter-form-label-text">
                      Not Assigned
                    </span>
                  </a-checkbox>
                </div>
              </template>
              <form-item-account-and-tag-select
                v-if="!hiddenFields.assigned_to"
                placeholder="All Accounts"
                v-decorator="[
                  'assign_manager_ids',
                  {
                    initialValue: defaultAccountIds(),
                  },
                ]"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Request By</span>
                </div>
              </template>

              <a-select
                size="large"
                placeholder="Select Requestor"
                :showSearch="true"
                v-decorator="['request_by_ids', { initialValue: this.defaultArrayByKey('request_by_ids') }]"
                mode="multiple"
                :getPopupContainer="(trigger) => trigger.parentNode"
                @search="handleSearchRequestor"
                @blur="handleBlurRequestor"
                :filterOption="false"
              >
                <a-select-option v-for="option in requestors" :key="option.id">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <span>{{ option.full_name }}</span>
                    <fc-status-tag v-if="option.role" :status="option.role" />
                  </div>
                </a-select-option>

                <a-select-option :key="'loadMore_' + requestorsPage" :disabled="true">
                  <infinite-loading @infinite="infiniteHandler" ref="infiniteLoadingRequestors">
                    <div slot="spinner"><a-spin /></div>
                    <div slot="no-more"></div>
                    <div slot="no-results"></div>
                  </infinite-loading>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Location Tags</span>
                  <a-checkbox
                    :checked="this.hiddenFields.location_tag"
                    @change="handleChangeHiddenFields('location_tag')"
                  >
                    <span class="request-filter-form-label-text">
                      No Location Tag
                    </span>
                  </a-checkbox>
                </div>
              </template>
              <location-tags-tree-select-v-2
                v-if="!hiddenFields.location_tag"
                :multiple="true"
                size="large"
                placeholder="All Location Tags"
                :showSearch="true"
                :getPopupContainer="(trigger) => trigger.parentNode"
                dispatchType="fault_locations/loadAllFaultLocationsV2"
                queryDispatchType="fault_locations/loadAllFaultLocationsV2"
                :form="form"
                :vDecorator="[
                  'fault_location_ids',
                  {
                    initialValue: this.defaultArrayByKey('fault_location_ids'),
                  },
                ]"
                type="LOCATION_TAGS"
                :payload="{ data_sort: 'name', order_sort: 'asc' }"
                :initialValue="this.defaultArrayByKey('fault_location_ids')"
              >
              </location-tags-tree-select-v-2>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Problem Codes</span>
                  <a-checkbox
                    :checked="this.hiddenFields.problem_code"
                    @change="handleChangeHiddenFields('problem_code')"
                  >
                    <span class="request-filter-form-label-text">
                      No Problem Code
                    </span>
                  </a-checkbox>
                </div>
              </template>
              <a-select
                v-if="!hiddenFields.problem_code"
                size="large"
                style="width: 100%"
                allowClear
                showSearch
                placeholder="All Problem Codes"
                :filterOption="filterOption"
                mode="multiple"
                v-decorator="['problem_code_ids', { initialValue: this.defaultFailureCode('problem_code_ids') }]"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option v-for="option in problem_codes" :key="option.id">
                  {{ option.code }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Cause Codes</span>
                  <a-checkbox :checked="this.hiddenFields.cause_code" @change="handleChangeHiddenFields('cause_code')">
                    <span class="request-filter-form-label-text">
                      No Cause Code
                    </span>
                  </a-checkbox>
                </div>
              </template>
              <a-select
                v-if="!hiddenFields.cause_code"
                size="large"
                style="width: 100%"
                allowClear
                showSearch
                placeholder="All Cause Codes"
                :filterOption="filterOption"
                mode="multiple"
                v-decorator="['cause_code_ids', { initialValue: this.defaultFailureCode('cause_code_ids') }]"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option v-for="option in cause_codes" :key="option.id">
                  {{ option.code }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item :colon="false" style="margin-bottom: 0">
              <template slot="label">
                <div class="request-filter-form-label">
                  <span class="request-filter-form-label-text">Remedy Codes</span>
                  <a-checkbox
                    :checked="this.hiddenFields.remedy_code"
                    @change="handleChangeHiddenFields('remedy_code')"
                  >
                    <span class="request-filter-form-label-text">
                      No Remedy Code
                    </span>
                  </a-checkbox>
                </div>
              </template>
              <a-select
                v-if="!hiddenFields.remedy_code"
                size="large"
                style="width: 100%"
                allowClear
                showSearch
                placeholder="All Remedy Codes"
                :filterOption="filterOption"
                mode="multiple"
                v-decorator="['remedy_code_ids', { initialValue: this.defaultFailureCode('remedy_code_ids') }]"
                :getPopupContainer="(trigger) => trigger.parentNode"
              >
                <a-select-option v-for="option in remedy_codes" :key="option.id">
                  {{ option.code }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :span="24">
            <a-form-item label="Date Range" :colon="false" style="margin-bottom: 60px">
              <a-range-picker
                size="large"
                style="width: 100%"
                :format="dateFormat"
                @change="handleRangePickerChange"
                :disabledDate="disabledDate"
                v-decorator="['range_time', { initialValue: defaultDate }]"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button size="large" :style="{ marginRight: '8px' }" @click="handleClearFilterClick">Clear Filters</a-button>
        <a-button size="large" type="primary" @click="handleApplyFilter">
          Apply
        </a-button>
      </div>
    </a-drawer>
  </fragment>
</template>

<script>
import _ from 'lodash';
import { mapState } from 'vuex';
import FcStatusTag from '@/components/Shared/FcStatusTag.vue';
import V2RequestPriority from '@/components/Requests/Core/V2RequestPriority.vue';
import LocationTagsTreeSelect from '@/components/Shared/LocationTagsTreeSelect.vue';
import FormItemAccountAndTagSelect from '@/components/Shared/DynamicForm/FormItemAccountAndTagSelect.vue';
import FaultTypesTreeSelect from '../../Shared/FaultTypesTreeSelect.vue';
import LocationTagsTreeSelectV2 from '@/components/Shared/LocationTagsTreeSelectV2.vue';
import { FilterIcon } from '~/components/Icons';
import FcEmpty from '@/components/Shared/FcEmpty.vue';
import { convertValuesToString } from '@/utils/helper';

export default {
  data() {
    return {
      dateFormat: 'DD/MM/YYYY',
      FilterIcon,
      visibleFilter: false,
      visible: false,
      form: this.$form.createForm(this),
      locationTagsPage: 1,
      faultTypesPage: 1,
      isEnd: false,
      isEndFaultTypes: false,
      requestAllPriorities: ['low', 'medium', 'high'],
      isEnableCorrectiveRequest: false,
      startDate: undefined,
      endDate: undefined,
      curFaultTypes: [],
      isInitData: false,
      hiddenFields: {
        fault_type: false,
        assigned_to: false,
        location_tag: false,
        problem_code: false,
        cause_code: false,
        remedy_code: false,
      },
      requestorsPage: 1,
      q: '',
    };
  },
  components: {
    FcStatusTag,
    V2RequestPriority,
    LocationTagsTreeSelect,
    FormItemAccountAndTagSelect,
    FaultTypesTreeSelect,
    LocationTagsTreeSelectV2,
    FcEmpty,
  },
  mounted() {
    this.$store.dispatch('managers/loadAll');
  },
  watch: {
    queryString: {
      handler(newVal) {
        this.defaultHiddenFields();
      },
      immediate: true,
    },
    visible: {
      handler(newVal) {
        if (newVal) {
          this.fetchAllRequestors();
        }
      },
    },
  },
  computed: {
    ...mapState({
      requestAllStatus: (state) => state.requests.requestAllStatus,
      requestAllRequestType: (state) => state.requests.requestAllRequestType,
      requestAllFaultType: (state) => state.v2.fault_types.fault_types,
      requestAllFaultSubType: (state) => state.v2.fault_sub_types.fault_sub_types,
      requestFilterCondition: (state) => state.requests.requestFilterCondition,
      problem_codes: (state) => state.failure_codes.problem_codes,
      cause_codes: (state) => state.failure_codes.cause_codes,
      remedy_codes: (state) => state.failure_codes.remedy_codes,
      pageInformation: (state) => state.requests.pagination,
      isLoaded: (state) => state.requests.isLoaded,
      filterPayload: (state) => state.requests.filterPayload,
      requestFilter: (state) => state.filter_remembered.request,
      requestors: (state) => state.requests.requestors,
    }),
    queryString() {
      return { ...this.$route.query };
    },
    defaultDate() {
      return this.queryString.start_date && this.queryString.end_date
        ? [moment(this.queryString.start_date, this.dateFormat), moment(this.queryString.end_date, this.dateFormat)]
        : undefined;
    },
    filtersQuery() {
      let query = { ...this.$route.query };
      delete query.request_id;
      delete query.conversation_id;
      delete query.page;
      delete query.data_sort;
      delete query.order_sort;
      delete query.per_page;
      delete query.q;
      query = _.omitBy(query, _.isNil);
      return query;
    },
    isExistFilters() {
      const isExistFilters = !_.isEmpty(this.filtersQuery);
      return isExistFilters;
    },
    activeButtonClass() {
      return this.isExistFilters ? 'fc-active-button' : '';
    },
    requestCount() {
      return this.isExistFilters && this.isLoaded ? this.countNumber : 0;
    },
    countNumber() {
      return this.pageInformation.count;
    },
  },
  methods: {
    handleRangePickerChange(_date, dateString) {
      this.startDate = dateString[0];
      this.endDate = dateString[1];
    },
    disabledDate(current) {
      return current && current > moment().endOf('day');
    },
    handleChangeHiddenFields(key) {
      this.hiddenFields[key] = !this.hiddenFields[key];
    },
    defaultFailureCode(key) {
      if (this.queryString['failure_code_ids']) {
        const failureCodeIds = JSON.parse(this.queryString['failure_code_ids']);
        if (failureCodeIds.length) {
          if (key === 'problem_code_ids') {
            return this.problem_codes.filter((item) => failureCodeIds.includes(item.id)).map((item) => item.id);
          }
          if (key === 'cause_code_ids') {
            return this.cause_codes.filter((item) => failureCodeIds.includes(item.id)).map((item) => item.id);
          }
          if (key === 'remedy_code_ids') {
            return this.remedy_codes.filter((item) => failureCodeIds.includes(item.id)).map((item) => item.id);
          }
        }
      }
    },
    defaultHiddenFields() {
      let blankResults = this.queryString['blank_results'] ? JSON.parse(this.queryString['blank_results']) : [];
      for (const key of Object.keys(this.hiddenFields)) {
        this.hiddenFields[key] = blankResults.includes(key);
      }
    },
    defaultArrayByKey(key) {
      if (key === 'facility_ids') {
        let defaultFacilityIds = [];
        defaultFacilityIds.push(
          ...(this.queryString['facility_ids']
            ? JSON.parse(this.queryString['facility_ids']).map((value) => `${value}`)
            : []),
        );
        defaultFacilityIds.push(
          ...(this.queryString['fault_sub_type_tag_ids']
            ? JSON.parse(this.queryString['fault_sub_type_tag_ids']).map((value) => `sub_${value}`)
            : []),
        );
        return defaultFacilityIds.length ? defaultFacilityIds : undefined;
      }
      let value = this.queryString[key] ? JSON.parse(this.queryString[key]) : undefined;
      return value;
    },
    defaultAccountIds() {
      let account_ids = this.queryString.assign_manager_ids ? JSON.parse(this.queryString.assign_manager_ids) : [];
      let account_tag_ids = this.queryString.account_tag_ids ? JSON.parse(this.queryString.account_tag_ids) : [];
      let selectedAccountAndTags = [...account_ids, ...account_tag_ids.map((tagId) => `tags_${tagId}`)];
      return selectedAccountAndTags;
    },
    handleClearFilterClick() {
      this.form.resetFields();
      this.startDate = undefined;
      this.endDate = undefined;
      let query = {
        page: 1,
        per_page: 10,
        order_sort: this.queryString['order_sort'],
        data_sort: this.queryString['data_sort'],
      };

      this.$router.push({ path: this.$route.path, query });

      this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', { property: 'request', value: query }),
        this.onClose();
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    showDrawer() {
      this.isInitData = true;
      this.visible = true;
    },
    onClose() {
      this.visible = false;
    },
    handleFaultTypeChange(value) {
      if (value.length < this.curFaultTypes.length) {
        let removeId = this.curFaultTypes.find((item) => !value.includes(item));
        this.$store
          .dispatch('requests/loadDetailsFaultSubTypes', removeId)
          .then((res) => {
            this.handleRemoveFaltSubTypes(res.data);
          })
          .catch((err) => {
            this.$message.error(err.response.data.message);
          });
      } else {
        let addId = value.find((item) => !this.curFaultTypes.includes(item));
        this.$store
          .dispatch('requests/loadDetailsFaultSubTypes', addId)
          .then((res) => {})
          .catch((err) => {
            this.$message.error(err.response.data.message);
          });
      }
      this.curFaultTypes = value;
    },
    handlePopulateFaultSubTypes(data) {
      data.forEach((item) => {
        this.$store.dispatch('v2/fault_sub_types/addFaultTypeToFaultTypes', item);
      });
      let faultTypeIds = this.form.getFieldValue('fault_sub_type_tag_ids') ?? [];
      let addIds = data.filter((item) => !faultTypeIds.includes(item.id));
      this.form.setFieldsValue({ fault_sub_type_tag_ids: [...addIds.map((item) => item.id), ...faultTypeIds] });
    },
    handleRemoveFaltSubTypes(data) {
      const faultTypeIds = this.form.getFieldValue('fault_sub_type_tag_ids') ?? [];
      const removeIds = data.map((item) => item.id);
      const removedFaultTypeIds = faultTypeIds.filter((item) => !removeIds.includes(item));
      this.form.setFieldsValue({ fault_sub_type_tag_ids: [...removedFaultTypeIds] });
    },
    getAccountAndTagIds(ids) {
      const account_ids = [];
      const accountTag_ids = [];
      ids.forEach((id) => {
        if (typeof id === 'string' && id.includes('tags')) {
          accountTag_ids.push(Number.parseInt(id.split('_')[1]));
        } else {
          account_ids.push(id);
        }
      });

      if (ids.find((id) => id == 'manage')) {
        return {};
      }

      return { account_ids, accountTag_ids };
    },
    handleApplyFilter() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const {
            problem_code_ids,
            cause_code_ids,
            remedy_code_ids,
            assign_manager_ids,
            ..._payload
          } = convertValuesToString(values);
          const failureCodeIds = [
            ...(problem_code_ids ? JSON.parse(problem_code_ids) : []),
            ...(cause_code_ids ? JSON.parse(cause_code_ids) : []),
            ...(remedy_code_ids ? JSON.parse(remedy_code_ids) : []),
          ];
          const { account_ids, accountTag_ids } = this.getAccountAndTagIds(
            assign_manager_ids ? JSON.parse(assign_manager_ids) : [],
          );
          const hiddenFields = [];
          for (const [key, value] of Object.entries(this.hiddenFields)) {
            if (value) {
              hiddenFields.push(key);
            }
          }

          const payload = {
            start_date: this.startDate,
            end_date: this.endDate,
            has_corrective_fault_report_and_requests: _payload?.has_corrective_fault_report_and_requests || false,
            failure_code_ids: JSON.stringify(failureCodeIds),
            assign_manager_ids: JSON.stringify(account_ids),
            account_tag_ids: JSON.stringify(accountTag_ids),
            assign_manager_tag_ids: JSON.stringify(accountTag_ids),
            blank_results: JSON.stringify(hiddenFields),
            ..._payload,
          };
          delete payload?.range_time;
          const query = _.omitBy(payload, _.isNil);
          !_.isEmpty(query) &&
            (this.$store.dispatch('requests/setFilterPayload', query),
            this.$store.dispatch('filter_remembered/setFilterPropertyAndPersist', {
              property: 'request',
              value: { ...query, isShowClearFilter: true },
            }),
            this.$router.push({
              path: this.$route.path,
              query: { ...this.$route.query, ...query },
            }));
          this.onClose();
        } else {
          this.$message.error('Can not apply filters, please try again!');
        }
      });
    },
    infiniteHandler($state) {
      this.$store
        .dispatch('requests/loadMoreRequestors', {
          ...(this.q ? { q: this.q } : {}),
          page: this.requestorsPage + 1,
          per_page: 25,
          source_ids: JSON.stringify([7]),
          data_sort: 'first_name',
          order_sort: 'asc',
        })
        .then((res) => {
          if (res.data.length) {
            this.requestorsPage += 1;
            $state.loaded();
          } else {
            $state.complete();
          }
        });
    },

    handleSearchRequestor: _.debounce(function(query) {
      this.q = query;
      this.$store
        .dispatch('requests/loadAllRequestors', {
          q: query,
          page: 1,
          per_page: 25,
        })
        .then((response) => {
          this.resetInfiniteLoading();
        });
    }, 600),

    resetInfiniteLoading() {
      this.requestorsPage = 1;
      if (this.$refs.infiniteLoadingRequestors && this.$refs.infiniteLoadingRequestors.stateChanger) {
        this.$refs.infiniteLoadingRequestors.stateChanger.reset();
      }
    },

    handleBlurRequestor() {
      this.q = '';
      this.fetchAllRequestors();
      this.resetInfiniteLoading();
    },

    fetchAllRequestors() {
      this.$store
        .dispatch('requests/loadAllRequestors', {
          page: 1,
          per_page: 25,
          data_sort: 'first_name',
          order_sort: 'asc',
        })
        .then(() => {
          if (_.get(this.queryString, 'request_by_ids', false)) {
            this.$store.dispatch('requests/loadSelectedRequestors', {
              ids: this.queryString.request_by_ids,
              data_sort: 'first_name',
              order_sort: 'asc',
            });
          }
        });
    },
  },
};
</script>

<style lang="less">
.card-filter {
  background: #ffffff;
  width: 500px;
}

.slide-out-enter-active,
.slide-out-leave-active {
  transition: all 0.2s;
}

.slide-out-enter,
.slide-out-leave-to {
  opacity: 0;
  transition: all 0.2s;
}

.request-filter-corrective {
  .ant-form-item {
    margin-bottom: 0px;
    padding-bottom: 0px;
  }

  span {
    color: #a0a4a8;
    font-size: 16px;
  }
}

.request-fault-sub-types {
  .ant-select-selection__rendered {
    ul {
      overflow: auto;
      max-height: 250px;

      .ant-select-search {
        overflow: hidden;
      }
    }
  }
}

.request-filter-form-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
  padding-bottom: 0px;
  .request-filter-form-label-text {
    color: #a0a4a8;
  }
}
</style>
