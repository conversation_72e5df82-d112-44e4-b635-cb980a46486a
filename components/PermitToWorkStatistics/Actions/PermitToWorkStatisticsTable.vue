<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center; padding: 13px 14px">
      <div class="fc-group__left">
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button
          v-if="isShowExportBtn"
          type="primary"
          class="fc-group-button yellow"
          @click="onClickExportCSV"
          :loading="isCSVLoading"
          icon="download"
        >
          Export CSV
        </a-button>
        <fc-add-to-dashboard :type="dashboardType" v-if="!shownInDashboard" />
      </div>
    </div>
    <div>
      <fc-table
        :columns="columns"
        rowKey="permit_to_work_type_id"
        :dataSource="tableData"
        :loading="loading"
        scrollX="40vw"
        @onTableChange="handleTableChange"
      >
        <template v-for="(meta, index) in statistic_metadata" :slot="meta.slot" slot-scope="text, record">
          <a v-if="text > 0" :key="index" @click="onClickNumber(record, meta.key)">{{ text }}</a>
          <span v-else :key="index">{{ text }}</span>
        </template>
      </fc-table>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { ExportIcon } from '@/components/Icons';
import FcSearch from '@/components/Shared/FcSearch.vue';
import FcAddToDashboard from '@/components/Shared/FcAddToDashboard.vue';
import { generateAndDownloadCsv } from '@/utils/helper';

const statistic_metadata = [
  {
    slot: 'pending',
    key: 'ids_pending',
  },
  {
    slot: 'processing',
    key: 'ids_processing',
  },
  {
    slot: 'completed',
    key: 'ids_complete',
  },
  {
    slot: 'canceled',
    key: 'ids_canceled',
  },
];
export default {
  components: {
    FcSearch,
    FcAddToDashboard,
  },
  data() {
    return {
      ExportIcon,
      statistic_metadata,
      loading: false,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      isCSVLoading: false,
      dashboardType: 'permit_to_work_data_table',
    };
  },

  props: {
    dataProp: {
      type: Array,
      default: () => [],
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    handleInputSearchSubmit(value) {
      this.q = value.toLowerCase();
    },
    handleTableChange({ pagination, filters, sorter }) {
      if (!sorter.order) {
        this.sorter = {
          data_sort: undefined,
          order_sort: undefined,
        };
      } else {
        this.sorter = {
          data_sort: sorter.field,
          order_sort: sorter.order,
        };
      }
    },

    onClickNumber(record, type) {
      const selectedIds = record?.permit_to_work_ids[`${type}`];

      let routeData = this.$router.resolve({
        path: '/features/permit_to_work',
        query: { ids: JSON.stringify(selectedIds) },
      });
      window.open(routeData.href, '_blank', 'noopener,noreferrer');
    },

    onClickExportCSV() {
      const exportData = this.tableData.map((item) => ({
        name: item.name,
        pending: item.status_statistic.pending,
        processing: item.status_statistic.processing,
        complete: item.status_statistic.complete,
        canceled: item.status_statistic.canceled,
      }));
      const headers = {
        name: 'Permit To Work Type',
        pending: 'Application',
        processing: 'Processing',
        complete: 'Complete',
        canceled: 'Canceled',
      };

      generateAndDownloadCsv(exportData, 'permit_to_work_statistics', headers);
    },
  },

  computed: {
    ...mapState({
      permitToWorkDataFilter: (state) => state.permit_to_work_statistics.permitToWorkDataFilter,
    }),

    tableData() {
      let data = this.dataProp.slice();

      if (this.q.length) {
        data = data.filter((r) => r.name.toLowerCase().search(this.q.toLowerCase()) > -1);
      }

      if (this.sorter && this.sorter.data_sort && this.sorter.order_sort) {
        data.sort((a, b) => {
          const { data_sort, order_sort } = this.sorter;
          let aVal = a;
          let bVal = b;
          if (order_sort === 'descend') {
            aVal = b;
            bVal = a;
          }
          switch (data_sort) {
            case 'name':
              return aVal.name.localeCompare(bVal.name);
            case 'status_statistic[pending]':
              return aVal.status_statistic.pending - bVal.status_statistic.pending;
            case 'status_statistic[processing]':
              return aVal.status_statistic.processing - bVal.status_statistic.processing;
            case 'status_statistic[complete]':
              return aVal.status_statistic.complete - bVal.status_statistic.complete;
            case 'status_statistic[canceled]':
              return aVal.status_statistic.canceled - bVal.status_statistic.canceled;
          }
        });
      }

      return data;
    },

    columns() {
      const description = {
        title: 'Description',
        dataIndex: 'description',
      };
      const columns = [
        {
          title: 'Permit To Work Type',
          dataIndex: 'name',
          sorter: true,
        },
        {
          title: (
            <a-tooltip title="Status of Permit to Work has not yet changed since application">Application</a-tooltip>
          ),
          dataIndex: 'status_statistic[pending]',
          scopedSlots: { customRender: 'pending' },
          sorter: true,
        },
        {
          title: (
            <a-tooltip title="Status of the Permit to Work has been changed at least once since application, but not yet Complete">
              Processing
            </a-tooltip>
          ),
          dataIndex: 'status_statistic[processing]',
          scopedSlots: { customRender: 'processing' },
          sorter: true,
        },
        {
          title: (
            <a-tooltip title="Permit to Work application has reached the last stage in the process flow">
              Complete
            </a-tooltip>
          ),
          dataIndex: 'status_statistic[complete]',
          scopedSlots: { customRender: 'completed' },
          sorter: true,
        },
        {
          title: 'Canceled',
          dataIndex: 'status_statistic[canceled]',
          scopedSlots: { customRender: 'canceled' },
          sorter: true,
        },
      ];

      if (this.tableType === 'Asset') {
        columns.splice(1, 0, description);
        return columns;
      } else return columns;
    },

    nameOfColumn() {
      return this.tableType === 'Asset' ? 'asset_id' : 'name';
    },
  },

  watch: {
    sorter(newVal) {
      if (newVal) {
        this.$store.dispatch('permit_to_work_statistics/setPermitToWorkData', this.tableData);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.fc-group-button {
  width: 100% !important;
}
</style>
