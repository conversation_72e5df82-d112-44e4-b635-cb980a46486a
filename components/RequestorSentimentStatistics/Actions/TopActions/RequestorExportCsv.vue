<template>
  <a-button type="primary" class="fc-group-button yellow" @click="onClickExportCSV" :loading="isLoading">
    <a-icon :component="ExportIcon" />
    Export CSV
  </a-button>
</template>

<script>
import { mapState } from 'vuex';
import { ExportIcon } from '@/components/Icons';
import { generateUUID } from '@/utils/helper';

export default {
  data() {
    return {
      ExportIcon,
      isLoading: false,
    };
  },
  computed: {
    ...mapState({
      filterDateRange: (state) => state.requestor_sentiment_statistics.dateRange,
      currentPayload: (state) => state.requestor_sentiment_statistics.currentPayload,
    }),
  },
  methods: {
    onClickExportCSV() {
      this.$message.loading({ content: 'Exporting...', key: 'updatable', duration: 0 });
      const export_uuid = generateUUID();
      this.initWebSocket(this.$config.websocketUrl, export_uuid);

      const filterPayload = {
        ...(this.currentPayload.q && { q: this.currentPayload.q }),
        ...(this.currentPayload.roles && { roles: this.currentPayload.roles }),
        ...(this.currentPayload.source_ids && { source_ids: this.currentPayload.source_ids }),
        ...(this.currentPayload.order_sort && {
          data_sort: this.currentPayload.data_sort,
          order_sort: this.currentPayload.order_sort,
        }),
      };

      this.$store.dispatch('requestor_sentiment_statistics/exportCsvRequestsByRequestor', {
        export_uuid: export_uuid,
        start_date: this.filterDateRange.start_date,
        end_date: this.filterDateRange.end_date,
        ...filterPayload,
      });
    },
    initWebSocket(url, export_uuid) {
      let socket = new WebSocket(url);
      this.isLoading = true;
      let _this = this;

      socket.onopen = function(event) {
        var message;
        console.log('[WEBSOCKET] Connected to server.');
        message = {
          command: 'subscribe',
          identifier: JSON.stringify({
            export_uuid: export_uuid,
            channel: 'ExportChannel',
          }),
        };
        return socket.send(JSON.stringify(message));
      };

      socket.onmessage = function(event) {
        var download_element, message;
        message = JSON.parse(event.data);
        if (message.type === 'ping') {
          return;
        }
        if (message.message) {
          let response = message.message;
          _this.isLoading = false;
          _this.$message.destroy();
          if (response.status === 400) {
            _this.$message.error(response.messages);
          } else {
            download_element = document.createElement('a');
            download_element.href = response.download_url;
            download_element.click();
          }
          return socket.close();
        }
      };
      socket.onclose = function(event) {
        return console.log('[WEBSOCKET] Disconnected from server.');
      };
      return (socket.onerror = function(error) {
        return console.log('[WEBSOCKET] Error');
      });
    },
  },
};
</script>

<style></style>
