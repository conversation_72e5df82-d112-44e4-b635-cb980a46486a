<template>
  <div>
    <div class="fc-group-on-top" style="align-items: center; padding: 13px 14px">
      <div class="fc-group__left">
        <fc-search v-on:onInputSearchSubmit="handleInputSearchSubmit" />
      </div>
      <div class="fc-group__right">
        <a-button
          v-if="isShowExportBtn"
          type="primary"
          class="fc-group-button yellow"
          :loading="isLoading"
          @click="exportCSV"
        >
          <a-icon :component="ExportIcon" />
          Export CSV
        </a-button>
      </div>
    </div>
    <div>
      <a-table
        :columns="columns"
        :dataSource="tableData"
        :loading="isLoading"
        v-bind="scrollYProperty"
        @change="handleTableChange"
      >
        <template slot="score">
          <div>
            Score
            <a-tooltip>
              <template slot="title">{{ tooltip }}</template>
              <a-icon type="info-circle" class="btn-tooltip" theme="filled" />
            </a-tooltip>
          </div>
        </template>
        <template slot="number_of_users" slot-scope="text, record">
          <a
            v-if="record"
            :href="`/settings/accounts_settings?tab=requestor_settings&guest_ids=${JSON.stringify(record.guest_id)}`"
            target="_blank"
            rel="noopener noreferrer"
          >
            {{ text }}
          </a>
        </template>
        <span slot="percentage" slot-scope="text"> {{ text }}% </span>
      </a-table>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { ExportIcon } from '@/components/Icons';
import FcSearch from '@/components/Shared/FcSearch.vue';
import { generateAndDownloadCsv } from '@/utils/helper';

const columns = [
  {
    slots: { title: 'score' },
    dataIndex: 'avg_sentiment',
    sorter: (a, b) => {
      // Extract the starting number from the string and compare as numbers
      return parseFloat(a.avg_sentiment) - parseFloat(b.avg_sentiment);
    },
  },
  {
    title: 'No. of Requestors',
    dataIndex: 'number_of_users',
    sorter: (a, b) => a.number_of_users - b.number_of_users,
    scopedSlots: { customRender: 'number_of_users' },
  },
  {
    title: '% of Total',
    dataIndex: 'percentage',
    scopedSlots: { customRender: 'percentage' },
    sorter: (a, b) => a.percentage - b.percentage,
  },
];
export default {
  props: {
    apiResponse: {
      type: Array,
    },
    isShowExportBtn: {
      type: Boolean,
      default: true,
    },
    shownInDashboard: {
      type: Boolean,
      default: false,
    },
    yScrollHeight: undefined,
  },
  components: {
    FcSearch,
  },
  data() {
    return {
      columns,
      ExportIcon,
      isLoading: false,
      q: '',
      sorter: {
        data_sort: undefined,
        order_sort: undefined,
      },
      tooltip: `A score of 3.0 is neutral.
                Scores below 3.0 indicate negative sentiment.
                Scores above 3.0 indicate positive sentiment`,
    };
  },
  methods: {
    handleInputSearchSubmit(value) {
      this.q = value.toLowerCase();
    },
    handleTableChange(pagination, filters, sorter) {
      if (!sorter.order) {
        this.sorter.data_sort = undefined;
        this.sorter.order_sort = undefined;
      } else {
        this.sorter.data_sort = sorter.field;
        this.sorter.order_sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      }
    },
    getSortedTableData() {
      if (!this.tableData || !this.tableData.length) {
        return [
          {
            avg_sentiment: null,
            number_of_users: null,
            percentage: null,
          },
        ];
      }

      return [...this.tableData].sort((a, b) => {
        if (!this.sorter.data_sort) return 0;
        const field = this.sorter.data_sort;
        const order = this.sorter.order_sort;

        if (field === 'avg_sentiment') {
          return order === 'asc'
            ? parseFloat(a.avg_sentiment) - parseFloat(b.avg_sentiment)
            : parseFloat(b.avg_sentiment) - parseFloat(a.avg_sentiment);
        }

        return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
      });
    },
    prepareExportData(data) {
      return data.map((item) => ({
        avg_sentiment: `${item.avg_sentiment}`,
        number_of_users: item.number_of_users,
        percentage: `${item.percentage}%`,
      }));
    },
    exportCSV() {
      this.$message.loading({ content: 'Exporting...', key: 'updatable' });
      this.isLoading = true;

      const sortedData = this.getSortedTableData();
      const exportData = this.prepareExportData(sortedData);
      const headers = {
        avg_sentiment: 'Score',
        number_of_users: 'No. of Requestors',
        percentage: '% of Total',
      };
      generateAndDownloadCsv(exportData, 'requestor_sentiment_statistics', headers);

      this.$message.destroy();
      this.$message.success('Exported successfully');
      this.isLoading = false;
    },
  },
  computed: {
    ...mapState({
      filterDateRange: (state) => state.requestor_sentiment_statistics.dateRange,
    }),

    scrollYProperty() {
      if (this.yScrollHeight) {
        return { scroll: { y: this.yScrollHeight } };
      }
    },
    trackingData() {
      return Object.keys(this.apiResponse).length ? this.apiResponse : [];
    },
    tableData() {
      if (this.q.length <= 0) {
        return this.trackingData;
      }

      return this.trackingData.filter((row) => {
        const rowSearchText = [row.avg_sentiment, row.number_of_users, row.percentage].join().toLowerCase();
        return rowSearchText.includes(this.q);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
