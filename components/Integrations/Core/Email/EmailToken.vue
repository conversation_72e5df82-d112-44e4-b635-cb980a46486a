<template>
  <div style="height: 100%">
    <div class="integrations-container" v-if="isLoaded">
      <div class="integrations-header">Email</div>
      <div class="integrations-form">
        <label style="margin-bottom: 9px; display: inline-block;">
          Requestors can send emails to this email address to create requests.
        </label>
        <div v-if="!this.isEdit" style="display: flex">
          <a-input style="margin: 0; margin-right: 8px" size="large" :disabled="true" v-model="emailAddress" />
          <a-button size="large" @click="onCopy">
            Copy
          </a-button>
          <a-button size="large" @click="onEdit">
            Edit
          </a-button>
        </div>
        <div v-else-if="this.isEdit" style="display: flex">
          <a-input
            class="custom-mail-data"
            :addon-before="mainEmailAddress"
            :addon-after="tailEmailAddress"
            size="large"
            v-model="extensionEmailAddress"
            placeholder="Extension request mail"
          />
          <a-button size="large" @click="onUpdate">
            Update
          </a-button>
        </div>
        <div class="email-integration-tip">
          <a-icon :component="YellowHintIcon" style="margin-right: 10px;"></a-icon>
          <span>
            <div class="email-integration-tip-text">
              Requests should be sent as fresh emails with plain text.
            </div>
            <div class="email-integration-tip-text">
              Only the first 5 image attachments will be captured.
            </div>
            <div class="email-integration-tip-text">
              All emails will be classified as Fault Reports unless Smart Email Parsing below is toggled on.
            </div>
            <div class="email-integration-tip-text">
              Non-image attachments will not be captured.
            </div>
            <div class="email-integration-tip-text">
              Attachments larger than 10 MB will not be captured.
            </div>
            <div class="email-integration-tip-text">
              Only the first 5000 characters in the email will be captured.
            </div>
          </span>
        </div>

        <br />

        <div class="smart-email-parsing">
          <label
            >Smart Email Parsing (Powered by Generative AI)
            <a-tag color="orange">Beta</a-tag>
          </label>
          <a-switch
            checked-children="On"
            un-checked-children="Off"
            :default-checked="smartEmailToggle"
            :disabled="!enable_generative_ai"
            @change="ToggleSmartEmailParsing($event)"
          />
          <div class="email-integration-tip">
            <a-icon :component="YellowHintIcon" style="margin-right: 10px;"></a-icon>
            <span>
              <div class="email-integration-tip-text">
                If Smart Email Parsing is toggled on, FacilityBot will parse emails sent to the email address above
                using Generative AI to (a) Classify the email based on Request Types (b) Populate the fields within the
                chosen Request Type.
              </div>
              <div class="email-integration-tip-text">
                Supported Request Type fields are: Description, Short Answer, Single Choice, Multiple Choice, Single
                Choice Dropdown, Multiple Choice Dropdown, Date / Time, Toggle
              </div>
              <div class="email-integration-tip-text">
                If no matching Request Type is detected or if compulsory fields within a Request Type cannot be
                populated, the email will be classified as a Fault Report.
              </div>
            </span>
          </div>
          <div class="prompt-container" v-if="smartEmailToggle">
            <div class="prompt-header">
              <p>
                Additional Prompt (Optional)
                <a-tooltip
                  title='Any additional instructions to guide the Generative AI agent when parsing the email (e.g. "All <NAME_EMAIL> should be classified as Request Type = Urgent")'
                >
                  <a-icon type="info-circle" theme="filled" />
                </a-tooltip>
              </p>
              <div class="actions">
                <div class="prompt-edit-actions">
                  <a-button type="primary" @click="handleUpdatePrompt">
                    Save
                  </a-button>
                </div>
              </div>
            </div>
            <a-textarea
              placeholder="Type your prompt here..."
              :rows="6"
              @change="handleChangePrompt"
              :defaultValue="defaultPrompt"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="spinning" v-else>
      <a-spin size="large" />
    </div>
  </div>
</template>

<script>
import './../index.less';
import { mapState } from 'vuex';
import FcPageTip from '@/components/Shared/FcPageTip.vue';
import { YellowHintIcon } from '@/components/Icons/index.js';
import _ from 'lodash';

export default {
  data() {
    return {
      YellowHintIcon,
      isEdit: false,
      mainEmailAddress: null,
      extensionEmailAddress: null,
      tailEmailAddress: null,
      prompt: '',
    };
  },
  components: {
    FcPageTip,
  },
  methods: {
    onCopy() {
      navigator.clipboard.writeText(this.emailAddress).then(
        () => {
          this.$message.success('Successfully Copied');
        },
        (err) => {
          this.$message.error(`Error occur when copied : ${err}`);
        },
      );
    },
    onEdit() {
      this.isEdit = true;
      this.mainEmailAddress = this.emailInfo.emailAddress.split(/[+@]/)[0] + '+';
      this.extensionEmailAddress = this.emailInfo.emailAddress.split(/[+@]/)[1];
      this.tailEmailAddress = '@' + this.emailInfo.emailAddress.split(/[+@]/)[2];
    },
    onUpdate() {
      this.$store
        .dispatch('integrations/setEmailAddress', { unique_string_mail: this.extensionEmailAddress })
        .then(() => {
          this.$store.dispatch('integrations/loadEmailAddress');
          this.isEdit = false;
        })
        .catch((err) => {
          this.isEdit = true;
          this.$message.error(err.response.data.message);
        });
    },
    ToggleSmartEmailParsing(value) {
      if (!this.enable_generative_ai) {
        this.$message.error('Generative AI is not supported for this instance.');
        value = false;
        return;
      }
      const payload = {
        is_enable_smart_email_parsing: value,
      };

      this.$store
        .dispatch('integrations/toggleSmartEmailParsing', payload)
        .then(() => {
          this.$message.success('Successfully Updated!');
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
    handleChangePrompt(e) {
      this.prompt = e.target.value;
    },
    handleUpdatePrompt() {
      const payload = {
        smart_email_parsing_prompt: this.prompt,
      };

      this.$store
        .dispatch('integrations/updateEmailPrompt', payload)
        .then(() => {
          this.$message.success('Successfully Updated!');
        })
        .catch((err) => {
          this.$message.error(err.response.data.message);
        });
    },
  },
  computed: {
    ...mapState({
      emailInfo: (state) => state.integrations.email_info,
      enable_generative_ai: (state) => state.managers.profile?.agent_management?.enable_generative_ai,
    }),
    isLoaded() {
      return this.emailInfo.isLoaded;
    },
    emailAddress() {
      return this.emailInfo.emailAddress;
    },
    smartEmailToggle() {
      return this.enable_generative_ai && this.emailInfo.enable_smart_email_parsing;
    },
    defaultPrompt() {
      return _.isEmpty(this.emailInfo.prompt) ? '' : this.emailInfo.prompt;
    },
  },
};
</script>

<style lang="less">
.custom-mail-data {
  input {
    margin: 0 !important;
  }
}
.spinning {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.email-integration-tip {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 12px 0px;
  background: #fff;
  border-bottom: 0.8px solid #eef1f5;

  .email-integration-tip-text {
    margin: 10px 0px;
  }
}

.smart-email-parsing {
  .prompt-container {
    margin-top: 20px;
    background: #f9f9fa;
    border: 1px solid #dbdde0;
    border-radius: 8px;
    padding: 15px 21px;
    .prompt-header {
      display: flex;
      justify-content: space-between;
      width: 100%;
      p {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      .ant-input {
        margin-top: 10px;
      }
    }
    .actions {
      display: flex;
      justify-content: flex-start;
      margin-top: 20px;
    }
  }
}
</style>
