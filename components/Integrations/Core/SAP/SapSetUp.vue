<template>
  <div class="integrations-container">
    <div class="integrations-header">SAP Configuration</div>
    <div class="sap-integrations-form">
      <a-form :form="form" @submit="handleSubmit">
        <div>
          <a-form-item label="SAP Host URL">
            <a-input
              placeholder="SAP Host URL"
              v-decorator="[
                'sap_host_url',
                {
                  initialValue: this.data.sap_host_url,
                  rules: [
                    {
                      required: true,
                      message: 'Please enter SAP Host URL',
                    },
                  ],
                },
              ]"
              size="large"
            />
          </a-form-item>
          <a-form-item label="IAS URL">
            <a-input
              placeholder="Your IAS URL"
              v-decorator="[
                'ias_url',
                {
                  initialValue: this.data.ias_url,
                  rules: [
                    {
                      required: true,
                      message: 'Please enter IAS URL',
                    },
                  ],
                },
              ]"
              size="large"
            />
          </a-form-item>
          <a-form-item label="Client ID">
            <a-input
              placeholder="Your Client ID"
              v-decorator="[
                'client_id',
                {
                  initialValue: this.data.client_id,
                  rules: [
                    {
                      required: true,
                      message: 'Please enter Client ID',
                    },
                  ],
                },
              ]"
              size="large"
            />
          </a-form-item>
          <a-form-item label="Client Secret">
            <a-input-password
              placeholder="Your Client Secret"
              v-decorator="[
                'client_secret',
                {
                  initialValue: this.data.client_secret,
                  rules: [
                    {
                      required: true,
                      message: 'Please enter Client Secret',
                    },
                  ],
                },
              ]"
              size="large"
            />
          </a-form-item>
          <a-form-item label="Edition">
            <a-input
              placeholder="SAP Edition"
              v-decorator="[
                'edition',
                {
                  initialValue: this.data.edition,
                  rules: [
                    {
                      required: true,
                      message: 'Please enter Edition',
                    },
                  ],
                },
              ]"
              size="large"
            />
          </a-form-item>
        </div>

        <a-button
          v-if="!isConfigured"
          size="large"
          class="fc-button-normal"
          html-type="submit"
          type="primary"
          :loading="isLoading"
        >
          Update
        </a-button>
        <div v-if="isConfigured" class="button-container">
          <a-button
            type="primary"
            size="large"
            class="fc-button-normal"
            :loading="isUnlinking"
            @click="handleUnlink"
            style="margin-right: 16px;"
          >
            Un-link SAP
          </a-button>
          <a-button type="primary" size="large" class="fc-button-normal" :loading="isSyncing" @click="handleUpdate">
            Update
          </a-button>
        </div>
      </a-form>
    </div>

    <a-divider></a-divider>

    <div class="setup-tips-container">
      <div class="integrations-header" id="setup-1">Setup Tips</div>
      <div class="setup-guide-download">
        <span>Download Setup Guide: </span>
        <a :href="pdfUrl" download target="_blank" rel="noopener noreferrer">
          <a-icon type="download"></a-icon> Download PDF
        </a>
      </div>
      <div class="setup-guide-pdf">
        <iframe :src="pdfUrl" width="100%" height="600px" frameborder="0" title="SAP Integration Guide PDF"> </iframe>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: Object,
  },
  data() {
    return {
      isLoading: false,
      isUnlinking: false,
      isSyncing: false,
      form: this.$form.createForm(this),
      pdfUrl:
        'https://cdn.facilitybot.co/production/attachments/files/001/357/603/original/FacilityBot_SAP_Integration_Guide.pdf?1752461097',
    };
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();
      this.isLoading = true;
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$store
            .dispatch('integrations/setupSap', { ...values })
            .then(() => {
              this.$store.dispatch('integrations/loadSapInfo');
              this.$message.success('SAP Integration Successfully Configured.');
              this.handleSync();
              this.isLoading = false;
            })
            .catch((error) => {
              this.isLoading = false;
              this.$message.error(error.response.message);
            });
        } else {
          this.isLoading = false;
        }
      });
    },
    handleUnlink() {
      this.isUnlinking = true;
      this.$store
        .dispatch('integrations/unlinkSap', this.data.id)
        .then(() => {
          this.$message.success('SAP Integration Successfully Unlinked.');
          this.isUnlinking = false;
        })
        .catch((error) => {
          this.isUnlinking = false;
          this.$message.error(error.response.message);
        });
    },
    handleSync() {
      this.isSyncing = true;
      this.$message.loading('Syncing SAP...');
      this.$store
        .dispatch('integrations/syncSap')
        .then((response) => {
          this.$message.destroy();
          this.$message.success(response.message);
          this.isSyncing = false;
        })
        .catch((error) => {
          this.$message.destroy();
          this.isSyncing = false;
          this.$message.error(error.response.message);
        });
    },
    handleUpdate() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$store.dispatch('integrations/updateSap', { ...values, id: this.data.id }).then(() => {
            this.handleSync();
          });
        }
      });
    },
  },
  computed: {
    isConfigured() {
      return this.data?.sap_host_url;
    },
  },
};
</script>

<style lang="less">
.button-container {
  display: flex;
}
.integrations-container {
  .integrations-header {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 24px;
  }
  .sap-integrations-form {
    max-width: 600px;
    .ant-form-item {
      margin-bottom: 12px;
    }
  }
}

.setup-tips-container {
  min-height: 800px;
}
</style>
