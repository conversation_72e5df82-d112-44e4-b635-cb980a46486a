import {
  RequestIcon,
  ExpendituresIcon,
  CheckListIcon,
  NewBookingIcon,
  StatisticIcon,
  RocketIcon,
  DashboardIcon,
  AssetIcon,
  BellIcon,
  WorkflowIcon,
  BroadcastIcon,
  FaqIcon,
  OtherIcon,
  QuickSetupIcon,
  SurveyIcon,
  UserIcon,
  SettingIcon,
  IntergationIcon,
  SignOutIcon,
  SorIcon,
  WorkplaceIcon,
  FacebookIcon,
  TelegramIcon,
  WhatsAppIcon,
  LineIcon,
  WidgetIcon,
  GoogleIcon,
  HotDeskIcon,
  StoreIcon,
  MqttIcon,
  RatingIcon,
  SlackIcon,
  TeamsIcon,
  AuditLogIcon,
  SmileIcon,
  VendorIcon,
  EmailIcon,
  BillingIcon,
  JmmIcon,
  ProcoreIcon,
  CustomerIcon,
  CustomPageIcon,
  CalendarIcon,
  SsoSignInIcon,
  SustainabilityIcon,
  EmailOutlinedIcon,
  PeopleCounterIcon,
  PermitToWorkIcon,
  UhooIcon,
  DeliveryIcon,
  CpfIcon,
  LeaseIcon,
  MicrosoftIcon,
  MapMarkIcon,
} from '~/components/Icons/index.js';
import {
  ApiIcon,
  ChatIcon,
  PortfolioTokenIcon,
  ZapierIcon,
  QrCodeIcon,
  MeterIcon,
  PartIcon,
  AttendanceIcon,
  MqttNoColorIcon,
  LicenseIcon,
  ViberIcon,
  BudgetIcon,
  ContractIcon,
  PaymentIcon,
  BimIcon,
  AnalyticIcon,
  ExceltecIcon,
} from '~/components/Icons/index.js';
import { default_custom_permissions, default_agent_management } from './routes';
import { routes } from './routes';
import SapIcon from '../components/Icons/SapIcon.vue';
import AtIcon from '../components/Icons/AtIcon.vue';

export const NAV_ITEMS = {
  dashboard: {
    name: 'Dashboard',
    path: '/dashboard',
    key: 'dashboard',
    icon: DashboardIcon,
  },
  features: {
    name: 'Features',
    path: '/features/requests',
    key: 'features',
    icon: RocketIcon,
  },
  statistics: {
    name: 'Statistics',
    path: '/statistics/request_statistics',
    key: 'statistics',
    icon: StatisticIcon,
  },
  analytics: {
    name: 'Analytics',
    path: '/analytics',
    key: 'analytics',
    icon: AnalyticIcon,
  },
  settings: {
    name: 'Settings',
    path: '/settings/quick_setup',
    key: 'settings',
    icon: SettingIcon,
  },
  integrations: {
    name: 'Integrations',
    path: '/integrations/telegram_integrations',
    key: 'integrations',
    icon: IntergationIcon,
  },
  sign_out: {
    name: 'Sign Out',
    path: '/sign_out',
    key: 'sign_out',
    icon: SignOutIcon,
  },
  subscription_plans: {
    name: 'Subscription Plan',
    path: '/subscription_plans',
    key: 'subscription_plans',
    icon: OtherIcon,
  },
};

export const NAV_NOT_ACTIVE_ACCOUNT = [NAV_ITEMS.subscription_plans, NAV_ITEMS.sign_out];

export const MANAGER_SUB_NAV_ITEMS = {
  /*******************************************
    Features Menu
  *******************************************/
  requests: {
    name: 'Requests',
    path: '/features/requests',
    key: 'requests',
    icon: RequestIcon,
  },
  checklists: {
    name: 'Checklists',
    path: '/features/checklists',
    key: 'checklists',
    icon: CheckListIcon,
  },
  assets: {
    name: 'Assets',
    path: '/features/assets',
    key: 'assets',
    icon: AssetIcon,
  },
  expenditures: {
    name: 'Expenditures',
    path: '/features/expenditures',
    key: 'expenditures',
    icon: ExpendituresIcon,
  },
  facility_bookings: {
    name: 'Facilities',
    path: '/features/facility_bookings',
    key: 'facility_bookings',
    icon: NewBookingIcon,
  },
  chats: {
    name: 'Chats',
    path: '/features/conversations',
    key: 'conversations',
    icon: ChatIcon,
  },
  surveys: {
    name: 'Surveys',
    path: '/features/surveys',
    key: 'surveys',
    icon: SurveyIcon,
  },
  broadcasts: {
    name: 'Broadcasts',
    path: '/features/broadcasts',
    key: 'broadcasts',
    icon: BroadcastIcon,
  },
  visitors: {
    name: 'Visitors',
    path: '/features/visitors',
    key: 'visitors',
    icon: UserIcon,
  },
  desk_bookings: {
    name: 'Desks',
    path: '/features/desk_bookings',
    key: 'desk_bookings',
    icon: HotDeskIcon,
  },
  deliveries: {
    name: 'Deliveries',
    path: '/features/deliveries',
    key: 'deliveries',
    icon: DeliveryIcon,
  },
  procurement: {
    name: 'Procurement',
    path: '/features/procurement',
    key: 'procurement',
    icon: StoreIcon,
  },
  meters: {
    name: 'Meters',
    path: '/features/meters',
    key: 'meters',
    icon: MeterIcon,
  },
  permit_to_work: {
    name: 'Permit to Work',
    path: '/features/permit_to_work',
    key: 'permit_to_work',
    icon: PermitToWorkIcon,
  },
  parts: {
    name: 'Parts',
    path: '/features/parts',
    key: 'parts',
    icon: PartIcon,
  },
  attendances: {
    name: 'Attendance',
    path: '/features/attendances',
    key: 'attendances',
    icon: AttendanceIcon,
  },
  vendors: {
    name: 'Vendors',
    path: '/features/vendors',
    key: 'vendors',
    icon: VendorIcon,
  },
  billings: {
    name: 'Billing',
    path: '/features/billings',
    key: 'billings',
    icon: BillingIcon,
  },
  customers: {
    name: 'Customers',
    path: '/features/customers',
    key: 'customers',
    icon: CustomerIcon,
  },
  licenses: {
    name: 'Licenses',
    path: '/features/licenses',
    key: 'licenses',
    icon: LicenseIcon,
  },
  contracts: {
    name: 'Contracts',
    path: '/features/contracts',
    key: 'contracts',
    icon: ContractIcon,
  },
  budgets: {
    name: 'Budgets',
    path: '/features/budgets',
    key: 'budgets',
    icon: BudgetIcon,
  },
  payments: {
    name: 'Payments',
    path: '/features/payments',
    key: 'payments',
    icon: PaymentIcon,
  },
  schedules: {
    name: 'Schedules',
    path: '/features/schedules',
    key: 'schedules',
    icon: CalendarIcon,
  },
  leases: {
    name: 'Leases',
    path: '/features/leases',
    key: 'leases',
    icon: LeaseIcon,
  },
  /*******************************************
    Statistics Menu
  *******************************************/
  request_statistics: {
    name: 'Request Data',
    path: '/statistics/request_statistics',
    key: 'request_statistics',
    icon: RequestIcon,
  },
  checklist_statistics: {
    name: 'Checklist Data',
    path: '/statistics/checklist_statistics',
    key: 'checklist_statistics',
    icon: CheckListIcon,
  },
  expenditure_statistics: {
    name: 'Expenditure Data',
    path: '/statistics/expenditure_statistics',
    key: 'expenditure_statistics',
    icon: ExpendituresIcon,
  },
  responder_statistics: {
    name: 'Responder Data',
    path: '/statistics/responder_statistics',
    key: 'responder_statistics',
    icon: UserIcon,
  },
  asset_statistics: {
    name: 'Asset Data',
    path: '/statistics/asset_statistics',
    key: 'asset_statistics',
    icon: AssetIcon,
  },
  license_statistics: {
    name: 'License Data',
    path: '/statistics/license_statistics',
    key: 'license_statistics',
    icon: LicenseIcon,
  },
  contract_statistics: {
    name: 'Contract Data',
    path: '/statistics/contract_statistics',
    key: 'contract_statistics',
    icon: ContractIcon,
  },
  lease_statistics: {
    name: 'Lease Data',
    path: '/statistics/lease_statistics',
    key: 'lease_statistics',
    icon: LeaseIcon,
  },
  mqtt_data_statistics: {
    name: 'Sensors Data',
    path: '/statistics/mqtt_data_statistics',
    key: 'mqtt_data_statistics',
    icon: MqttNoColorIcon,
  },
  toilet_feedback_data: {
    name: 'Toilet Feedback Data',
    path: '/statistics/toilet_feedback_data',
    key: 'toilet_feedback_data',
    icon: RatingIcon,
  },
  meter_statistics: {
    name: 'Meter Data',
    path: '/statistics/meter_statistics',
    key: 'meter_statistics',
    icon: MeterIcon,
  },
  part_statistics: {
    name: 'Parts Data',
    path: '/statistics/part_statistics',
    key: 'part_statistics',
    icon: PartIcon,
  },
  permit_to_work_statistics: {
    name: 'Permit to Work Data',
    path: '/statistics/permit_to_work_statistics',
    key: 'permit_to_work_statistics',
    icon: PermitToWorkIcon,
  },
  sustainability_statistics: {
    name: 'Sustainability Data',
    path: '/statistics/sustainability_statistics',
    key: 'sustainability_statistics',
    icon: SustainabilityIcon,
  },
  people_counter_statistics: {
    name: 'People Counter Data',
    path: '/statistics/people_counter_statistics',
    key: 'people_counter_statistics',
    icon: PeopleCounterIcon,
  },
  attendance_statistics: {
    name: 'Attendance Data',
    path: '/statistics/attendance_statistics',
    key: 'attendance_statistics',
    icon: AttendanceIcon,
  },
  desk_data_statistics: {
    name: 'Desk Data',
    path: '/statistics/desk_data_statistics',
    key: 'desk_data_statistics',
    icon: HotDeskIcon,
  },
  facilities_statistics: {
    name: 'Facilities Data',
    path: '/statistics/facilities_statistics',
    key: 'facilities_statistics',
    icon: NewBookingIcon,
  },
  requestor_sentiment_statistics: {
    name: 'Requestor Data',
    path: '/statistics/requestor_sentiment_statistics',
    key: 'requestor_sentiment_statistics',
    icon: SmileIcon,
  },
  emails_sent_statistics: {
    name: 'Emails Sent',
    path: '/statistics/emails_sent_statistics',
    key: 'emails_sent_statistics',
    icon: EmailOutlinedIcon,
  },
  audit_logs_statistics: {
    name: 'Audit Log',
    path: '/statistics/audit_logs_statistics',
    key: 'audit_logs_statistics',
    icon: AuditLogIcon,
  },
  /*******************************************
    Settings Menu
  *******************************************/
  quick_setup: {
    name: 'General',
    path: '/settings/quick_setup',
    key: 'quick_setup',
    icon: QuickSetupIcon,
  },
  channel_setup: {
    name: 'Channels',
    path: '/settings/channel_setup',
    key: 'channel_setup',
    icon: IntergationIcon,
  },
  chatbot_setup: {
    name: 'Chatbot',
    path: '/settings/chatbot_setup',
    key: 'chatbot_setup',
    icon: ChatIcon,
  },
  feature_setup: {
    name: 'Features',
    path: '/settings/feature_setup',
    key: 'feature_setup',
    icon: RocketIcon,
  },
  visitor_settings: {
    name: 'Visitors',
    path: '/settings/visitor_settings/invitation',
    key: 'visitor_settings',
    icon: UserIcon,
  },
  location_tag_settings: {
    name: 'Location Tags',
    path: '/settings/location_tag_settings',
    key: 'location_tag_settings',
    icon: MapMarkIcon,
  },
  request_settings: {
    name: 'Requests',
    path: '/settings/request_settings',
    key: 'request_settings',
    icon: RequestIcon,
  },
  checklist_settings: {
    name: 'Checklists',
    path: '/settings/checklist_settings',
    key: 'checklist_settings',
    icon: CheckListIcon,
  },
  desk_settings: {
    name: 'Desks',
    path: '/settings/desk_settings',
    key: 'desk_settings',
    icon: HotDeskIcon,
  },
  facilities_settings: {
    name: 'Facilities',
    path: '/settings/facilities_settings',
    key: 'facilities_settings',
    icon: NewBookingIcon,
  },
  alert_settings: {
    name: 'Alert',
    path: '/settings/alert_settings',
    key: 'alert_settings',
    icon: BellIcon,
  },
  workflow_settings: {
    name: 'Workflows',
    path: '/features/workflow_settings',
    key: 'workflow_settings',
    icon: WorkflowIcon,
  },
  qr_code_settings: {
    name: 'QR Code Web Forms',
    path: '/features/qr_code_settings',
    key: 'qr_code_settings',
    icon: QrCodeIcon,
  },
  faq_settings: {
    name: 'FAQs',
    path: '/features/faq_settings',
    key: 'faq_settings',
    icon: FaqIcon,
  },
  schedule_of_rates: {
    name: 'Schedule of Rates',
    path: '/features/schedule_of_rates',
    key: 'schedule_of_rates',
    icon: SorIcon,
  },
  accounts_settings: {
    name: 'Accounts',
    path: '/settings/accounts_settings',
    key: 'accounts_settings',
    icon: UserIcon,
  },
  custom_pages: {
    name: 'Tenant Portal',
    path: '/features/custom_pages',
    key: 'custom_pages',
    icon: CustomPageIcon,
  },
  /*******************************************
    Integrations Menu
  *******************************************/
  workplace_integrations: {
    name: 'Workplace',
    path: '/integrations/workplace_integrations',
    key: 'workplace_integrations',
    icon: WorkplaceIcon,
  },
  facebook_integrations: {
    name: 'Facebook',
    path: '/integrations/facebook_integrations',
    key: 'facebook_integrations',
    icon: FacebookIcon,
  },
  telegram_integrations: {
    name: 'Telegram',
    path: '/integrations/telegram_integrations',
    key: 'telegram_integrations',
    icon: TelegramIcon,
  },
  slack_integrations: {
    name: 'Slack',
    path: '/integrations/slack_integrations',
    key: 'slack_integrations',
    icon: SlackIcon,
  },
  teams_integrations: {
    name: 'Teams',
    path: '/integrations/teams_integrations',
    key: 'teams_integrations',
    icon: TeamsIcon,
  },
  whatsapp_integrations: {
    name: 'WhatsApp',
    path: '/integrations/whatsapp_integrations',
    key: 'whatsapp_integrations',
    icon: WhatsAppIcon,
  },
  line_integrations: {
    name: 'Line',
    path: '/integrations/line_integrations',
    key: 'line_integrations',
    icon: LineIcon,
  },
  widget_integrations: {
    name: 'Webchat',
    path: '/integrations/widget_integrations',
    key: 'widget_integrations',
    icon: WidgetIcon,
  },
  google_integrations: {
    name: 'Google Chat',
    path: '/integrations/google_integrations',
    key: 'google_integrations',
    icon: GoogleIcon,
  },
  email_integrations: {
    name: 'Email',
    path: '/integrations/email_integrations',
    key: 'email_integrations',
    icon: EmailIcon,
  },
  mqtt_integrations: {
    name: 'Sensors',
    path: '/integrations/mqtt_integrations',
    key: 'mqtt_integrations',
    icon: MqttIcon,
  },
  bim_integrations: {
    name: 'BIM',
    path: '/integrations/bim_integrations',
    key: 'bim_integrations',
    icon: BimIcon,
  },
  api_integrations: {
    name: 'API',
    path: '/integrations/api_integrations',
    key: 'api_integrations',
    icon: ApiIcon,
  },
  portfolio_integrations: {
    name: 'Portfolio',
    path: '/integrations/portfolio_integrations',
    key: 'portfolio_integrations',
    icon: PortfolioTokenIcon,
  },
  payment_integrations: {
    name: 'Stripe',
    path: '/integrations/stripe_integrations',
    key: 'payment_integrations',
    icon: PaymentIcon,
  },
  zapier_integrations: {
    name: 'Zapier',
    path: '/integrations/zapier_integrations',
    key: 'zapier_integrations',
    icon: ZapierIcon,
  },
  jmm_integrations: {
    name: 'JTC Corporation',
    path: '/integrations/jmm_integrations',
    key: 'jmm_integrations',
    icon: JmmIcon,
  },
  exceltec_integrations: {
    name: 'Exceltec',
    path: '/integrations/exceltec_integrations',
    key: 'exceltec_integrations',
    icon: ExceltecIcon,
  },
  viber_integrations: {
    name: 'Viber',
    path: '/integrations/viber_integrations',
    key: 'viber_integrations',
    icon: ViberIcon,
  },
  jmm_integrations: {
    name: 'JTC Corporation',
    path: '/integrations/jmm_integrations',
    key: 'jmm_integrations',
    icon: JmmIcon,
  },
  procore_integrations: {
    name: 'Procore',
    path: '/integrations/procore_integrations',
    key: 'procore_integrations',
    icon: ProcoreIcon,
  },
  sso_integrations: {
    name: 'SSO',
    path: '/integrations/sso_integrations',
    key: 'sso_integrations',
    icon: SsoSignInIcon,
  },
  uhoo_integrations: {
    name: 'uHoo',
    path: '/integrations/uhoo_integrations',
    key: 'uhoo_integrations',
    icon: UhooIcon,
  },
  cpf_integrations: {
    name: 'CPF',
    path: '/integrations/cpf_integrations',
    key: 'cpf_integrations',
    icon: CpfIcon,
  },
  sap_integrations: {
    name: 'SAP',
    path: '/integrations/sap_integrations',
    key: 'sap_integrations',
    icon: SapIcon,
  },
  microsoft365_integrations: {
    name: 'Microsoft Dynamics 365',
    path: '/integrations/microsoft365_integrations',
    key: 'microsoft365_integrations',
    icon: MicrosoftIcon,
  },
};

const isEnableRequestsFeature = (feature_config) => {
  return (
    feature_config.feature_report_fault || feature_config.feature_custom_fault || feature_config.feature_chat_with_staff
  );
};

export const findNavItemsByRole = (
  role,
  is_custom_role = false,
  available_role_routes = [],
  agent_management,
  feature_config,
) => {
  let items = [];
  if (!agent_management) agent_management = default_agent_management;

  let requestFeatureEnabled = isEnableRequestsFeature(feature_config);

  if (is_custom_role) {
    let main_keys = ['features', 'settings', 'integrations', 'statistics', 'analytics'];
    items.push(NAV_ITEMS.dashboard);

    available_role_routes.map((route) => {
      if (main_keys.includes(route.key)) {
        main_keys = main_keys.filter((key) => key !== route.key);
        let _nav = NAV_ITEMS[route.key];
        _nav.path = route.path;
        items.push(_nav);
      }
    });
    items.push(NAV_ITEMS.sign_out);

    return items;
  } else {
    const NAV_ITEMS_FEATURES = requestFeatureEnabled
      ? NAV_ITEMS.features
      : {
          ...NAV_ITEMS.features,
          path: '/features/checklists',
        };

    switch (role) {
      case 'manager':
        items = [
          NAV_ITEMS.dashboard,
          NAV_ITEMS_FEATURES,
          NAV_ITEMS.statistics,
          ...(agent_management.enable_analytic ? [NAV_ITEMS.analytics] : []),
          NAV_ITEMS.settings,
          NAV_ITEMS.integrations,
          NAV_ITEMS.sign_out,
        ];
        break;
      case 'admin':
        items = [
          NAV_ITEMS.dashboard,
          NAV_ITEMS_FEATURES,
          NAV_ITEMS.statistics,
          ...(agent_management.enable_analytic ? [NAV_ITEMS.analytics] : []),
          NAV_ITEMS.settings,
          NAV_ITEMS.integrations,
          NAV_ITEMS.sign_out,
        ];
        break;
    }

    return items;
  }
};

export const findSubNavItemsByRole = (
  role,
  feature_config = {
    feature_survey: true,
    feature_booking: true,
    feature_chat_with_staff: true,
    feature_report_fault: true,
    feature_desk_booking: true,
    feature_schedule_of_rate: true,
    feature_procurement: true,
    feature_checklist: true,
    feature_visitor: true,
    feaure_expenditure: true,
    feature_faq: true,
    feature_broadcast: true,
    feature_asset_tracking: true,
    feature_custom_fault: true,
    feature_meter: true,
    feature_attendance: true,
    feature_qr_code_web_form: true,
    feature_vendor: true,
    feature_billing: true,
    feature_license_management: true,
    feature_budget: true,
    feature_payment: true,
    feature_customer: true,
    feature_schedules: true,
    feature_tenant_portal: true,
    feature_permit_to_work: true,
    feature_part: true,
    feature_delivery: true,
    feature_lease_management: true,
    feature_contract_management: true,
  },
  enable_agent_token = false,
  custom_permissions,
  agent_management,
) => {
  if (!custom_permissions) custom_permissions = default_custom_permissions;
  if (!agent_management) agent_management = default_agent_management;
  let items = [];

  let requestFeatureEnabled = isEnableRequestsFeature(feature_config);

  switch (role) {
    case 'manager':
      items = {
        features: [
          {
            key: 'core_maintenance',
            name: 'Core Maintenance',
            items: [
              custom_permissions.feature_request && requestFeatureEnabled && MANAGER_SUB_NAV_ITEMS.requests,
              feature_config.feature_checklist &&
                custom_permissions.feature_checklist &&
                MANAGER_SUB_NAV_ITEMS.checklists,
              feature_config.feature_asset_tracking && custom_permissions.feature_asset && MANAGER_SUB_NAV_ITEMS.assets,
              feature_config.feature_qr_code_web_form &&
                custom_permissions.feature_qrcode_webform &&
                MANAGER_SUB_NAV_ITEMS.qr_code_settings,
              feature_config.feature_meter && custom_permissions.feature_meter && MANAGER_SUB_NAV_ITEMS.meters,
              feature_config.feature_part && custom_permissions.feature_part && MANAGER_SUB_NAV_ITEMS.parts,
              feature_config.feature_permit_to_work &&
                custom_permissions.feature_permit_to_work &&
                MANAGER_SUB_NAV_ITEMS.permit_to_work,
              feature_config.feature_license_management &&
                custom_permissions.feature_license &&
                MANAGER_SUB_NAV_ITEMS.licenses,
              feature_config.feature_contract_management &&
                custom_permissions.feature_contract_management &&
                MANAGER_SUB_NAV_ITEMS.contracts,
              feature_config.feature_lease_management &&
                custom_permissions.feature_lease &&
                MANAGER_SUB_NAV_ITEMS.leases,
              custom_permissions.feature_workflow && MANAGER_SUB_NAV_ITEMS.workflow_settings,
              feature_config.feature_attendance &&
                custom_permissions.feature_attendance &&
                MANAGER_SUB_NAV_ITEMS.attendances,
              feature_config.feature_schedules &&
                custom_permissions.feature_schedule &&
                MANAGER_SUB_NAV_ITEMS.schedules,
            ],
          },
          {
            key: 'financial',
            name: 'Financial',
            items: [
              feature_config.feature_vendor && custom_permissions.feature_vendor && MANAGER_SUB_NAV_ITEMS.vendors,
              feature_config.feature_schedule_of_rate &&
                custom_permissions.feature_sor &&
                MANAGER_SUB_NAV_ITEMS.schedule_of_rates,
              feature_config.feature_procurement &&
                custom_permissions.feature_procurement &&
                MANAGER_SUB_NAV_ITEMS.procurement,
              feature_config.feaure_expenditure &&
                custom_permissions.feature_expenditure &&
                MANAGER_SUB_NAV_ITEMS.expenditures,
              feature_config.feature_budget && custom_permissions.feature_budget && MANAGER_SUB_NAV_ITEMS.budgets,
              feature_config.feature_billing && custom_permissions.feature_billing && MANAGER_SUB_NAV_ITEMS.billings,
              feature_config.feature_payment && custom_permissions.feature_payment && MANAGER_SUB_NAV_ITEMS.payments,
              feature_config.feature_customer && custom_permissions.feature_customer && MANAGER_SUB_NAV_ITEMS.customers,
            ],
          },
          {
            key: 'tenant_experience',
            name: 'Tenant Experience',
            items: [
              feature_config.feature_booking &&
                custom_permissions.feature_facility &&
                MANAGER_SUB_NAV_ITEMS.facility_bookings,
              feature_config.feature_desk_booking &&
                custom_permissions.feature_desk &&
                MANAGER_SUB_NAV_ITEMS.desk_bookings,
              feature_config.feature_delivery &&
                custom_permissions.feature_delivery &&
                MANAGER_SUB_NAV_ITEMS.deliveries,
              agent_management.enable_visitor &&
                feature_config.feature_visitor &&
                custom_permissions.feature_visitor &&
                MANAGER_SUB_NAV_ITEMS.visitors,
              feature_config.feature_faq && custom_permissions.feature_faq && MANAGER_SUB_NAV_ITEMS.faq_settings,
              feature_config.feature_survey && custom_permissions.feature_survey && MANAGER_SUB_NAV_ITEMS.surveys,
              feature_config.feature_broadcast &&
                custom_permissions.feature_broadcast &&
                MANAGER_SUB_NAV_ITEMS.broadcasts,
              custom_permissions.feature_chat && MANAGER_SUB_NAV_ITEMS.chats,
            ],
          },
        ],

        statistics: [
          custom_permissions.statistic_request_data && MANAGER_SUB_NAV_ITEMS.request_statistics,
          feature_config.feature_checklist &&
            custom_permissions.statistic_checklist_data &&
            MANAGER_SUB_NAV_ITEMS.checklist_statistics,
          feature_config.feaure_expenditure &&
            custom_permissions.statistic_expenditure &&
            MANAGER_SUB_NAV_ITEMS.expenditure_statistics,
          custom_permissions.statistic_responder && MANAGER_SUB_NAV_ITEMS.responder_statistics,
          feature_config.feature_asset_tracking &&
            custom_permissions.statistic_asset_data &&
            MANAGER_SUB_NAV_ITEMS.asset_statistics,
          feature_config.feature_license_management &&
            custom_permissions.statistic_license_data &&
            MANAGER_SUB_NAV_ITEMS.license_statistics,
          feature_config.feature_contract_management &&
            custom_permissions.statistic_contract_data &&
            MANAGER_SUB_NAV_ITEMS.contract_statistics,
          feature_config.feature_lease_management &&
            custom_permissions.statistic_lease_data &&
            MANAGER_SUB_NAV_ITEMS.lease_statistics,
          agent_management.enable_sensors &&
            custom_permissions.statistic_mqtt_data &&
            MANAGER_SUB_NAV_ITEMS.mqtt_data_statistics,
          feature_config.feature_part && custom_permissions.feature_part && MANAGER_SUB_NAV_ITEMS.part_statistics,
          feature_config.feature_meter &&
            custom_permissions.statistic_meter_data &&
            MANAGER_SUB_NAV_ITEMS.meter_statistics,
          feature_config.feature_permit_to_work &&
            custom_permissions.statistic_permit_to_work_data &&
            MANAGER_SUB_NAV_ITEMS.permit_to_work_statistics,
          feature_config.feature_meter &&
            custom_permissions.statistic_sustainability_data &&
            MANAGER_SUB_NAV_ITEMS.sustainability_statistics,
          feature_config.feature_attendance &&
            custom_permissions.statistic_attendance_data &&
            MANAGER_SUB_NAV_ITEMS.attendance_statistics,
          feature_config.feature_desk_booking &&
            custom_permissions.statistic_desk_data &&
            MANAGER_SUB_NAV_ITEMS.desk_data_statistics,
          feature_config.feature_booking &&
            custom_permissions.statistic_facility_data &&
            MANAGER_SUB_NAV_ITEMS.facilities_statistics,
          custom_permissions.statistic_toilet_feedback_data && MANAGER_SUB_NAV_ITEMS.toilet_feedback_data,
          custom_permissions.statistic_people_counter_data && MANAGER_SUB_NAV_ITEMS.people_counter_statistics,
          custom_permissions.statistic_requestor_sentiment && MANAGER_SUB_NAV_ITEMS.requestor_sentiment_statistics,
          custom_permissions.statistic_emails_sent && MANAGER_SUB_NAV_ITEMS.emails_sent_statistics,
        ],
        settings: [
          custom_permissions.setting_general && MANAGER_SUB_NAV_ITEMS.quick_setup,
          custom_permissions.setting_channel && MANAGER_SUB_NAV_ITEMS.channel_setup,
          custom_permissions.setting_chatbot && MANAGER_SUB_NAV_ITEMS.chatbot_setup,
          custom_permissions.setting_feature && MANAGER_SUB_NAV_ITEMS.feature_setup,
          MANAGER_SUB_NAV_ITEMS.location_tag_settings,
          custom_permissions.setting_request && MANAGER_SUB_NAV_ITEMS.request_settings,
          feature_config.feature_checklist &&
            custom_permissions.setting_checklist &&
            MANAGER_SUB_NAV_ITEMS.checklist_settings,
          feature_config.feature_booking &&
            custom_permissions.setting_facility &&
            MANAGER_SUB_NAV_ITEMS.facilities_settings,
          feature_config.feature_desk_booking && custom_permissions.setting_desk && MANAGER_SUB_NAV_ITEMS.desk_settings,
          agent_management.enable_visitor &&
            feature_config.feature_visitor &&
            custom_permissions.setting_visitor &&
            MANAGER_SUB_NAV_ITEMS.visitor_settings,
        ],
        integrations: [
          custom_permissions.integration_telegram && MANAGER_SUB_NAV_ITEMS.telegram_integrations,
          custom_permissions.integration_facebook && MANAGER_SUB_NAV_ITEMS.facebook_integrations,
          custom_permissions.integration_workplace && MANAGER_SUB_NAV_ITEMS.workplace_integrations,
          custom_permissions.integration_slack && MANAGER_SUB_NAV_ITEMS.slack_integrations,
          custom_permissions.integration_team && MANAGER_SUB_NAV_ITEMS.teams_integrations,
          agent_management.enable_whatsapp &&
            custom_permissions.integration_whatsapp &&
            MANAGER_SUB_NAV_ITEMS.whatsapp_integrations,
          custom_permissions.integration_line && MANAGER_SUB_NAV_ITEMS.line_integrations,
          custom_permissions.integration_viber && MANAGER_SUB_NAV_ITEMS.viber_integrations,
          custom_permissions.integration_webchat && MANAGER_SUB_NAV_ITEMS.widget_integrations,
          custom_permissions.integration_google_chat && MANAGER_SUB_NAV_ITEMS.google_integrations,
          agent_management.enable_jmm && custom_permissions.integration_jmm && MANAGER_SUB_NAV_ITEMS.jmm_integrations,
          agent_management.enable_exceltec &&
            custom_permissions.enable_exceltec &&
            MANAGER_SUB_NAV_ITEMS.exceltec_integrations,
          agent_management.enable_sensors &&
            custom_permissions.integration_mqtt &&
            MANAGER_SUB_NAV_ITEMS.mqtt_integrations,
          agent_management.enable_bim && custom_permissions.integration_bim && MANAGER_SUB_NAV_ITEMS.bim_integrations,
          custom_permissions.integration_email && MANAGER_SUB_NAV_ITEMS.email_integrations,
          agent_management.enable_procore &&
            custom_permissions.integration_procore &&
            MANAGER_SUB_NAV_ITEMS.procore_integrations,
          agent_management.enable_uhoo &&
            custom_permissions.integration_uhoo &&
            MANAGER_SUB_NAV_ITEMS.uhoo_integrations,
          agent_management.enable_workpal &&
            custom_permissions.integration_workpal &&
            MANAGER_SUB_NAV_ITEMS.cpf_integrations,
          agent_management.enable_sap_integrate &&
            custom_permissions.integration_sap &&
            MANAGER_SUB_NAV_ITEMS.sap_integrations,
          agent_management.enable_agent_dynamics365 && MANAGER_SUB_NAV_ITEMS.microsoft365_integrations,
        ],
      };
      break;
    case 'admin':
      items = {
        features: [
          {
            key: 'core_maintenance',
            name: 'Core Maintenance',
            items: [
              requestFeatureEnabled && MANAGER_SUB_NAV_ITEMS.requests,
              feature_config.feature_checklist && MANAGER_SUB_NAV_ITEMS.checklists,
              feature_config.feature_asset_tracking && MANAGER_SUB_NAV_ITEMS.assets,
              feature_config.feature_qr_code_web_form && MANAGER_SUB_NAV_ITEMS.qr_code_settings,
              feature_config.feature_meter && MANAGER_SUB_NAV_ITEMS.meters,
              feature_config.feature_part && MANAGER_SUB_NAV_ITEMS.parts,
              feature_config.feature_permit_to_work && MANAGER_SUB_NAV_ITEMS.permit_to_work,
              feature_config.feature_license_management && MANAGER_SUB_NAV_ITEMS.licenses,
              feature_config.feature_contract_management && MANAGER_SUB_NAV_ITEMS.contracts,
              MANAGER_SUB_NAV_ITEMS.workflow_settings,
              feature_config.feature_attendance && MANAGER_SUB_NAV_ITEMS.attendances,
              feature_config.feature_schedules &&
                custom_permissions.feature_schedule &&
                MANAGER_SUB_NAV_ITEMS.schedules,
              feature_config.feature_lease_management && MANAGER_SUB_NAV_ITEMS.leases,
            ],
          },
          {
            key: 'financial',
            name: 'Financial',
            items: [
              feature_config.feature_vendor && MANAGER_SUB_NAV_ITEMS.vendors,
              feature_config.feature_schedule_of_rate && MANAGER_SUB_NAV_ITEMS.schedule_of_rates,

              feature_config.feature_procurement && MANAGER_SUB_NAV_ITEMS.procurement,
              feature_config.feaure_expenditure && MANAGER_SUB_NAV_ITEMS.expenditures,
              feature_config.feature_budget && MANAGER_SUB_NAV_ITEMS.budgets,
              feature_config.feature_billing && MANAGER_SUB_NAV_ITEMS.billings,
              feature_config.feature_payment && MANAGER_SUB_NAV_ITEMS.payments,
              feature_config.feature_customer && MANAGER_SUB_NAV_ITEMS.customers,
            ],
          },
          {
            key: 'tenant_experience',
            name: 'Tenant Experience',
            items: [
              agent_management.enable_custom_sign_in_page &&
                feature_config.feature_tenant_portal &&
                MANAGER_SUB_NAV_ITEMS.custom_pages,
              feature_config.feature_booking && MANAGER_SUB_NAV_ITEMS.facility_bookings,
              feature_config.feature_desk_booking && MANAGER_SUB_NAV_ITEMS.desk_bookings,
              agent_management.enable_delivery && feature_config.feature_delivery && MANAGER_SUB_NAV_ITEMS.deliveries,
              agent_management.enable_visitor && feature_config.feature_visitor && MANAGER_SUB_NAV_ITEMS.visitors,
              feature_config.feature_faq && MANAGER_SUB_NAV_ITEMS.faq_settings,
              feature_config.feature_survey && MANAGER_SUB_NAV_ITEMS.surveys,
              feature_config.feature_broadcast && MANAGER_SUB_NAV_ITEMS.broadcasts,
              MANAGER_SUB_NAV_ITEMS.chats,
            ],
          },
        ],

        statistics: [
          MANAGER_SUB_NAV_ITEMS.request_statistics,
          feature_config.feature_checklist && MANAGER_SUB_NAV_ITEMS.checklist_statistics,
          feature_config.feaure_expenditure && MANAGER_SUB_NAV_ITEMS.expenditure_statistics,
          MANAGER_SUB_NAV_ITEMS.responder_statistics,
          feature_config.feature_asset_tracking && MANAGER_SUB_NAV_ITEMS.asset_statistics,
          feature_config.feature_license_management && MANAGER_SUB_NAV_ITEMS.license_statistics,
          feature_config.feature_contract_management && MANAGER_SUB_NAV_ITEMS.contract_statistics,
          feature_config.feature_lease_management && MANAGER_SUB_NAV_ITEMS.lease_statistics,
          agent_management.enable_sensors && MANAGER_SUB_NAV_ITEMS.mqtt_data_statistics,
          feature_config.feature_meter && MANAGER_SUB_NAV_ITEMS.meter_statistics,
          feature_config.feature_part && MANAGER_SUB_NAV_ITEMS.part_statistics,
          feature_config.feature_permit_to_work && MANAGER_SUB_NAV_ITEMS.permit_to_work_statistics,
          MANAGER_SUB_NAV_ITEMS.sustainability_statistics,
          feature_config.feature_attendance && MANAGER_SUB_NAV_ITEMS.attendance_statistics,
          feature_config.feature_desk_booking && MANAGER_SUB_NAV_ITEMS.desk_data_statistics,
          feature_config.feature_booking && MANAGER_SUB_NAV_ITEMS.facilities_statistics,
          MANAGER_SUB_NAV_ITEMS.toilet_feedback_data,
          MANAGER_SUB_NAV_ITEMS.people_counter_statistics,
          MANAGER_SUB_NAV_ITEMS.requestor_sentiment_statistics,
          MANAGER_SUB_NAV_ITEMS.emails_sent_statistics,
          agent_management.enable_audit_log && MANAGER_SUB_NAV_ITEMS.audit_logs_statistics,
        ],
        settings: [
          MANAGER_SUB_NAV_ITEMS.quick_setup,
          MANAGER_SUB_NAV_ITEMS.accounts_settings,
          MANAGER_SUB_NAV_ITEMS.channel_setup,
          MANAGER_SUB_NAV_ITEMS.chatbot_setup,
          MANAGER_SUB_NAV_ITEMS.feature_setup,
          MANAGER_SUB_NAV_ITEMS.location_tag_settings,
          MANAGER_SUB_NAV_ITEMS.request_settings,
          feature_config.feature_checklist && MANAGER_SUB_NAV_ITEMS.checklist_settings,
          feature_config.feature_booking && MANAGER_SUB_NAV_ITEMS.facilities_settings,
          feature_config.feature_desk_booking && MANAGER_SUB_NAV_ITEMS.desk_settings,
          agent_management.enable_visitor && feature_config.feature_visitor && MANAGER_SUB_NAV_ITEMS.visitor_settings,
        ],
        integrations: [
          MANAGER_SUB_NAV_ITEMS.telegram_integrations,
          MANAGER_SUB_NAV_ITEMS.facebook_integrations,
          MANAGER_SUB_NAV_ITEMS.workplace_integrations,
          MANAGER_SUB_NAV_ITEMS.slack_integrations,
          MANAGER_SUB_NAV_ITEMS.teams_integrations,
          agent_management.enable_whatsapp && MANAGER_SUB_NAV_ITEMS.whatsapp_integrations,
          MANAGER_SUB_NAV_ITEMS.line_integrations,
          MANAGER_SUB_NAV_ITEMS.viber_integrations,
          MANAGER_SUB_NAV_ITEMS.widget_integrations,
          MANAGER_SUB_NAV_ITEMS.google_integrations,
          MANAGER_SUB_NAV_ITEMS.email_integrations,
          agent_management.enable_sensors && MANAGER_SUB_NAV_ITEMS.mqtt_integrations,
          agent_management.enable_bim && custom_permissions.integration_bim && MANAGER_SUB_NAV_ITEMS.bim_integrations,
          agent_management.enable_api_token && MANAGER_SUB_NAV_ITEMS.api_integrations,
          MANAGER_SUB_NAV_ITEMS.portfolio_integrations,
          agent_management.enable_payment && MANAGER_SUB_NAV_ITEMS.payment_integrations,
          agent_management.enable_zapier && MANAGER_SUB_NAV_ITEMS.zapier_integrations,
          agent_management.enable_jmm && MANAGER_SUB_NAV_ITEMS.jmm_integrations,
          agent_management.enable_exceltec && MANAGER_SUB_NAV_ITEMS.exceltec_integrations,
          agent_management.enable_procore && MANAGER_SUB_NAV_ITEMS.procore_integrations,
          agent_management.enable_sso && MANAGER_SUB_NAV_ITEMS.sso_integrations,
          agent_management.enable_uhoo && MANAGER_SUB_NAV_ITEMS.uhoo_integrations,
          agent_management.enable_workpal && MANAGER_SUB_NAV_ITEMS.cpf_integrations,
          agent_management.enable_sap_integrate && MANAGER_SUB_NAV_ITEMS.sap_integrations,
          agent_management.enable_agent_dynamics365 && MANAGER_SUB_NAV_ITEMS.microsoft365_integrations,
        ],
      };
      break;
  }

  return items;
};

export const beta_menu = [routes.analytics];
