const TITLE_STRING = ' | FacilityBot Portal';
const PARENT_KEY = {
  features: {
    key: 'features',
    label: 'Features',
  },
  statistics: {
    key: 'statistics',
    label: 'Statistics',
  },
  settings: {
    key: 'settings',
    label: 'Settings',
  },
  integrations: {
    key: 'integrations',
    label: 'Integrations',
  },
  dashboard: {
    key: 'dashboard',
    label: 'Home',
  },
  subscription_plans: {
    key: 'subscription_plans',
    label: 'Subscription Plans',
  },
  analytics: {
    key: 'analytics',
    label: 'Analytics',
  },
};
export const routes = {
  /*******************************************
    Features Routes
  *******************************************/
  accounts: {
    key: 'accounts',
    sub_key: null,
    path: '/accounts',
    header: 'My Account',
    main_header: 'Personal',
    title: 'My Account' + TITLE_STRING,
  },
  requests: {
    key: PARENT_KEY.features.key,
    sub_key: 'requests',
    header: 'Requests',
    main_header: PARENT_KEY.features.label,
    path: '/features/requests',
    title: 'Requests' + TITLE_STRING,
  },
  checklists: {
    key: PARENT_KEY.features.key,
    sub_key: 'checklists',
    header: 'Checklists',
    main_header: PARENT_KEY.features.label,
    path: '/features/checklists',
    title: 'Checklists' + TITLE_STRING,
  },
  assets: {
    key: PARENT_KEY.features.key,
    sub_key: 'assets',
    header: 'Assets',
    main_header: PARENT_KEY.features.label,
    path: '/features/assets',
    title: 'Assets' + TITLE_STRING,
  },
  expenditures: {
    key: PARENT_KEY.features.key,
    sub_key: 'expenditures',
    header: 'Expenditures',
    main_header: PARENT_KEY.features.label,
    path: '/features/expenditures',
    title: 'Expenditures' + TITLE_STRING,
  },
  budgets: {
    key: PARENT_KEY.features.key,
    sub_key: 'budgets',
    header: 'Budgets',
    main_header: PARENT_KEY.features.label,
    path: '/features/budgets',
    title: 'Budgets' + TITLE_STRING,
  },
  facility_bookings: {
    key: PARENT_KEY.features.key,
    sub_key: 'facility_bookings',
    header: 'Facilities',
    main_header: PARENT_KEY.features.label,
    path: '/features/facility_bookings',
    title: 'Facilities' + TITLE_STRING,
  },
  conversations: {
    key: PARENT_KEY.features.key,
    sub_key: 'conversations',
    header: 'Chat',
    main_header: PARENT_KEY.features.label,
    path: '/features/conversations',
    title: 'Chats' + TITLE_STRING,
  },
  surveys: {
    key: PARENT_KEY.features.key,
    sub_key: 'surveys',
    header: 'Surveys',
    main_header: PARENT_KEY.features.label,
    path: '/features/surveys',
    title: 'Surveys' + TITLE_STRING,
  },
  broadcasts: {
    key: PARENT_KEY.features.key,
    sub_key: 'broadcasts',
    header: 'Broadcasts',
    main_header: PARENT_KEY.features.label,
    path: '/features/broadcasts',
    title: 'Broadcasts' + TITLE_STRING,
  },
  visitors: {
    key: PARENT_KEY.features.key,
    sub_key: 'visitors',
    header: 'Visitors',
    main_header: PARENT_KEY.features.label,
    path: '/features/visitors',
    title: 'Visitors' + TITLE_STRING,
  },
  desk_bookings: {
    key: PARENT_KEY.features.key,
    sub_key: 'desk_bookings',
    header: 'Desks',
    main_header: PARENT_KEY.features.label,
    path: '/features/desk_bookings',
    title: 'Desks' + TITLE_STRING,
  },
  deliveries: {
    key: PARENT_KEY.features.key,
    sub_key: 'deliveries',
    header: 'Deliveries',
    main_header: PARENT_KEY.features.label,
    path: '/features/deliveries',
    title: 'Deliveries' + TITLE_STRING,
  },
  faqs: {
    key: PARENT_KEY.features.key,
    sub_key: 'faq_settings',
    header: 'FAQs',
    main_header: PARENT_KEY.features.label,
    path: '/features/faq_settings',
    title: 'FAQs' + TITLE_STRING,
  },
  workflows: {
    key: PARENT_KEY.features.key,
    sub_key: 'workflow_settings',
    header: 'Workflows',
    main_header: PARENT_KEY.features.label,
    path: '/features/workflow_settings',
    title: 'Workflow' + TITLE_STRING,
  },
  qr_codes: {
    key: PARENT_KEY.features.key,
    sub_key: 'qr_code_settings',
    header: 'QR Code Web Forms',
    main_header: PARENT_KEY.features.label,
    path: '/features/qr_code_settings',
    title: 'QR Code Web Forms' + TITLE_STRING,
  },

  schedule_of_rates: {
    key: PARENT_KEY.features.key,
    sub_key: 'schedule_of_rates',
    header: 'Schedule of Rates',
    main_header: PARENT_KEY.features.label,
    path: '/features/schedule_of_rates',
    title: 'Schedule of Rates' + TITLE_STRING,
  },
  procurement: {
    key: PARENT_KEY.features.key,
    sub_key: 'procurement',
    header: 'Procurement',
    main_header: PARENT_KEY.features.label,
    path: '/features/procurement',
    title: 'Procurement' + TITLE_STRING,
  },
  vendors: {
    key: PARENT_KEY.features.key,
    sub_key: 'vendors',
    header: 'Vendors',
    main_header: PARENT_KEY.features.label,
    path: '/features/vendors',
    title: 'Vendors' + TITLE_STRING,
  },
  meters: {
    key: PARENT_KEY.features.key,
    sub_key: 'meters',
    header: 'Meters',
    main_header: PARENT_KEY.features.label,
    path: '/features/meters',
    title: 'Meters' + TITLE_STRING,
  },
  parts: {
    key: PARENT_KEY.features.key,
    sub_key: 'parts',
    header: 'Parts',
    main_header: PARENT_KEY.features.label,
    path: '/features/parts',
    title: 'Parts' + TITLE_STRING,
  },
  permit_to_work: {
    key: PARENT_KEY.features.key,
    sub_key: 'permit_to_work',
    header: 'Permit to Work',
    main_header: PARENT_KEY.features.label,
    path: '/features/permit_to_work',
    title: 'Permit to Work' + TITLE_STRING,
  },
  attendances: {
    key: PARENT_KEY.features.key,
    sub_key: 'attendances',
    header: 'Attendance',
    main_header: PARENT_KEY.features.label,
    path: '/features/attendances',
    title: 'Attendances' + TITLE_STRING,
  },
  invoices: {
    key: PARENT_KEY.features.key,
    sub_key: 'invoices',
    header: 'Vendors',
    main_header: PARENT_KEY.features.label,
    path: '/features/invoices',
    title: 'Vendors' + TITLE_STRING,
  },
  billings: {
    key: PARENT_KEY.features.key,
    sub_key: 'billings',
    header: 'Billing',
    main_header: PARENT_KEY.features.label,
    path: '/features/billings',
    title: 'Billing' + TITLE_STRING,
  },
  customers: {
    key: PARENT_KEY.features.key,
    sub_key: 'customers',
    header: 'Customers',
    main_header: PARENT_KEY.features.label,
    path: '/features/customers',
    title: 'Customers' + TITLE_STRING,
  },
  licenses: {
    key: PARENT_KEY.features.key,
    sub_key: 'licenses',
    header: 'Licenses',
    main_header: PARENT_KEY.features.label,
    path: '/features/licenses',
    title: 'Licenses' + TITLE_STRING,
  },
  payments: {
    key: PARENT_KEY.features.key,
    sub_key: 'payments',
    header: 'Payments',
    main_header: PARENT_KEY.features.label,
    path: '/features/payments',
    title: 'Payments' + TITLE_STRING,
  },
  contracts: {
    key: PARENT_KEY.features.key,
    sub_key: 'contracts',
    header: 'Contracts',
    main_header: PARENT_KEY.features.label,
    path: '/features/contracts',
    title: 'Contracts' + TITLE_STRING,
  },
  leases: {
    key: PARENT_KEY.features.key,
    sub_key: 'leases',
    header: 'Leases',
    main_header: PARENT_KEY.features.label,
    path: '/features/leases',
    title: 'Leases' + TITLE_STRING,
  },
  schedules: {
    key: PARENT_KEY.features.key,
    sub_key: 'schedules',
    header: 'Schedules',
    main_header: PARENT_KEY.features.label,
    path: '/features/schedules',
    title: 'Schedules' + TITLE_STRING,
  },
  custom_pages: {
    key: PARENT_KEY.features.key,
    sub_key: 'custom_pages',
    header: 'Tenant Portal',
    main_header: PARENT_KEY.features.label,
    path: '/features/custom_pages',
    title: 'Tenant Portal' + TITLE_STRING,
  },
  /*******************************************
    Statistics Routes
  *******************************************/
  request_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'request_statistics',
    header: 'Request Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/request_statistics',
    title: 'Request Data' + TITLE_STRING,
  },
  checklist_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'checklist_statistics',
    header: 'Checklist Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/checklist_statistics',
    title: 'Checklist Data' + TITLE_STRING,
  },
  expenditure_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'expenditure_statistics',
    header: 'Expenditure Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/expenditure_statistics',
    title: 'Expenditure Statistics' + TITLE_STRING,
  },
  responder_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'responder_statistics',
    header: 'Responder Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/responder_statistics',
    title: 'Responder Statistics' + TITLE_STRING,
  },
  asset_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'asset_statistics',
    header: 'Asset Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/asset_statistics',
    title: 'Asset Data' + TITLE_STRING,
  },
  license_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'license_statistics',
    header: 'License Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/license_statistics',
    title: 'License Data' + TITLE_STRING,
  },
  contract_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'contract_statistics',
    header: 'Contract Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/contract_statistics',
    title: 'Contract Data' + TITLE_STRING,
  },
  lease_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'lease_statistics',
    header: 'Lease Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/lease_statistics',
    title: 'Lease Data' + TITLE_STRING,
  },
  mqtt_data_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'mqtt_data_statistics',
    header: 'Sensors Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/mqtt_data_statistics',
    title: 'Sensors Data' + TITLE_STRING,
  },
  toilet_feedback_data: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'toilet_feedback_data',
    header: 'Toilet Feedback Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/toilet_feedback_data',
    title: 'Toilet Feedback Data' + TITLE_STRING,
  },
  meter_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'meter_statistics',
    header: 'Meter Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/meter_statistics',
    title: 'Meter Data' + TITLE_STRING,
  },
  part_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'part_statistics',
    header: 'Parts Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/part_statistics',
    title: 'Parts Data' + TITLE_STRING,
  },
  permit_to_work_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'permit_to_work_statistics',
    header: 'Permit to Work Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/permit_to_work_statistics',
    title: 'Permit to Work Data' + TITLE_STRING,
  },
  sustainability_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'sustainability_statistics',
    header: 'Sustainability Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/sustainability_statistics',
    title: 'Sustainability Data' + TITLE_STRING,
  },
  attendance_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'attendance_statistics',
    header: 'Attendance Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/attendance_statistics',
    title: 'Attendance Data' + TITLE_STRING,
  },
  desk_data_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'desk_data_statistics',
    header: 'Desk Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/desk_data_statistics',
    title: 'Desk Data' + TITLE_STRING,
  },
  facilities_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'facilities_statistics',
    header: 'Facilities Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/facilities_statistics',
    title: 'Facilities Data' + TITLE_STRING,
  },
  requestor_sentiment_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'requestor_sentiment_statistics',
    header: 'Requestor Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/requestor_sentiment_statistics',
    title: 'Requestor Data' + TITLE_STRING,
  },
  emails_sent_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'emails_sent_statistics',
    header: 'Emails Sent',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/emails_sent_statistics',
    title: 'Emails Sent' + TITLE_STRING,
  },
  audit_logs_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'audit_logs_statistics',
    header: 'Audit Log',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/audit_logs_statistics',
    title: 'Audit Log' + TITLE_STRING,
  },
  people_counter_statistics: {
    key: PARENT_KEY.statistics.key,
    sub_key: 'people_counter_statistics',
    header: 'People Counter Data',
    main_header: PARENT_KEY.statistics.label,
    path: '/statistics/people_counter_statistics',
    title: 'People Counter Data' + TITLE_STRING,
  },
  /*******************************************
    Settings Routes
  *******************************************/
  quick_setup: {
    key: PARENT_KEY.settings.key,
    sub_key: 'quick_setup',
    header: 'General',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/quick_setup',
    title: 'General' + TITLE_STRING,
  },
  channel_setup: {
    key: PARENT_KEY.settings.key,
    sub_key: 'channel_setup',
    header: 'Channels',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/channel_setup',
    title: 'Channels' + TITLE_STRING,
  },
  chatbot_setup: {
    key: PARENT_KEY.settings.key,
    sub_key: 'chatbot_setup',
    header: 'Chatbot',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/chatbot_setup',
    title: 'Chatbot' + TITLE_STRING,
  },
  feature_setup: {
    key: PARENT_KEY.settings.key,
    sub_key: 'feature_setup',
    header: 'Features',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/feature_setup',
    title: 'Features' + TITLE_STRING,
  },
  visitor_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'visitor_settings',
    header: 'Visitors',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/visitor_settings/invitation',
    title: 'Visitors' + TITLE_STRING,
  },
  location_tag_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'location_tag_settings',
    header: 'Location Tags',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/location_tag_settings',
    title: 'Location Tags' + TITLE_STRING,
  },
  request_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'request_settings',
    header: 'Requests',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/request_settings',
    title: 'Request' + TITLE_STRING,
  },
  checklist_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'checklist_settings',
    header: 'Checklists',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/checklist_settings',
    title: 'Checklists' + TITLE_STRING,
  },
  facilities_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'facilities_settings',
    header: 'Facilities',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/facilities_settings',
    title: 'Facilities' + TITLE_STRING,
  },
  desk_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'desk_settings',
    header: 'Desks',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/desk_settings',
    title: 'Desks' + TITLE_STRING,
  },
  alert_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'alert_settings',
    header: 'Alert',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/alert_settings',
    title: 'Alert' + TITLE_STRING,
  },

  accounts_settings: {
    key: PARENT_KEY.settings.key,
    sub_key: 'accounts_settings',
    header: 'Accounts',
    main_header: PARENT_KEY.settings.label,
    path: '/settings/accounts_settings',
    title: 'Accounts' + TITLE_STRING,
  },

  /*******************************************
    Integrations Routes
  *******************************************/
  workplace_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'workplace_integrations',
    header: 'Workplace',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/workplace_integrations',
    title: 'Workplace' + TITLE_STRING,
  },
  facebook_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'facebook_integrations',
    header: 'Facebook',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/facebook_integrations',
    title: 'Facebook' + TITLE_STRING,
  },
  telegram_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'telegram_integrations',
    header: 'Telegram',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/telegram_integrations',
    title: 'Telegram' + TITLE_STRING,
  },
  slack_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'slack_integrations',
    header: 'Slack',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/slack_integrations',
    title: 'Slack' + TITLE_STRING,
  },
  teams_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'teams_integrations',
    header: 'Teams',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/teams_integrations',
    title: 'Teams' + TITLE_STRING,
  },
  whatsapp_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'whatsapp_integrations',
    header: 'WhatsApp',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/whatsapp_integrations',
    title: 'WhatsApp' + TITLE_STRING,
  },
  line_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'line_integrations',
    header: 'Line',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/line_integrations',
    title: 'Line' + TITLE_STRING,
  },
  widget_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'widget_integrations',
    header: 'Webchat',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/widget_integrations',
    title: 'Webchat' + TITLE_STRING,
  },
  google_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'google_integrations',
    header: 'Google Chat',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/google_integrations',
    title: 'Google Chat' + TITLE_STRING,
  },
  email_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'email_integrations',
    header: 'Email',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/email_integrations',
    title: 'Email' + TITLE_STRING,
  },
  mqtt_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'mqtt_integrations',
    header: 'Sensors',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/mqtt_integrations',
    title: 'Sensors' + TITLE_STRING,
  },
  bim_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'bim_integrations',
    header: 'BIM',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/bim_integrations',
    title: 'BIM' + TITLE_STRING,
  },
  api_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'api_integrations',
    header: 'API',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/api_integrations',
    title: 'API' + TITLE_STRING,
  },
  portfolio_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'portfolio_integrations',
    header: 'Portfolio',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/portfolio_integrations',
    title: 'Portfolio' + TITLE_STRING,
  },
  stripe_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'stripe_integrations',
    header: 'Stripe',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/stripe_integrations',
    title: 'Stripe' + TITLE_STRING,
  },
  zapier_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'zapier_integrations',
    header: 'Zapier',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/zapier_integrations',
    title: 'Zapier' + TITLE_STRING,
  },
  viber_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'viber_integrations',
    header: 'Viber',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/viber_integrations',
    title: 'Viber' + TITLE_STRING,
  },
  jmm_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'jmm_integrations',
    header: 'JTC Corporation',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/jmm_integrations',
    title: 'JTC Corporation' + TITLE_STRING,
  },
  exceltec_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'exceltec_integrations',
    header: 'Exceltec',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/exceltec_integrations',
    title: 'Exceltec' + TITLE_STRING,
  },
  procore_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'procore_integrations',
    header: 'Procore',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/procore_integrations',
    title: 'Procore' + TITLE_STRING,
  },
  sso_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'sso_integrations',
    header: 'SSO',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/sso_integrations',
    title: 'SSO' + TITLE_STRING,
  },
  uhoo_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'uhoo_integrations',
    header: 'uHoo',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/uhoo_integrations',
    title: 'uHoo' + TITLE_STRING,
  },
  microsoft365_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'microsoft365_integrations',
    header: 'Microsoft Dynamics 365',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/microsoft365_integrations',
    title: 'Microsoft Dynamics 365' + TITLE_STRING,
  },
  cpf_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'cpf_integrations',
    header: 'CPF',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/cpf_integrations',
    title: 'cpf' + TITLE_STRING,
  },
  sap_integrations: {
    key: PARENT_KEY.integrations.key,
    sub_key: 'sap_integrations',
    header: 'SAP',
    main_header: PARENT_KEY.integrations.label,
    path: '/integrations/sap_integrations',
    title: 'SAP' + TITLE_STRING,
  },
  /*******************************************
    Dashboard Routes
  *******************************************/
  dashboard: {
    key: PARENT_KEY.dashboard.key,
    sub_key: 'dashboard',
    header: 'Dashboard',
    main_header: PARENT_KEY.dashboard.label,
    path: '/dashboard/',
    title: 'Dashboard' + TITLE_STRING,
  },
  /*******************************************
    Analytics Routes
  *******************************************/
  analytics: {
    key: PARENT_KEY.analytics.key,
    sub_key: 'analytics',
    header: 'Analytics',
    main_header: PARENT_KEY.analytics.label,
    path: '/analytics/',
    title: 'Analytics' + TITLE_STRING,
  },
  /*******************************************
    Error Routes
  *******************************************/
  errors: {
    key: 'errors',
    sub_key: null,
    header: 'Errors',
    main_header: PARENT_KEY.dashboard.label,
    path: '/errors',
    title: 'Errors' + TITLE_STRING,
  },
  /*******************************************
     Subscription Plans Routes
  *******************************************/
  subscription_plans: {
    key: PARENT_KEY.subscription_plans.key,
    sub_key: 'subscription_plans',
    header: 'Subscription Plan',
    main_header: PARENT_KEY.subscription_plans.label,
    path: '/subscription_plans/',
    title: 'Subscription Plan' + TITLE_STRING,
  },
  /*******************************************
     Auth Routes
  *******************************************/
  google_oauth2: {
    key: 'auth',
    sub_key: 'google_oauth2',
    header: 'Google Oauth2',
    main_header: PARENT_KEY.dashboard.label,
    path: '/auth/google_oauth2',
    title: 'Google Oauth2' + TITLE_STRING,
  },
};

export const default_custom_permissions = {
  feature: true,
  feature_request: true,
  feature_request_read: true,
  feature_checklist: true,
  feature_checklist_read: true,
  feature_asset: true,
  feature_asset_read: true,
  feature_expenditure: true,
  feature_expenditure_read: true,
  feature_facility: true,
  feature_facility_read: true,
  feature_chat: true,
  feature_chat_read: true,
  feature_survey: true,
  feature_survey_read: true,
  feature_broadcast: true,
  feature_broadcast_read: true,
  feature_sor: true,
  feature_sor_read: true,
  feature_visitor: true,
  feature_visitor_read: true,
  feature_workflow: true,
  feature_workflow_read: true,
  feature_qrcode_webform: true,
  feature_qrcode_webform_read: true,
  feature_faq: true,
  feature_faq_read: true,
  feature_desk: true,
  feature_desk_read: true,
  feature_procurement: true,
  feature_procurement_read: true,
  feature_procurement_vendor: true,
  feature_procurement_vendor_read: true,
  feature_meter: true,
  feature_meter_read: true,
  feature_attendance: true,
  feature_attendance_read: true,
  feature_part: true,
  feature_part_read: true,
  feature_billing: true,
  feature_customer: true,
  feature_budget: true,
  feature_license: true,
  feature_vendor: true,
  feature_payment: true,
  feature_contract: true,
  feature_schedule: true,
  feature_lease: true,
  feature_permit_to_work: true,
  feature_delivery: true,
  setting: true,
  setting_visitor: true,
  setting_visitor_read: true,
  setting_general: true,
  setting_general_read: true,
  setting_request: true,
  setting_request_read: true,
  setting_checklist: true,
  setting_checklist_read: true,
  setting_facility: true,
  setting_facility_read: true,
  setting_channel: true,
  setting_channel_read: true,
  setting_chatbot: true,
  setting_chatbot_read: true,
  setting_feature: true,
  setting_feature_read: true,
  setting_account: true,
  setting_account_read: true,
  setting_desk: true,
  setting_desk_read: true,
  statistic: true,
  statistic_request_data: true,
  statistic_request_data_read: true,
  statistic_checklist_data: true,
  statistic_checklist_data_read: true,
  statistic_license_data: true,
  statistic_lease_data: true,
  statistic_expenditure: true,
  statistic_expenditure_read: true,
  statistic_responder: true,
  statistic_responder_read: true,
  statistic_asset_data: true,
  statistic_asset_data_read: true,
  statistic_mqtt_data: true,
  statistic_mqtt_data_read: true,
  statistic_meter_data: true,
  statistic_meter_data_read: true,
  statistic_permit_to_work_data: true,
  statistic_permit_to_work_data_read: true,
  statistic_sustainability_data: true,
  statistic_sustainability_data_read: true,
  statistic_attendance_data: true,
  statistic_attendance_data_read: true,
  statistic_desk_data: true,
  statistic_desk_data_read: true,
  statistic_facility_data: true,
  statistic_facility_data_read: true,
  statistic_toilet_feedback_data: true,
  statistic_toilet_feedback_data_read: true,
  statistic_requestor_sentiment: true,
  statistic_requestor_sentiment_read: true,
  statistic_audit_log: true,
  statistic_audit_log_read: true,
  statistic_people_counter_data: true,
  statistic_people_counter_data_read: true,
  statistic_emails_sent: true,
  integration: true,
  integration_telegram: true,
  integration_telegram_read: true,
  integration_facebook: true,
  integration_facebook_read: true,
  integration_workplace: true,
  integration_workplace_read: true,
  integration_slack: true,
  integration_slack_read: true,
  integration_team: true,
  integration_team_read: true,
  integration_whatsapp: true,
  integration_whatsapp_read: true,
  integration_line: true,
  integration_line_read: true,
  integration_webchat: true,
  integration_webchat_read: true,
  integration_google_chat: true,
  integration_google_chat_read: true,
  integration_mqtt: true,
  integration_mqtt_read: true,
  integration_bim: true,
  integration_bim_read: true,
  integration_api: true,
  integration_api_read: true,
  integration_portfolio: true,
  integration_portfolio_read: true,
  integration_stripe: true,
  integration_stripe_read: true,
  integration_zapier: true,
  integration_zapier_read: true,
  integration_jmm: true,
  integration_jmm_read: true,
  integration_viber: true,
  integration_viber_read: true,
  integration_procore: true,
  integration_uhoo: true,
  integration_uhoo_read: true,
  integration_cpf: true,
  integration_cpf_read: true,
  integration_bim: true,
  integration_bim_read: true,
  integration_sap: true,
  integration_sap_read: true,
  integration_exceltec: true,
  integration_exceltec_read: true,
  enable_agent_dynamics365: false,
};

export const default_agent_management = {
  enable_sensors: true,
  enable_audit_log: true,
  enable_api_token: true,
  enable_zapier: true,
  enable_export_workplace_conversation: true,
  enable_whatsapp: true,
  enable_agent_token: true,
  enable_report_fault: true,
  enable_custom_fault: true,
  enable_pm_checklist: true,
  enable_asset_tracking: true,
  enable_qr_code_web_form: true,
  enable_meter: true,
  enable_part: true,
  enable_vendor: true,
  enable_attendance: true,
  enable_booking: true,
  enable_desk_booking: true,
  enable_visitor: true,
  enable_schedule_of_rates: true,
  enable_procurement: true,
  enable_expenditure: true,
  enable_faq: true,
  enable_survey: true,
  enable_broadcast: true,
  enable_chat_with_staff: true,
  enable_custom_manager_accounts: true,
  enable_custom_dashboard: true,
  enable_budget: true,
  enable_jmm: true,
  enable_payment: true,
  enable_task_scheduling: true,
  enable_custom_sign_in_page: false,
  enable_procore: true,
  enable_bim: true,
  enable_delivery: true,
  enable_map_dashboard: true,
  enable_workpal: false,
  enable_analytic: false,
  enable_lease_management: true,
  enable_exceltec: true,
  enable_agent_dynamics365: false,
};

export const default_feature_config = {
  feature_survey: true,
  feature_booking: true,
  feature_chat_with_staff: true,
  feature_report_fault: true,
  feature_desk_booking: true,
  feature_schedule_of_rate: true,
  feature_procurement: true,
  feature_checklist: true,
  feature_visitor: true,
  feaure_expenditure: true,
  feature_faq: true,
  feature_broadcast: true,
  feature_asset_tracking: true,
  feature_custom_fault: true,
  feature_meter: true,
  feature_part: true,
  feature_permit_to_work: true,
  feature_vendor: true,
  feature_billing: true,
  feature_customer: true,
  feature_attendance: true,
  feature_qr_code_web_form: true,
  feature_license_management: true,
  feature_lease: true,
  feature_budget: true,
  feature_payment: true,
  feature_schedules: true,
  feature_delivery: true,
  feature_contract_management: true,
};

const expired_routes = [
  routes.subscription_plans, // DON'T REMOVE THIS LINE
  routes.accounts,
  routes.errors,
  routes.google_oauth2,
];

export const routes_by_role = ({ role, feature_config, custom_permissions, agent_management, days_remain }) => {
  if (!role) return [];
  if (!feature_config) feature_config = default_feature_config;
  if (!custom_permissions) custom_permissions = default_custom_permissions;
  if (!agent_management) agent_management = default_agent_management;

  const isEnableRequestsFeature =
    feature_config.feature_report_fault ||
    feature_config.feature_custom_fault ||
    feature_config.feature_chat_with_staff;

  const isExpired = days_remain < 0;
  if (isExpired) return expired_routes;

  let _routes = {
    manager: [
      routes.dashboard, // DON'T REMOVE THIS LINE
      routes.accounts,
      routes.errors,
      /////////////////////// FEATURES ///////////////////////
      custom_permissions.feature_request && isEnableRequestsFeature && routes.requests,
      feature_config.feature_checklist && custom_permissions.feature_checklist && routes.checklists,
      feature_config.feature_asset_tracking && custom_permissions.feature_asset && routes.assets,
      feature_config.feaure_expenditure && custom_permissions.feature_expenditure && routes.expenditures,
      feature_config.feature_budget && custom_permissions.feature_budget && routes.budgets,
      feature_config.feature_booking && custom_permissions.feature_facility && routes.facility_bookings,
      feature_config.feature_chat_with_staff && custom_permissions.feature_chat && routes.conversations,
      feature_config.feature_survey && custom_permissions.feature_survey && routes.surveys,
      feature_config.feature_broadcast && custom_permissions.feature_broadcast && routes.broadcasts,
      feature_config.feature_schedule_of_rate && custom_permissions.feature_sor && routes.schedule_of_rates,
      feature_config.feature_visitor && custom_permissions.feature_visitor && routes.visitors,
      feature_config.feature_faq && custom_permissions.feature_faq && routes.faqs,
      feature_config.feature_desk_booking && custom_permissions.feature_desk && routes.desk_bookings,
      feature_config.feature_delivery && custom_permissions.feature_delivery && routes.deliveries,
      feature_config.feature_procurement && custom_permissions.feature_procurement && routes.procurement,
      feature_config.feature_vendor && custom_permissions.feature_vendor && routes.vendors,
      feature_config.feature_meter && custom_permissions.feature_meter && routes.meters,
      feature_config.feature_attendance && custom_permissions.feature_attendance && routes.attendances,
      feature_config.feature_part && custom_permissions.feature_part && routes.parts,
      feature_config.feature_permit_to_work && custom_permissions.feature_permit_to_work && routes.permit_to_work,
      feature_config.feature_qr_code_web_form && custom_permissions.feature_qrcode_webform && routes.qr_codes,
      feature_config.feature_billing && custom_permissions.feature_billing && routes.billings,
      feature_config.feature_payment && custom_permissions.feature_payment && routes.payments,
      feature_config.feature_customer && custom_permissions.feature_customer && routes.customers,
      custom_permissions.feature_workflow && routes.workflows,
      feature_config.feature_license_management && custom_permissions.feature_license && routes.licenses,
      feature_config.feature_contract_management && custom_permissions.feature_contract_management && routes.contracts,
      feature_config.feature_lease_management && custom_permissions.feature_lease && routes.leases,
      feature_config.feature_schedules && custom_permissions.feature_schedule && routes.schedules,
      /////////////////////// STATISTICS ///////////////////////
      custom_permissions.statistic_request_data && routes.request_statistics,
      feature_config.feature_checklist && custom_permissions.statistic_checklist_data && routes.checklist_statistics,
      feature_config.feature_license_management &&
        custom_permissions.statistic_license_data &&
        routes.license_statistics,
      feature_config.feature_contract_management &&
        custom_permissions.statistic_contract_data &&
        routes.contract_statistics,
      feature_config.feature_lease_management && custom_permissions.statistic_lease_data && routes.lease_statistics,
      agent_management.enable_sensors && custom_permissions.statistic_mqtt_data && routes.mqtt_data_statistics,
      feature_config.feaure_expenditure && custom_permissions.statistic_expenditure && routes.expenditure_statistics,
      custom_permissions.statistic_responder && routes.responder_statistics,
      feature_config.feature_desk_booking && custom_permissions.statistic_desk_data && routes.desk_data_statistics,
      feature_config.feature_asset_tracking && custom_permissions.statistic_asset_data && routes.asset_statistics,
      custom_permissions.statistic_requestor_sentiment && routes.requestor_sentiment_statistics,
      custom_permissions.statistic_toilet_feedback_data && routes.toilet_feedback_data,
      custom_permissions.statistic_people_counter_data && routes.people_counter_statistics,
      feature_config.feature_meter && custom_permissions.statistic_meter_data && routes.meter_statistics,
      custom_permissions.statistic_sustainability_data && routes.sustainability_statistics,
      feature_config.feature_booking && custom_permissions.statistic_facility_data && routes.facilities_statistics,
      feature_config.feature_attendance && custom_permissions.statistic_attendance_data && routes.attendance_statistics,
      custom_permissions.statistic_emails_sent && routes.emails_sent_statistics,
      feature_config.feature_part && custom_permissions.feature_part && routes.part_statistics,
      custom_permissions.statistic_permit_to_work_data && routes.permit_to_work_statistics,
      /////////////////////// ANALYTICS ///////////////////////
      agent_management.enable_analytic && custom_permissions.analytics && routes.analytics,
      /////////////////////// SETTINGS ///////////////////////
      custom_permissions.setting_general && routes.quick_setup,
      custom_permissions.setting_channel && routes.channel_setup,
      custom_permissions.setting_chatbot && routes.chatbot_setup,
      custom_permissions.setting_feature && routes.feature_setup,
      feature_config.feature_desk_booking && custom_permissions.setting_desk && routes.desk_settings,
      feature_config.feature_visitor && custom_permissions.setting_visitor && routes.visitor_settings,
      routes.location_tag_settings,
      custom_permissions.setting_request && routes.request_settings,
      feature_config.feature_checklist && custom_permissions.setting_checklist && routes.checklist_settings,
      feature_config.feature_booking && custom_permissions.setting_facility && routes.facilities_settings,
      /////////////////////// INTEGRATIONS ///////////////////////
      custom_permissions.integration_telegram && routes.telegram_integrations,
      custom_permissions.integration_facebook && routes.facebook_integrations,
      custom_permissions.integration_workplace && routes.workplace_integrations,
      custom_permissions.integration_slack && routes.slack_integrations,
      custom_permissions.integration_team && routes.teams_integrations,
      custom_permissions.integration_whatsapp && routes.whatsapp_integrations,
      custom_permissions.integration_line && routes.line_integrations,
      custom_permissions.integration_webchat && routes.widget_integrations,
      custom_permissions.integration_google_chat && routes.google_integrations,
      agent_management.enable_sensors && custom_permissions.integration_mqtt && routes.mqtt_integrations,
      agent_management.enable_jmm && custom_permissions.integration_jmm && routes.jmm_integrations,
      custom_permissions.integration_viber && routes.viber_integrations,
      routes.google_oauth2,
      custom_permissions.integration_email && routes.email_integrations,
      agent_management.enable_bim && custom_permissions.integration_bim && routes.bim_integrations,
      agent_management.enable_procore && custom_permissions.integration_procore && routes.procore_integrations,
      agent_management.enable_payment && custom_permissions.integration_stripe && routes.stripe_integrations,
      agent_management.enable_uhoo && custom_permissions.uhoo_integrations && routes.uhoo_integrations,
      agent_management.enable_workpal && custom_permissions.integration_workpal && routes.cpf_integrations,
      agent_management.enable_exceltec && custom_permissions.exceltec_integrations && routes.exceltec_integrations,
      agent_management.enable_sap_integrate && custom_permissions.integration_sap && routes.sap_integrations,
      agent_management.enable_agent_dynamics365 && routes.microsoft365_integrations,
    ],
    admin: [
      routes.dashboard, // DON'T REMOVE THIS LINE
      routes.accounts,
      isEnableRequestsFeature && routes.requests,
      feature_config.feature_checklist && routes.checklists,
      feature_config.feature_asset_tracking && routes.assets,
      feature_config.feaure_expenditure && routes.expenditures,
      feature_config.feature_booking && routes.facility_bookings,
      feature_config.feature_budget && routes.budgets,
      routes.conversations,
      feature_config.feature_survey && routes.surveys,
      feature_config.feature_broadcast && routes.broadcasts,
      feature_config.feature_visitor && routes.visitor_settings,
      feature_config.feature_schedule_of_rate && routes.schedule_of_rates,
      feature_config.feature_visitor && routes.visitors,
      routes.request_statistics,
      feature_config.feature_checklist && routes.checklist_statistics,
      feature_config.feature_license_management && routes.license_statistics,
      feature_config.feature_contract_management && routes.contract_statistics,
      feature_config.feature_lease_management && routes.lease_statistics,
      feature_config.feature_part && routes.part_statistics,
      agent_management.enable_sensors && routes.mqtt_data_statistics,
      feature_config.feaure_expenditure && routes.expenditure_statistics,
      routes.responder_statistics,
      feature_config.feature_desk_booking && routes.desk_data_statistics,
      feature_config.feature_asset_tracking && routes.asset_statistics,
      routes.quick_setup,
      routes.location_tag_settings,
      routes.request_settings,
      feature_config.feature_checklist && routes.checklist_settings,
      feature_config.feature_booking && routes.facilities_settings,
      routes.alert_settings,
      routes.workflows,
      feature_config.feature_qr_code_web_form && routes.qr_codes,
      feature_config.feature_billing && routes.billings,
      feature_config.feature_payment && routes.payments,
      feature_config.feature_customer && routes.customers,
      feature_config.feature_faq && routes.faqs,
      routes.workplace_integrations,
      routes.facebook_integrations,
      routes.telegram_integrations,
      routes.slack_integrations,
      routes.teams_integrations,
      routes.whatsapp_integrations,
      routes.line_integrations,
      routes.widget_integrations,
      routes.google_integrations,
      routes.email_integrations,
      agent_management.enable_bim && custom_permissions.integration_bim && routes.bim_integrations,
      agent_management.enable_sensors && routes.mqtt_integrations,
      agent_management.enable_jmm && routes.jmm_integrations,
      agent_management.enable_analytic && routes.analytics,
      routes.subscription_plans,
      feature_config.feature_desk_booking && routes.desk_bookings,
      agent_management.enable_delivery && feature_config.feature_delivery && routes.deliveries,
      routes.channel_setup,
      routes.chatbot_setup,
      routes.feature_setup,
      routes.accounts_settings,
      feature_config.feature_desk_booking && routes.desk_settings,
      feature_config.feature_procurement && routes.procurement,
      feature_config.feature_procurement && routes.vendors,
      routes.toilet_feedback_data,
      routes.people_counter_statistics,
      agent_management.enable_api_token && routes.api_integrations,
      routes.portfolio_integrations,
      agent_management.enable_zapier && routes.zapier_integrations,
      feature_config.feature_meter && routes.meters,
      feature_config.feature_meter && routes.meter_statistics,
      routes.sustainability_statistics,
      feature_config.feature_booking && routes.facilities_statistics,
      feature_config.feature_attendance && routes.attendances,
      feature_config.feature_attendance && routes.attendance_statistics,
      feature_config.feature_permit_to_work && routes.permit_to_work,
      feature_config.feature_part && routes.parts,
      routes.errors,
      routes.requestor_sentiment_statistics,
      routes.emails_sent_statistics,
      agent_management.enable_audit_log && routes.audit_logs_statistics,
      routes.google_oauth2,
      routes.invoices,
      feature_config.feature_license_management && routes.licenses,
      feature_config.feature_contract_management && routes.contracts,
      agent_management.enable_task_scheduling && routes.schedules,
      routes.viber_integrations,
      routes.stripe_integrations,
      agent_management.enable_procore && routes.procore_integrations,
      agent_management.enable_custom_sign_in_page && feature_config.feature_tenant_portal && routes.custom_pages,
      agent_management.enable_payment && routes.stripe_integrations,
      agent_management.enable_sso && routes.sso_integrations,
      agent_management.enable_uhoo && routes.uhoo_integrations,
      routes.permit_to_work_statistics,
      agent_management.enable_workpal && routes.cpf_integrations,
      feature_config.feature_lease_management && custom_permissions.feature_lease && routes.leases,
      agent_management.enable_exceltec && routes.exceltec_integrations,
      agent_management.enable_sap_integrate && routes.sap_integrations,
      agent_management.enable_agent_dynamics365 && routes.microsoft365_integrations,
    ],
  };

  return _routes[role].filter(Boolean);
};

export const get_default_route = (available_routes) => {
  if (available_routes.length === 0) return '/dashboard';
  if (!available_routes[0]) return '/dashboard';
  return available_routes[0].path;
};
