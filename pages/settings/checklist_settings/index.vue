<template>
  <checklist-settings
    :checklistSettings="checklistSettings"
    :pageInformation="pageInformation"
    :labels="assignedLabels"
  />
</template>

<script>
import RSVP from 'rsvp';
import { mapState } from 'vuex';

import ChecklistSettings from '@/components/Settings/ChecklistSettings/Core/ChecklistSettings.vue';

import { LABEL_TYPES } from '@/utils/consts';

export default {
  watchQuery: ['tab'],
  head() {
    return {
      title: this.current_menu.title || '',
    };
  },

  beforeDestroy() {
    if (this.current_menu?.sub_key !== 'checklist_settings') {
      this.$store.dispatch('checklist_settings/setPayload', undefined);
    }
  },

  async asyncData({ store, route }) {
    const payload = store.state.checklist_settings.payload;
    const commonDispatches = [
      store.dispatch('managers/loadAssignmentManagers'),
      store.dispatch('managers/loadManagerProfile'),
      store.dispatch('managers/loadAssignmentStaffs'),
      store.dispatch('agents/loadFeatureSettings'),
      store.dispatch('meters/loadAll', {
        page: 1,
        per_page: 10,
        data_sort: 'name',
        order_sort: 'asc',
      }),
      store.dispatch('parts/loadAll', {
        page: 1,
        per_page: 10,
        data_sort: 'name',
        order_sort: 'asc',
      }),
      store.dispatch('checklist_process_flows/loadAllProcessFlows'),
    ];

    let tab = 'active';
    let loadAction = 'loadAll';

    let filter_remembered = {};
    if (route.query.tab === 'draft') {
      tab = 'draft';
      loadAction = 'loadDrafts';
      filter_remembered = store.state.filter_remembered.settings.checklists.drafts;
    } else {
      filter_remembered = store.state.filter_remembered.settings.checklists.actives;
    }
    let loadParams = {
      per_page: 10,
      page: 1,
      ...route.query,
      ...filter_remembered,
    };
    store.dispatch('checklist_settings/setCurrentTab', tab);
    if (payload) {
      loadParams = { ...loadParams, ...payload };
    }

    await RSVP.all([...commonDispatches, store.dispatch('checklist_settings/' + loadAction, loadParams)]);
  },

  components: {
    ChecklistSettings,
  },
  computed: {
    ...mapState({
      checklistSettings: (state) => state.checklist_settings.checklist_settings,
      pageInformation: (state) => state.checklist_settings.page_information,
      current_menu: (state) => state.menu.current_menu,
      assignedLabels: (state) => state.checklist_labels[LABEL_TYPES.checklist.key].assigned_labels,
    }),
  },
};
</script>

<style lang="less" scoped></style>
