<template>
  <checklists
    :checklists="checklists"
    :pageInformation="pageInformation"
    :checklistsSignOff="checklistsSignOff"
    :pageInformationSignOff="pageInformationSignOff"
    :labels="assignedLabels"
  />
</template>

<script>
import _ from 'lodash';
import { mapState } from 'vuex';

import Checklists from '@/components/Checklists/Core/Checklists.vue';

import { clearRememberFilter } from '@/utils/helper';
import { FILTER_REMEMBERED_PROPERTIES, LABEL_TYPES } from '@/utils/consts';
const pendingChecklistFilterKey = `${FILTER_REMEMBERED_PROPERTIES.CHECKLIST}.${FILTER_REMEMBERED_PROPERTIES.PENDING_CHECKLIST}`;
const forReviewChecklistFilterKey = `${FILTER_REMEMBERED_PROPERTIES.CHECKLIST}.${FILTER_REMEMBERED_PROPERTIES.FOR_REVIEW_CHECKLIST}`;

import RSVP from 'rsvp';

export default {
  components: { Checklists },
  watchQuery: ['tab'],
  head() {
    return {
      title: this.current_menu.title,
    };
  },
  async asyncData({ store, route }) {
    const checklist_ids = route.query.checklist_ids || [];
    const param_tracking_id = route.query.param_tracking_id || undefined;

    if (param_tracking_id) {
      store.dispatch('param_trackings/setParamTrackingId', param_tracking_id);
    }
    const filter_remembered =
      route.query.tab == 'pending' || !route.query.tab
        ? store.state.filter_remembered.checklist.pendingChecklist
        : store.state.filter_remembered.checklist.forReviewChecklist;
    const label_ids =
      filter_remembered && filter_remembered?.label_ids ? JSON.stringify(filter_remembered.label_ids) : undefined;

    const ids = !_.isEmpty(checklist_ids) ? { ids: route.query.checklist_ids } : {};
    const page = !_.isEmpty(checklist_ids) ? { page: 1 } : {};

    const commonParams = {
      param_tracking_id,
      q: route.query.checklist_id || '',
      ...route.query,
      ...filter_remembered,
      ...ids,
      ...page,
      label_ids,
    };

    const actions = [];

    switch (route.query.tab) {
      case 'pending':
        actions.push(store.dispatch('checklists/setChecklistIds', checklist_ids));
        actions.push(
          store
            .dispatch('checklists/loadAll', commonParams)
            .catch((err) => clearRememberFilter(store, pendingChecklistFilterKey, err)),
        );
        actions.push(store.dispatch('requests/getDueDateReminders'));
        actions.push(store.dispatch('checklists/setCurrentTab', '1'));
        actions.push(store.dispatch('managers/loadAssignmentManagers'));
        break;

      case 'reviewing':
        actions.push(store.dispatch('checklists/setChecklistIds', checklist_ids));
        actions.push(
          store
            .dispatch('checklists/loadSignOff', commonParams)
            .catch((err) => clearRememberFilter(store, forReviewChecklistFilterKey, err)),
        );
        actions.push(store.dispatch('requests/getDueDateReminders'));

        if (route.query.checklist_id) {
          actions.push(store.dispatch('checklists/setIsShowDrawerChecklistReviewing', true));
          actions.push(store.dispatch('checklists/setCurrentDrawerChecklistReviewingGutter', 'review'));
          actions.push(store.dispatch('checklists/setIsLinkedFromMeter', true));
          actions.push(store.dispatch('checklists/setCurrentChecklistId', route.query.checklist_id));
        }

        actions.push(store.dispatch('checklists/setCurrentTab', '2'));
        break;

      default:
        actions.push(
          store
            .dispatch('checklists/loadAll', commonParams)
            .catch((err) => clearRememberFilter(store, pendingChecklistFilterKey, err)),
        );

        actions.push(store.dispatch('requests/getDueDateReminders'));
        actions.push(store.dispatch('checklists/setCurrentTab', '1'));
        actions.push(store.dispatch('managers/loadAssignmentManagers'));
        break;
    }

    // actions.push(store.dispatch('checklist_labels/getAssignedLabels'));
    await RSVP.all(actions);
  },
  computed: {
    ...mapState({
      current_menu: (state) => state.menu.current_menu,
      checklists: (state) => state.checklists.checklists,
      pageInformation: (state) => state.checklists.page_information,
      checklistsSignOff: (state) => state.checklists.checklists_sign_off,
      pageInformationSignOff: (state) => state.checklists.page_information_sign_off,
      assignedLabels: (state) => state.checklist_labels[LABEL_TYPES.checklist.key].assigned_labels,
    }),
    routeHash() {
      return this.$route.hash;
    },
  },
};
</script>

<style lang="less" scoped></style>
