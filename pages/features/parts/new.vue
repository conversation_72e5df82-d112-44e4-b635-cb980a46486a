<template>
  <div class="features-facility-bookings-container">
    <div class="fc-breadcrumb">
      <a-breadcrumb>
        <span slot="separator"><BreadcrumSeparatorIcon /></span>
        <a-breadcrumb-item>
          <nuxt-link to="/features/parts">
            Parts
          </nuxt-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>
          New Part
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <div style="padding: 20px 30px; background: #fff;">
      <a-form :form="form" style="width: 400px" class="fc-reduce-space-form" :colon="false">
        <part-form :isAdd="true" />
        <a-form-item>
          <a-button type="primary" block size="large" @click="handleSubmit" :loading="isLoading">Submit</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script>
import RSVP from 'rsvp';
import { mapState } from 'vuex';

import PartForm from '../../../components/Parts/MainActions/Actions/PartForm.vue';
import { LABEL_TYPES } from '@/utils/consts';

export default {
  components: {
    PartForm,
  },
  data() {
    return {
      isVisible: false,
      isVisibleAsset: false,
      form: this.$form.createForm(this),
      isLoading: false,
    };
  },
  computed: {
    ...mapState({
      faultLocations: (state) => state['request-types'].faultLocations,
      selectedAssets: (state) => state.linked_items.selectedAssets,
      selectedLabels: (state) => state.checklist_labels[LABEL_TYPES.part.key].selected_labels,
      selectedChecklists: (store) => store.linked_items.selectedChecklists,
      selectedVendors: (store) => store.linked_items.selectedVendors,
    }),
  },

  methods: {
    onClickNewBtn() {
      this.isVisible = true;
    },
    onCloseModal() {
      this.isVisible = false;
    },
    handleSubmit(e) {
      e.preventDefault();

      this.form.validateFields(async (err, values) => {
        if (!err) {
          this.isLoading = true;
          let formData = new FormData();

          formData.append('name', values.name);
          formData.append('quantity', values.quantity);
          formData.append('location_ids', JSON.stringify(values.location_ids));
          formData.append('asset_ids', JSON.stringify([...this.selectedAssets.map((asset) => asset.id)]));
          formData.append('label_ids', JSON.stringify(this.selectedLabels));
          formData.append(
            'checklist_ids',
            JSON.stringify([...this.selectedChecklists.map((checklist) => checklist.id)]),
          );
          formData.append('vendor_ids', JSON.stringify([...this.selectedVendors.map((vendor) => vendor.id)]));

          if (values.part_store_id) formData.append('part_store_id', values.part_store_id);

          if (values.images.length > 0) {
            let imageForm = new FormData();
            imageForm.append('file', values.images[0].originFileObj);
            imageForm.append('attachment_ids', JSON.stringify([]));
            let res = await this.$store.dispatch('parts/uploadPartImage', imageForm);
            res && formData.append('attachment_ids', JSON.stringify([res.id]));
          }
          this.$store
            .dispatch('parts/createPart', formData)
            .then(() => {
              this.isVisible = false;
              this.$store.dispatch('parts/loadAll');
              this.$message.success('Created Part successfully');
              this.$router.push({ path: '/features/parts' });
              this.$store.dispatch('checklist_labels/setSelectedLabels', {
                labelType: LABEL_TYPES.part,
                selected_labels: [],
              });
            })
            .catch((error) => {
              this.$message.error(error.response.data.message);
            })
            .finally(() => (this.isLoading = false));
        } else {
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
