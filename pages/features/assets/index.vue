<template>
  <asset-trackings />
</template>

<script>
import { mapState } from 'vuex';

import RSVP from 'rsvp';
import AssetTrackings from '../../../components/AssetManagement/Core/AssetTrackings.vue';
import { clearRememberFilter } from '~/utils/helper';
import { FILTER_REMEMBERED_PROPERTIES } from '~/utils/consts';
export default {
  components: {
    AssetTrackings,
  },
  watchQuery: ['tab'],
  head() {
    return {
      title: this.current_menu.title,
    };
  },
  asyncData({ store, route, query, redirect, router }) {
    if (route.query.asset_tracking_id && route.query.action) {
      // example: asset_tracking_id=22555&action=history&history_tab=downtime
      store.dispatch('assets/loadAssetTracking', route.query.asset_tracking_id).then(() => {
        store.dispatch('assets/setCurrentDrawerGutter', route.query.action);
        store.dispatch('assets/setIsShowDrawer', true);
      });
    }
    const assetsTable = store.state.filter_remembered.assetsTable;
    const assetsTree = store.state.filter_remembered.assetsTree;
    const isValidCustomField = assetsTable && assetsTable.custom_fields && _.isArray(assetsTable.custom_fields);
    const custom_fields = isValidCustomField ? JSON.stringify(assetsTable.custom_fields) : undefined;
    switch (route.query.tab) {
      case 'table_view': {
        return RSVP.all([
          store.dispatch('assets/setCurrentTab', 'table_view'),
          store.dispatch('assets/loadAssetFields'),
          store.dispatch('managers/loadManagerProfile'),
          store
            .dispatch('assets/loadAssetTrackings', {
              per_page: 10,
              q: query.asset_id,
              ...assetsTable,
              custom_fields,
              isShowClearFilter: undefined,
            })
            .catch((err) => clearRememberFilter(store, FILTER_REMEMBERED_PROPERTIES.ASSETS_TABLE, err)),
          store.dispatch('assets/getFilterCustomFields'),
          store.dispatch('assets/getFilterFields'),
        ]);
      }
      case 'tree_view': {
        return RSVP.all([
          store.dispatch('assets/setCurrentTab', 'tree_view'),
          store.dispatch('assets/loadAssetFields'),
          store
            .dispatch('assets/loadAssetTree', {
              id: null,
              page: 1,
              per_page: 10,
              ...assetsTree,
              isShowClearFilter: undefined,
            })
            .catch((err) => clearRememberFilter(store, FILTER_REMEMBERED_PROPERTIES.ASSETS_TREE, err)),
        ]);
      }
      default: {
        return RSVP.all([
          store.dispatch('assets/setCurrentTab', 'table_view'),
          store.dispatch('assets/loadAssetFields'),
          store.dispatch('managers/loadManagerProfile'),
          store
            .dispatch('assets/loadAssetTrackings', {
              per_page: 10,
              q: query.asset_id,
              ...assetsTable,
              custom_fields,
              isShowClearFilter: undefined,
            })
            .catch((err) => clearRememberFilter(store, FILTER_REMEMBERED_PROPERTIES.ASSETS_TABLE, err)),
          store.dispatch('assets/getFilterCustomFields'),
          store.dispatch('assets/getFilterFields'),
        ]);
      }
    }
  },
  computed: {
    ...mapState({
      current_menu: (state) => state.menu.current_menu,
    }),
  },
};
</script>

<style lang="less" scoped></style>
