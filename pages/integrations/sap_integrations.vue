<template>
  <sap :data="sap_info" />
</template>

<script>
import { mapState } from 'vuex';
import Sap from '@/components/Integrations/Core/SAP/Sap.vue';
export default {
  components: { Sap },
  head() {
    return {
      title: this.current_menu.title,
    };
  },
  asyncData({ store, route }) {
    store.dispatch('integrations/loadSapInfo');
  },
  computed: {
    ...mapState({
      current_menu: (state) => state.menu.current_menu,
      sap_info: (state) => state.integrations.sap_info,
    }),
  },
};
</script>

<style lang="less" scoped></style>
