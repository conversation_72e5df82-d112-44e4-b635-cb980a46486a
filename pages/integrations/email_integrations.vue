<template>
  <div class="integrations">
    <email-setup />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import EmailSetup from '../../components/Integrations/Core/Email/EmailSetup.vue';
export default {
  components: {
    EmailSetup,
  },
  head() {
    return {
      title: this.current_menu.title,
    };
  },
  asyncData({ store }) {
    store.dispatch('integrations/loadEmailAddress');
    store.dispatch('managers/loadManagerProfile');
  },
  computed: {
    ...mapState({
      current_menu: (state) => state.menu.current_menu,
    }),
  },
};
</script>

<style lang="less" scoped></style>
