<template>
  <meter-statistics />
</template>

<script>
import { mapState } from 'vuex';
import { convertToNumber } from '@/utils/helper';
import MeterStatistics from '@/components/MeterStatistics/Core/MeterStatistics.vue';

export default {
  components: {
    MeterStatistics,
  },
  watchQuery: ['tab'],
  head() {
    return {
      title: this.current_menu.title,
    };
  },
  computed: {
    ...mapState({
      current_menu: (state) => state.menu.current_menu,
      dateRange: (state) => state.meter_statistics.dateRange,
      selectedMeters: (state) => state.meter_statistics.currentMultipleSelectedMeter,
    }),
  },
  methods: {
    decorateFilterMeter() {
      let payload = {
        start_date:
          this.dateRange?.startDate ||
          moment()
            .subtract(30, 'days')
            .format('DD/MM/YYYY'),
        end_date: this.dateRange?.endDate || moment().format('DD/MM/YYYY'),
        ...(this.selectedMeters.length && {
          meter_ids: JSON.stringify(this.selectedMeters),
        }),
      };
      return payload;
    },
  },
  asyncData({ store, route, query }) {
    switch (route.query.tab) {
      case 'tracking':
        store.dispatch('meter_statistics/loadMqttTrackings'),
          store.dispatch('managers/loadManagerProfile'),
          store.dispatch('meter_statistics/setCurrentTab', 'tracking'),
          store.dispatch('meters/loadAll');
        break;
      case 'meter_data':
        store.dispatch('managers/loadManagerProfile'),
          store.dispatch('meter_statistics/setCurrentTab', 'meter_data'),
          store.dispatch('meters/loadAll');
        break;
      case 'chart':
        store.dispatch('managers/loadManagerProfile'),
          store.dispatch('meter_statistics/setCurrentTab', 'chart'),
          store.dispatch('meters/loadAll');
        break;
      case 'chart_generator':
        store.dispatch('managers/loadManagerProfile'),
          store.dispatch('meter_statistics/setCurrentTab', 'chart_generator'),
          store.dispatch('meters/loadAll');
        store.dispatch('prompts/loadAll', {
          page: 1,
          per_page: 100,
          interable_type: 'Meter',
          data_sort: 'created_at',
          order_sort: 'desc',
        });
        break;
      default:
        store.dispatch('meter_statistics/loadMqttTrackings'),
          store.dispatch('managers/loadManagerProfile'),
          store.dispatch('meter_statistics/setCurrentTab', 'tracking'),
          store.dispatch('meters/loadAll');
        break;
    }
  },
  mounted() {
    let meter_id = this.$route.query.meter_id ? convertToNumber(this.$route.query.meter_id) : null;
    // Reset selected meter if no meter_id is specified
    if (!meter_id) {
      this.$store.dispatch('meter_statistics/resetSelectedMeter');
      this.$store.dispatch('meter_statistics/resetExternalChartData');
      this.$store.dispatch('meter_statistics/setIsMultipleRender', false);
      this.$store.dispatch('meter_statistics/setIsVisbileMeterChart', false);
    }
  },
};
</script>
