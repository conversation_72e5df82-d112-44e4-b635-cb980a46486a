import { getData, postData, putData, editMutation, deleteData, combinePageInformation } from '@/utils/store-utils';
import { mergeArraysUniqueById } from '@/utils/helper';

export const state = () => ({
  requests: [],
  isLoaded: false,
  isRequestGutterLoaded: false,
  isShowRequestFilter: false,
  requestAllStatus: [],
  requestAllRequestType: [],
  requestAllFaultType: [],
  requestAllLocationTag: [],
  totalPage: 0,
  requestFilterCondition: {
    status: undefined,
    facility_id: undefined,
    fault_location_id: undefined,
    request_type_id: undefined,
    start_date: undefined,
    end_date: undefined,
    data_sort: undefined,
    order_sort: undefined,
    page: 1,
    per_page: 10,
    q: undefined,
  },
  reportFaultImages: [],
  chosenRequest: null,
  request: null,
  link_requests: [],
  link_request_page_information: {
    page: 1,
    per_page: 10,
  },
  selected_link_requests: [],
  requestHistory: [],
  pagination: {},
  filterPayload: {},
  isShowModalRequestSignature: false,
  link_other_requests: [],
  link_other_requests_page_information: {
    page: 1,
    per_page: 10,
  },
  selected_link_other_requests: { ids: [], case_ids: [] },
  selected_requests: [],
  is_create_request_loading: false,
  due_date_reminders: [],
  requestors: [],
});

export const mutations = {
  SET_REQUESTS(state, requests) {
    state.requests = requests;
  },
  SET_PAGINATION(state, pagination) {
    state.pagination = pagination;
  },
  SET_CURRENT_PAGE(state, currentPage) {
    state.currentPage = currentPage;
  },
  SET_IS_LOADED(state, isLoaded) {
    state.isLoaded = isLoaded;
  },
  SET_PAGE_SIZE(state, pageSize) {
    state.pageSize = pageSize;
  },
  SET_IS_REQUEST_GUTTER_LOADED(state, isRequestGutterLoaded) {
    state.isRequestGutterLoaded = isRequestGutterLoaded;
  },
  SET_REQUEST_ALL_STATUS(state, requestAllStatus) {
    state.requestAllStatus = requestAllStatus;
  },
  SET_REQUEST_ALL_REQUEST_TYPE(state, requestAllRequestType) {
    state.requestAllRequestType = requestAllRequestType;
  },
  SET_REQUEST_ALL_FAULT_TYPE(state, requestAllFaultType) {
    state.requestAllFaultType = requestAllFaultType;
  },
  SET_REQUEST_ALL_LOCATION_TAG(state, requestAllLocationTag) {
    state.requestAllLocationTag = requestAllLocationTag;
  },
  SET_IS_SHOW_REQUEST_FILTER(state, isShowRequestFilter) {
    state.isShowRequestFilter = isShowRequestFilter;
  },
  SET_TOTAL_PAGE(state, totalPage) {
    state.totalPage = totalPage;
  },
  SET_REQUEST_FILTER_CONDITION(state, requestFilterCondition) {
    state.requestFilterCondition = requestFilterCondition;
  },
  PUSH_REPORT_FAULTS_IMAGES(state, reportFaultImage) {
    state.reportFaultImages.push(reportFaultImage);
  },
  SET_REPORT_FAULTS_IMAGES(state, reportFaultImages) {
    state.reportFaultImages = reportFaultImages;
  },
  SET_CHOSEN_REQUEST(state, chosenRequest) {
    state.chosenRequest = chosenRequest;
  },
  SET_REQUEST(state, request) {
    state.request = request;
  },
  PUSH_REQUESTS(state, requests) {
    state.requests = [...state.requests, ...requests];
  },
  SET_LINK_REQUEST_PAGE_INFORMATION(state, link_request_page_information) {
    state.link_request_page_information = link_request_page_information;
  },
  SET_LINK_REQUESTS(state, link_requests) {
    state.link_requests = link_requests;
  },
  SET_SELECTED_LINK_REQUESTS(state, selected_link_requests) {
    state.selected_link_requests = selected_link_requests;
  },
  SET_LOAD_MORE_LOCATION_TAGS(state, locationTags) {
    state.requestAllLocationTag = [...state.requestAllLocationTag, ...locationTags];
  },
  SET_LOAD_MORE_FAULT_TYPES(state, faultTypes) {
    state.requestAllFaultType = [...state.requestAllFaultType, ...faultTypes];
  },
  SET_PROCESSING_ATTACHMENT(state, attachment_processing) {
    state.request.attachment_processing = attachment_processing;
  },
  SET_COMPLETION_ATTACHMENT(state, attachment_completion) {
    state.request.attachment_completion = attachment_completion;
  },
  SET_REQUEST_HISTORY(state, histories) {
    state.requestHistory = histories;
  },
  SET_FILTER_PAYLOAD(state, filterPayload) {
    state.filterPayload = filterPayload;
  },
  SET_IS_SHOW_MODAL_REQUEST_SIGNATURE(state, isShowModalRequestSignature) {
    state.isShowModalRequestSignature = isShowModalRequestSignature;
  },
  PUSH_LINK_OTHER_REQUESTS(state, payload) {
    state.link_other_requests = payload;
  },
  SET_LINK_OTHER_REQUESTS(state, payload) {
    state.link_other_requests = payload;
  },
  SET_SELECTED_LINK_OTHER_REQUESTS(state, payload) {
    state.selected_link_other_requests = payload;
  },
  SET_SELECTED_REQUESTS(state, selected_requests) {
    state.selected_requests = selected_requests;
  },
  SET_IS_CREATE_REQUEST_LOADING(state, is_create_request_loading) {
    state.is_create_request_loading = is_create_request_loading;
  },
  SET_DUE_DATE_REMINDERS(state, due_date_reminders) {
    state.due_date_reminders = due_date_reminders;
  },
  SET_REQUESTORS(state, requestors) {
    state.requestors = requestors;
  },
};

export const actions = {
  async loadAll({ commit, dispatch, getters }, payload = {}) {
    commit('SET_IS_LOADED', false);

    let response = await getData(`/web/requests`, this.$axios, { ...payload });

    commit('SET_REQUESTS', response.data);
    commit('SET_PAGINATION', combinePageInformation(response));
    commit('SET_IS_LOADED', true);

    return response;
  },

  async loadRequests({ commit }, payload = {}) {
    let response = await getData(`/api_v2/requests`, this.$axios, ...payload);

    commit('SET_REQUESTS', response.data);
    return response;
  },

  async loadRequestAllStatus({ commit }) {
    let response = await getData(`/api/requests/all_status`, this.$axios);

    commit('SET_REQUEST_ALL_STATUS', response.data);
  },

  async loadRequestAllRequestType({ commit }) {
    let response = await getData(`/api/requests/all_request_types`, this.$axios);

    commit('SET_REQUEST_ALL_REQUEST_TYPE', response.data);
  },

  async loadRequestAllFaultType({ commit }) {
    let response = await getData(`/api/facilities`, this.$axios);

    commit('SET_REQUEST_ALL_FAULT_TYPE', response.data);
  },

  async loadRequestAllLocationTag({ commit }) {
    let response = await getData(`/api/fault_locations`, this.$axios);

    commit('SET_REQUEST_ALL_LOCATION_TAG', response.data);
  },

  async loadRequestAllLocationTagNoPaginate({ commit, payload = {} }) {
    let response = await getData(`/api/fault_locations/locations_not_paginate`, this.$axios, payload);

    commit('SET_REQUEST_ALL_LOCATION_TAG', response.data);
  },

  async createRequest({ commit, dispatch }, payload) {
    await postData(`/api_v2/requests`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    dispatch('loadAll');
  },

  async uploadFaultImages({ commit }, payload) {
    let response = await postData(`/api_v2/requests/upload_fault_attachments`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    commit('PUSH_REPORT_FAULTS_IMAGES', response.id);
  },

  async getRequest({ commit }, id) {
    let response = await getData(`/web/requests/${id}`, this.$axios);

    const linkedRequests = response?.linked_requests;
    if (linkedRequests?.length) {
      let linkedRequestsIds = linkedRequests.map((item) => item.id);
      let linkedRequestsCaseIds = linkedRequests.map((item) => item.case_id);
      commit('SET_SELECTED_LINK_OTHER_REQUESTS', { ids: linkedRequestsIds, case_ids: linkedRequestsCaseIds });
    } else {
      commit('SET_SELECTED_LINK_OTHER_REQUESTS', { ids: [], case_ids: [] });
    }
    commit('SET_REQUEST', response);
    return response;
  },

  async deleteRequest({ commit }, id) {
    const response = await deleteData(`/api/requests/${id}`, this.$axios);
    return response;
  },

  async updateStatusRequest({ commit, getters, dispatch }, payload = {}) {
    let response = await postData(`/api/requests/${payload.id}/update_status`, this.$axios, payload);

    const newData = getters.getRequests.map((item) =>
      item.id === payload.id
        ? {
            ...item,
            status: payload.status,
            status_title: payload.status_title,
          }
        : item,
    );

    commit('SET_REQUESTS', newData);
    dispatch('getRequest', payload.id);

    return response;
  },

  async updatePriorityRequest({ commit, getters }, payload = {}) {
    let response = await postData(`/api/requests/${payload.id}/update_priority`, this.$axios, payload);

    const newData = getters.getRequests.map((item) =>
      item.id === payload.id ? { ...item, priority: payload.priority } : item,
    );
    commit('SET_REQUESTS', newData);

    return response;
  },

  updateRequestInRequests({ commit, getters }, request = {}) {
    let isEditRequest = request.id === getters.getRequest.id;
    let isRequestExist = getters.getRequests.findIndex((item) => item.id === request.id);

    const newRequest = isEditRequest ? { ...getters.getRequest, ...request } : request;

    let newData = [...getters.getRequests];
    if (isEditRequest) {
      newData = getters.getRequests.map((item) => (item.id === request.id ? { ...item, ...request } : item));
    } else if (isRequestExist == -1) {
      newData.unshift(request);
    }

    if (isEditRequest) commit('SET_REQUEST', newRequest);
    commit('SET_REQUESTS', newData);
  },

  async assignManagerRequest({ commit, dispatch }, payload = {}) {
    // manager_ids
    let response = await postData(`/api/requests/${payload.id}/assign_manager`, this.$axios, payload.formData);

    return response;
  },

  async assignFaultSubTypeTag({ commit }, { id, fault_sub_type_tag_id }) {
    let response = await postData(`/api/requests/${id}/assign_fault_sub_type_tag`, this.$axios, {
      fault_sub_type_tag_id: fault_sub_type_tag_id,
    });

    return response;
  },

  async removeFaultSubTypeTag({ commit }, { id }) {
    let response = await putData(`/api/requests/${id}/remove_fault_sub_type_tag`, this.$axios);

    return response;
  },

  async assignFaultLocationsRequest({ commit }, payload = {}) {
    // fault_location_ids
    let response = await postData(`/api/requests/${payload.id}/assign_fault_locations`, this.$axios, payload.formData);

    return response;
  },

  async updateProcessingRemarks({ commit, getters }, payload = {}) {
    let response = await postData(`/api/requests/${payload.id}/update_processing_remarks`, this.$axios, payload);

    const newData = { ...getters.getRequest, processing_remarks: payload.remarks };
    commit('SET_REQUEST', newData);

    return response;
  },

  async updateCompleteRemarks({ commit, getters }, payload = {}) {
    let response = await postData(`/api/requests/${payload.id}/update_completion_remarks`, this.$axios, payload);

    const newData = { ...getters.getRequest, completion_remarks: payload.remarks };
    commit('SET_REQUEST', newData);

    return response;
  },

  async updateFaultType({ commit, getters }, payload = {}) {
    let response = await postData(`/api/requests/${payload.id}/update_fault_type`, this.$axios, payload);

    commit('SET_REQUEST', response);
    return response;
  },

  async removeFaultType({ commit, getters }, { id }) {
    let response = await putData(`/api/requests/${id}/remove_fault_type`, this.$axios);

    return response;
  },

  async loadMoreRequest({ commit, getters, dispatch }, payload = {}) {
    let response = await getData(`/web/requests`, this.$axios, { ...payload });

    commit('PUSH_REQUESTS', response.data);
    return response;
  },

  async loadMoreLinkOtherRequests({ commit, state }, payload = {}) {
    let response = await getData(`/web/requests`, this.$axios, { ...payload });

    commit('PUSH_LINK_OTHER_REQUESTS', [...state.link_other_requests, ...response.data]);
    return response;
  },

  async loadAllLinkOtherRequests({ commit, getters, dispatch }, payload = {}) {
    let response = await getData(`/web/requests`, this.$axios, { ...payload });

    commit('SET_LINK_OTHER_REQUESTS', response.data);
    return response;
  },

  async uploadAttachmentProcessingAttachment({ commit }, { id, file }) {
    let response = await postData(`/api/requests/${id}/upload_processing_attachment`, this.$axios, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    commit('SET_PROCESSING_ATTACHMENT', response);
    return response;
  },

  async uploadAttachmentCompleteAttachment({ commit }, { id, file }) {
    let response = await postData(`/api/requests/${id}/upload_completion_attachment`, this.$axios, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    commit('SET_COMPLETION_ATTACHMENT', response);

    return response;
  },

  async linkAssetIds({ commit }, { id, asset_tracking_ids }) {
    let response = await postData(`/api_v2/requests/${id}/link_assets`, this.$axios, { asset_tracking_ids });

    return response;
  },

  async unlinkAssetId({ commit }, { id }) {
    let response = await postData(`/api/requests/${id}/unlink_asset_id`, this.$axios);

    return response;
  },

  async sendRequestRemarks({ commit }, { id, payload }) {
    let response = await postData(`/api/requests/${id}/send_request_remarks`, this.$axios, payload);

    return response;
  },

  async deleteRequestRemark({ commit }, { id, payload }) {
    let response = await deleteData(`api/requests/${id}/delete_request_remarks`, this.$axios, payload);

    return response;
  },

  async toggleRequestDefaultDisableRemarksBroadcast({ commit }, { id, payload }) {
    let response = await putData(`api/requests/${id}/default_disable_broadcast_for_remark`, this.$axios, payload);

    return response;
  },

  async toggleRequestRemarkBroadcast({ commit }, { id, payload }) {
    // id is a req's remark(note)'s id
    let response = await putData(`api_v2/request_remarks/${id}/is_disable_broadcast`, this.$axios, payload);

    return response;
  },

  async createFaultSignature({ commit }, { id, fault_signature, signatory }) {
    let response = await postData(`/api/requests/${id}/create_fault_signature`, this.$axios, {
      fault_signature: fault_signature,
      signatory: signatory,
    });

    return response;
  },

  async deleteFaultSignature({ commit }, { id, fault_signature_id }) {
    let response = await deleteData(`/api/requests/${id}/delete_fault_signature`, this.$axios, {
      fault_signature_id: fault_signature_id,
    });

    return response;
  },

  async loadLinkRequests({ commit }, payload = {}) {
    let response = await getData(`/web/requests`, this.$axios, { ...payload });

    commit('SET_LINK_REQUESTS', response.data);
    commit('SET_LINK_REQUEST_PAGE_INFORMATION', combinePageInformation(response));

    return response;
  },
  async uploadFileRequestNote({ commit }, { payload, id }) {
    let response = await postData(`/api/requests/${id}/add_remark_attachment`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },
  async signatureRequestByEmail({ commit }, { id, formData }) {
    let response = await postData(`/api/requests/${id}/signature_request_by_email`, this.$axios, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },
  async loadAllRequestors({ commit }, payload = {}) {
    let response = await getData(`/web/requests/request_by`, this.$axios, {
      ...payload,
    });
    commit('SET_REQUESTORS', response.data);
    return response;
  },
  async loadMoreRequestors({ commit, getters }, payload = {}) {
    let response = await getData(`/web/requests/request_by`, this.$axios, {
      ...payload,
    });

    const currentRequestors = getters.getRequestors;
    const newRequestors = response.data;
    const mergedRequestors = mergeArraysUniqueById(currentRequestors, newRequestors);

    commit('SET_REQUESTORS', mergedRequestors);
    return response;
  },
  async loadSelectedRequestors({ getters, commit }, payload = {}) {
    let response = await getData(`/web/requests/request_by`, this.$axios, {
      ...payload,
    });

    const currentRequestors = getters.getRequestors;
    const newRequestors = response.data;
    const mergedRequestors = mergeArraysUniqueById(newRequestors, currentRequestors);

    commit('SET_REQUESTORS', mergedRequestors);
    return response;
  },
  setIsShowModalRequestSignature({ commit }, payload) {
    commit('SET_IS_SHOW_MODAL_REQUEST_SIGNATURE', payload);
  },
  setRequests({ commit }, requests) {
    commit('SET_REQUESTS', requests);
  },

  setIsLoaded({ commit }, isLoaded) {
    commit('SET_IS_LOADED', isLoaded);
  },
  setIsRequestGutterLoaded({ commit }, isRequestGutterLoaded) {
    commit('SET_IS_REQUEST_GUTTER_LOADED', isRequestGutterLoaded);
  },
  setIsShowRequestFilter({ commit }, isShowRequestFilter) {
    commit('SET_IS_SHOW_REQUEST_FILTER', isShowRequestFilter);
  },
  setTotalPage({ commit }, totalPage) {
    commit('SET_TOTAL_PAGE', totalPage);
  },
  setRequestFilterCondition({ commit }, requestFilterCondition) {
    commit('SET_REQUEST_FILTER_CONDITION', requestFilterCondition);
  },
  resetChosenRequest({ commit }) {
    commit('SET_CHOSEN_REQUEST', null);
  },
  setChosenRequest({ commit, getters }, chosenRequest) {
    commit('SET_CHOSEN_REQUEST', chosenRequest);

    let newData = { ...$nuxt.$route.query };
    newData['request_id'] = chosenRequest;

    $nuxt.$router.push({
      query: newData,
    });
  },
  pushReportFaultImages({ commit }, id) {
    commit('PUSH_REPORT_FAULTS_IMAGES', id);
  },
  setReportFaultImages({ commit }, value = []) {
    commit('SET_REPORT_FAULTS_IMAGES', value);
  },
  setSelectedLinkRequests({ commit }, value = []) {
    commit('SET_SELECTED_LINK_REQUESTS', value);
  },
  setFilterPayload({ commit }, payload) {
    commit('SET_FILTER_PAYLOAD', payload);
  },
  setSelectedLinkOtherRequests({ commit, state }, payload) {
    const data = {};
    data.ids = [...state.selected_link_other_requests.ids, payload.id];
    data.case_ids = [...state.selected_link_other_requests.case_ids, payload.case_id];
    commit('SET_SELECTED_LINK_OTHER_REQUESTS', data);
  },
  resetSelectedLinkOtherRequests({ commit }) {
    commit('SET_SELECTED_LINK_OTHER_REQUESTS', { ids: [], case_ids: [] });
  },
  setAllSelectedLinkOtherRequests({ commit }, payload) {
    commit('SET_SELECTED_LINK_OTHER_REQUESTS', payload);
  },
  removeSelectedLinkOtherRequests({ commit, state }, payload) {
    const data = {};
    data.ids = state.selected_link_other_requests.ids.filter((item) => item !== payload.id);
    data.case_ids = state.selected_link_other_requests.case_ids.filter((item) => item !== payload.case_id);
    commit('SET_SELECTED_LINK_OTHER_REQUESTS', data);
  },
  setIsCreateRequestLoading({ commit }, payload) {
    commit('SET_IS_CREATE_REQUEST_LOADING', payload);
  },
  async linkRequestToOtherRequest({ commit, getters }, { id, formData }) {
    let response = await postData(`/api/requests/${id}/link_requests`, this.$axios, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },
  async loadMoreLocationTags({ commit, getters }, payload) {
    let response = await getData(`/api/fault_locations`, this.$axios, payload);

    let ids = getters.getLocationTags.map((item) => item.id);
    let locationTagsToAdd = response.data.reduce(
      (preVal, curVal) => (!ids.includes(curVal.id) ? [...preVal, curVal] : preVal),
      [],
    );

    commit('SET_LOAD_MORE_LOCATION_TAGS', locationTagsToAdd);
    return response;
  },

  async loadMoreFaultTypes({ commit, getters }, payload) {
    let response = await getData(`/api/facilities`, this.$axios, payload);

    let ids = getters.getFaultTypes.map((item) => item.id);
    let faultTypesToAdd = response.data.reduce(
      (preVal, curVal) => (!ids.includes(curVal.id) ? [...preVal, curVal] : preVal),
      [],
    );

    commit('SET_LOAD_MORE_FAULT_TYPES', faultTypesToAdd);
    return response;
  },

  async updateRequireAttachment({ commit, state, dispatch }, payload = {}) {
    let response = await postData(`/api/requests/${payload.id}/require_attachment`, this.$axios, payload.payload);

    dispatch('getRequest', payload.id);

    return response;
  },

  async exportCsv({ commit }, payload) {
    let response = await getData(`/api/requests/export_csv`, this.$axios, { ...payload });

    return response;
  },

  async loadRequestHistory({ commit }, { id, payload = {} }) {
    let response = await getData(`api/requests/${id}/histories`, this.$axios, payload);

    commit('SET_REQUEST_HISTORY', response.data);
    return response;
  },

  async uploadProcessingAttachmentV2({ commit }, { id, file }) {
    let response = await postData(`/api_v2/requests/${id}/upload_processing_attachment`, this.$axios, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response;
  },

  async uploadCompleteAttachmentV2({ commit }, { id, file }) {
    let response = await postData(`/api_v2/requests/${id}/upload_completion_attachment`, this.$axios, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response;
  },

  async destroyAttachmentV2({ commit }, payload = {}) {
    let response = await deleteData(`/api_v2/requests/delete_attachment`, this.$axios, payload);

    return response;
  },

  async getTimestampImage({ commit }, payload) {
    let response = await postData(`api/internal_tools/image_with_time_stamp`, this.$axios, payload);

    return response;
  },

  async getGeolocationImage({ commit }, payload) {
    let response = await postData(`api/internal_tools/geolocation_of_a_image`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response;
  },

  async uploadCSV({ commit }, payload) {
    let response = await postData(`api/requests/import_csv`, this.$axios, payload);

    return response;
  },

  async loadDetailsFaultSubTypes({ commit }, id) {
    let response = await getData(`api/fault_sub_type_tags/facility/${id}`, this.$axios);

    return response;
  },

  async loadRequestPdfData({}, id) {
    let response = await getData(`api/requests/pdf_request_datas`, this.$axios, { id });
    return response;
  },

  async exportRequestPdf({}, payload) {
    let response = await postData(`/nodejs_services/pdf/request`, this.$axios, payload);
    return response;
  },

  async getRequestMailData({}, mailId) {
    let response = await postData(`api/requests/download_mail`, this.$axios, {
      mail_message_id: mailId,
    });

    return response;
  },

  async getBulkRequestPdfData({}, requestIds) {
    let response = await getData(`api/requests/bulk_pdf_request_datas`, this.$axios, {
      ids: requestIds,
    });

    return response;
  },

  async getDueDateReminders({ commit }) {
    let response = await getData(`api/requests/due_date_reminders`, this.$axios);

    commit('SET_DUE_DATE_REMINDERS', response.data);
  },

  async setDueDateReminder({ commit }, { id, payload }) {
    let response = await postData(`api/requests/${id}/set_due_date`, this.$axios, payload);

    return response;
  },

  async removeDueDateReminder({ commit }, id) {
    let response = await deleteData(`api/requests/${id}/remove_due_date`, this.$axios);

    return response;
  },

  setSelectedRequests({ commit, getters }, incomeRequest) {
    const selectedRequests = getters.getSelectedRequest;

    let updatedSelectedRequests = selectedRequests.filter((request) => request.id !== incomeRequest.id);
    if (!selectedRequests.some((request) => request.id === incomeRequest.id)) {
      updatedSelectedRequests.push(incomeRequest);
    }

    commit('SET_SELECTED_REQUESTS', updatedSelectedRequests.filter(Boolean));
  },

  setBulkSelectedRequests({ commit, getters }) {
    const chosenRequest = getters.getChosenRequest;
    const requests = getters.getRequests;

    const chosenRequestIndex = requests.findIndex((request) => request.id === chosenRequest);
    const newIdsArray = requests.slice(chosenRequestIndex, chosenRequestIndex + 25).map((request) => request);

    commit('SET_SELECTED_REQUESTS', newIdsArray);
  },

  clearBulkSelectedRequests({ commit }) {
    commit('SET_SELECTED_REQUESTS', []);
  },

  async updateChecklistCompletionRequest({ commit }, { id, payload }) {
    let response = await postData(`/api/requests/${id}/complete_checklist`, this.$axios, payload);

    return response;
  },
};

export const getters = {
  getCurrentPage(state) {
    return state.currentPage;
  },
  getPageSize(state) {
    return state.pageSize;
  },
  getRequestFilterCondition(state) {
    return state.requestFilterCondition;
  },
  getRequests(state) {
    return state.requests;
  },
  getRequest(state) {
    return state.request;
  },
  getChosenRequest(state) {
    return state.chosenRequest;
  },
  getLocationTags(state) {
    return state.requestAllLocationTag;
  },
  getFaultTypes(state) {
    return state.requestAllFaultType;
  },
  getSelectedRequest(state) {
    return state.selected_requests;
  },
  getRequestors(state) {
    return state.requestors;
  },
};
