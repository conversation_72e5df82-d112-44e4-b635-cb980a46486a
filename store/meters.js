import { getData, postData, putData, deleteData, combinePageInformation } from '@/utils/store-utils';
import { mergeArraysUniqueById } from '../utils/helper';

export const state = () => ({
  meters: [],
  meter: {},
  page_information: {
    page: 1,
    per_page: 10,
    total_pages: 1,
    count: 0,
  },
  isShowDrawer: false,
  currentDrawerGutter: 'edit',
  isMeterAvailable: false,
  currentMeter: {},
  isShowImportCsvModal: false,
  minimumAcceptableNextReadingScore: undefined,
  maximumAcceptableNextReadingScore: undefined,
  schedules: [],
  custom_recurrence: {
    recurrence_day_of_month: 0,
    recurrence_week_of_month: 0,
    recurrence_day_in_week: 0,
  },
  recurrence: {},
  reminder_emails: [],
  is_send_reminder_emails: false,
  is_push_reminder_notifications: false,
  reminder_manager_ids: [],
});

export const mutations = {
  SET_METERS(state, meters) {
    state.meters = meters;
  },
  SET_METER(state, meter) {
    state.meter = meter;
  },
  ADD_MORE_METERS(state, meters) {
    state.meters = [...state.meters, ...meters];
  },
  SET_PAGE_INFORMATION(state, page_information) {
    state.page_information = page_information;
  },
  SET_IS_SHOW_DRAWER(state, isShowDrawer) {
    state.isShowDrawer = isShowDrawer;
  },
  SET_CURRENT_DRAWER_GUTTER(state, currentDrawerGutter) {
    state.currentDrawerGutter = currentDrawerGutter;
  },
  SET_IS_METER_AVAILABLE(state, payload) {
    state.isMeterAvailable = payload.available;
  },
  SET_CURRENT_METER(state, currentMeter) {
    state.currentMeter = currentMeter;
  },
  SET_IS_SHOW_IMPORT_CSV_MODAL(state, isShowImportCsvModal) {
    state.isShowImportCsvModal = isShowImportCsvModal;
  },
  SET_MINIMUM_ACCEPTABLE_NEXT_READING_SCORE(state, minimumAcceptableNextReadingScore) {
    state.minimumAcceptableNextReadingScore = minimumAcceptableNextReadingScore;
  },
  SET_MAXIMUM_ACCEPTABLE_NEXT_READING_SCORE(state, maximumAcceptableNextReadingScore) {
    state.maximumAcceptableNextReadingScore = maximumAcceptableNextReadingScore;
  },
  SET_REMINDER_EMAILS(state, reminder_emails) {
    state.reminder_emails = reminder_emails;
  },
  ADD_REMINDER_EMAIL(state, reminder_email) {
    state.reminder_emails.push(reminder_email);
  },
  REMOVE_REMINDER_EMAIL(state, reminder_email) {
    state.reminder_emails = state.reminder_emails.filter((email) => email.id !== reminder_email.id);
  },
  SET_CUSTOM_RECURRENCE(state, custom_recurrence) {
    state.custom_recurrence = custom_recurrence;
  },
  SET_RECURRENCE(state, recurrence) {
    state.recurrence = recurrence;
  },
  SET_SCHEDULES(state, schedules) {
    state.schedules = schedules;
  },
  SET_IS_SEND_REMINDER_EMAILS(state, is_send_reminder_emails) {
    state.is_send_reminder_emails = is_send_reminder_emails;
  },
  SET_IS_PUSH_REMINDER_NOTIFICATIONS(state, is_push_reminder_notifications) {
    state.is_push_reminder_notifications = is_push_reminder_notifications;
  },
  SET_REMINDER_MANAGER_IDS(state, reminder_manager_ids) {
    state.reminder_manager_ids = reminder_manager_ids;
  },
};

export const actions = {
  async loadAll({ commit }, payload = {}) {
    let response = await getData(`/api/meters`, this.$axios, payload);

    commit('SET_METERS', response.data);
    commit('SET_PAGE_INFORMATION', combinePageInformation(response));

    return response;
  },

  async loadMoreMeters({ commit, getters }, payload = {}) {
    let response = await getData(`/api/meters`, this.$axios, payload);

    const currentMeters = getters.getMeters;
    const newMeters = response.data;
    const mergedMeters = mergeArraysUniqueById(currentMeters, newMeters);

    commit('SET_METERS', mergedMeters);
    return response;
  },

  async loadMeter({ commit }, id) {
    let response = await getData(`/api/meters/${id}`, this.$axios);

    if (response) {
      commit('SET_METER', response);
      commit('SET_IS_PUSH_REMINDER_NOTIFICATIONS', response.is_push_reminder_notifications);
      commit('SET_IS_SEND_REMINDER_EMAILS', response.is_send_reminder_emails);
      if (response.reminder_emails) {
        let reminderEmails = response.reminder_emails.map((email, index) => {
          return {
            id: index + 1,
            email,
          };
        });
        commit('SET_REMINDER_EMAILS', reminderEmails);
      }
      if (response.reminder_manager_ids?.length > 0) {
        commit('SET_REMINDER_MANAGER_IDS', response.reminder_manager_ids);
      }
    }

    return response;
  },

  async loadAndAddMeters({ commit, getters, state }, payload = {}) {
    let response = await getData(`/api/meters`, this.$axios, payload);
    const currentMeters = getters.getMeters;
    let combinedMeters = currentMeters.concat(
      response.data.filter((newMeter) => !currentMeters.some((meter) => meter.id === newMeter.id)),
    );
    commit('SET_METERS', combinedMeters);
    return response;
  },

  async loadMeterByIds({ commit }, payload = {}) {
    let response = await getData(`/api/meters`, this.$axios, { ...payload, per_page: 100 });
    return response;
  },

  addMeterToMeters({ commit, getters, state }, payload = []) {
    const currentMeters = getters.getMeters;
    let combinedMeters = currentMeters.concat(
      payload.data.filter((newMeter) => !currentMeters.some((meter) => meter.id === newMeter.id)),
    );
    commit('SET_METERS', combinedMeters);
  },

  async createMeter({ commit }, payload) {
    let response = await postData(`/api/meters/`, this.$axios, payload);

    return response;
  },

  async updateMeter({ commit }, { id, payload }) {
    let response = await putData(`/api/meters/${id}`, this.$axios, payload);

    return response;
  },

  async deleteMeter({ commit }, id) {
    let response = await deleteData(`/api/meters/${id}`, this.$axios);
    return response;
  },
  async isMeterAvailable({ commit }) {
    let response = await getData(`/api/meters/check_available`, this.$axios);
    commit('SET_IS_METER_AVAILABLE', response);
    return response;
  },

  async getQrCodeImage({ commit }, id) {
    let response = await getData(`api/meters/${id}/qr_code`, this.$axios);

    return response;
  },

  async loadLinkedMeter({ commit }, id) {
    let response = await getData(`/api/meters/${id}`, this.$axios);
    commit('SET_METERS', [response]);

    return response;
  },

  async importCsv({ commit }, payload = {}) {
    let response = await postData(`api/meter_readings/import_csv`, this.$axios, payload);

    return response;
  },

  async getAverageScore({ commit }, { id, payload = {} }) {
    let response = await getData(`api/meters/average_score/${id}`, this.$axios, payload);

    if (response?.data) {
      commit('SET_MINIMUM_ACCEPTABLE_NEXT_READING_SCORE', response.data.minimum_acceptable_next_reading_score);
      commit('SET_MAXIMUM_ACCEPTABLE_NEXT_READING_SCORE', response.data.maximum_acceptable_next_reading_score);
    }

    return response;
  },

  async getReminderSchedule({ commit, state }) {
    let response = await getData(`/api/meters/schedule_reminder_in_future`, this.$axios, {
      recurrence_start_datetime: state.custom_recurrence.recurrence_start_datetime,
      recurrence_kind: state.custom_recurrence.recurrence_kind,
      recurrence_number_repeat: state.custom_recurrence.recurrence_number_repeat || 1,
      recurrence_day_of_week: JSON.stringify(state.custom_recurrence.recurrence_day_of_week),
      recurrence_day_of_month: state.custom_recurrence.recurrence_day_of_month || 0,
      recurrence_week_of_month: state.custom_recurrence.recurrence_week_of_month,
      recurrence_day_in_week: state.custom_recurrence.recurrence_day_in_week,
      recurrence_never_stop: state.custom_recurrence.recurrence_never_stop,
      recurrence_enabled_end_on: state.custom_recurrence.recurrence_enabled_end_on,
      recurrence_end_on: state.custom_recurrence.recurrence_end_on,
      recurrence_enabled_number_limit: state.custom_recurrence.recurrence_enabled_number_limit,
      recurrence_number_limit: state.custom_recurrence.recurrence_number_limit || 0,
    });

    commit('SET_SCHEDULES', response.schedules);
    return response;
  },

  async loadRecurrenceSetting({ commit }, { meter_id, payload = {} }) {
    let response = await getData(`/api/meters/${meter_id}/recurrence_setting`, this.$axios, payload);

    let recurrence = {
      id: response.data.id,
      recurrence_start_datetime: moment(response.data.recurrence_start_datetime, 'X'),
      recurrence_kind: response.data.recurrence_kind,
      recurrence_number_repeat: response.data.recurrence_number_repeat,
      recurrence_day_of_week: response.data.recurrence_day_of_week,
      recurrence_day_of_month: response.data.recurrence_day_of_month,
      recurrence_week_of_month: response.data.recurrence_week_of_month,
      recurrence_day_in_week: response.data.recurrence_day_in_week,
      recurrence_never_stop: response.data.recurrence_never_stop,
      recurrence_enabled_end_on: response.data.recurrence_enabled_end_on,
      recurrence_end_on: moment(response.data.recurrence_end_on, 'X'),
      recurrence_enabled_number_limit: response.data.recurrence_enabled_number_limit,
      recurrence_number_limit: response.data.recurrence_number_limit,
    };

    commit('SET_CUSTOM_RECURRENCE', recurrence);
    commit('SET_RECURRENCE', response.data);
    return response;
  },

  setCustomRecurrence({ commit }, custom_recurrence) {
    commit('SET_CUSTOM_RECURRENCE', custom_recurrence);
  },

  async createMeterReminderRecurrence({ commit, state }, meter_id) {
    let payload = {
      recurrence_start_datetime: state.custom_recurrence.recurrence_start_datetime,
      recurrence_kind: state.custom_recurrence.recurrence_kind,
      recurrence_number_repeat: state.custom_recurrence.recurrence_number_repeat,
      recurrence_day_of_week: JSON.stringify(state.custom_recurrence.recurrence_day_of_week),
      recurrence_day_of_month: state.custom_recurrence.recurrence_day_of_month,
      recurrence_week_of_month: state.custom_recurrence.recurrence_week_of_month,
      recurrence_day_in_week: state.custom_recurrence.recurrence_day_in_week,
      recurrence_never_stop: state.custom_recurrence.recurrence_never_stop,
      recurrence_enabled_end_on: state.custom_recurrence.recurrence_enabled_end_on,
      recurrence_end_on: state.custom_recurrence.recurrence_end_on,
      recurrence_enabled_number_limit: state.custom_recurrence.recurrence_enabled_number_limit,
      recurrence_number_limit: state.custom_recurrence.recurrence_number_limit,
      recurrence_next_datetime: state.custom_recurrence.recurrence_next_datetime || JSON.stringify([]),
    };

    let response = await postData(`/api/meters/${meter_id}/reminder_recurrence`, this.$axios, payload);
    commit('SET_CUSTOM_RECURRENCE', response.data);
    return response;
  },

  setIsShowDrawer({ commit }, status) {
    commit('SET_IS_SHOW_DRAWER', status);
  },

  setCurrentDrawerGutter({ commit }, key) {
    commit('SET_CURRENT_DRAWER_GUTTER', key);
  },
  setCurrentMeter({ commit }, currentMeter) {
    commit('SET_CURRENT_METER', currentMeter);
  },
  setIsShowImportCsvModal({ commit }, isShowImportCsvModal) {
    commit('SET_IS_SHOW_IMPORT_CSV_MODAL', isShowImportCsvModal);
  },
  setReminderEmails({ commit }, reminder_emails) {
    commit('SET_REMINDER_EMAILS', reminder_emails);
  },
  addReminderEmail({ commit }, reminder_email) {
    commit('ADD_REMINDER_EMAIL', reminder_email);
  },
  removeReminderEmail({ commit }, reminder_email) {
    commit('REMOVE_REMINDER_EMAIL', reminder_email);
  },
  setIsSendReminderEmails({ commit }, is_send_reminder_emails) {
    commit('SET_IS_SEND_REMINDER_EMAILS', is_send_reminder_emails);
  },
  setIsPushReminderNotifications({ commit }, is_push_reminder_notifications) {
    commit('SET_IS_PUSH_REMINDER_NOTIFICATIONS', is_push_reminder_notifications);
  },
  setReminderManagerIds({ commit }, reminder_manager_ids) {
    commit('SET_REMINDER_MANAGER_IDS', reminder_manager_ids);
  },
};

export const getters = {
  getMeters(state) {
    return state.meters;
  },
};
