import { getData, postData, deleteData, combinePageInformation, putData } from '@/utils/store-utils';

export const state = () => ({
  checklists: [],
  checklist: {},
  checklists_sign_off: [],
  page_information: {
    page: 1,
    per_page: 10,
    count: 10,
  },
  page_information_sign_off: {
    page: 1,
    per_page: 10,
    count: 10,
  },
  histories: [],
  calendar: [],
  pending_calendar: [],
  calendar_page_information: {
    page: 1,
    per_page: 10,
  },
  isShowDrawerChecklistPending: false,
  currentDrawerChecklistPendingGutter: 'edit',
  isShowDrawerChecklistReviewing: false,
  currentDrawerChecklistReviewingGutter: 'review',
  rowOfChecklists: [],
  isLoadingCalendar: true,
  checklistImages: [],
  currentTab: '1',
  isShowModalCancelChecklist: false,
  isShowModalAddRemarks: false,
  isShowModalSignOff: false,
  isShowModalUpdateStatus: false,
  checklistIds: [],
  isLinkedFromMeter: false,
  currentChecklistId: -1,
  isLoadingSelectedOptions: false,
  isLoadingBulkDownloadPdf: false,
  filterPayload: {},
  multiSignoffStepStatus: [],
  currentFormData: null,
  scoreTagRenderKey: 0,
  checklistRowsRenderKey: 0,
  rowMetadata: {},
  rowValues: {},
  currentAction: 'checklist',
  scoringGridOverallScoreRenderKey: 0,
  scoringFormulaUpdateRenderKey: 0,
});

export const mutations = {
  SET_CURRENT_TAB(state, currentTab) {
    state.currentTab = currentTab;
  },
  SET_HISTORIES(state, histories) {
    state.histories = histories;
  },
  SET_CHECKLISTS(state, checklists) {
    state.checklists = checklists;
  },
  SET_CHECKLIST(state, checklist) {
    state.checklist = checklist;
  },
  SET_CHECKLISTS_SIGN_OFF(state, checklists) {
    state.checklists_sign_off = checklists;
  },
  SET_CALENDAR(state, calendar) {
    state.calendar = calendar;
  },
  SET_PENDING_CALENDAR(state, pending_calendar) {
    state.pending_calendar = pending_calendar;
  },
  SET_PAGE_INFORMATION(state, page_information) {
    state.page_information = page_information;
  },
  SET_PAGE_INFORMATION_SIGN_OFF(state, page_information) {
    state.page_information_sign_off = page_information;
  },
  SET_CALENDAR_PAGE_INFOMATION(state, calendar_page_information) {
    state.calendar_page_information = calendar_page_information;
  },
  SET_IS_SHOW_DRAWER_CHECKLIST_PENDING(state, isShowDrawerChecklistPending) {
    state.isShowDrawerChecklistPending = isShowDrawerChecklistPending;
  },
  SET_IS_SHOW_DRAWER_CHECKLIST_REVIEWING(state, isShowDrawerChecklistReviewing) {
    state.isShowDrawerChecklistReviewing = isShowDrawerChecklistReviewing;
  },
  SET_CURRENT_DRAWER_CHECKLIST_PENDING_GUTTER(state, currentDrawerChecklistPendingGutter) {
    state.currentDrawerChecklistPendingGutter = currentDrawerChecklistPendingGutter;
  },
  SET_CURRENT_DRAWER_CHECKLIST_REVIEWING_GUTTER(state, currentDrawerChecklistReviewingGutter) {
    state.currentDrawerChecklistReviewingGutter = currentDrawerChecklistReviewingGutter;
  },
  SET_ROW_OF_CHECKLISTS(state, rowOfChecklists) {
    state.rowOfChecklists = rowOfChecklists;
  },
  SET_ROW_OF_CHECKLIST(state, { index, rowOfChecklist }) {
    state.rowOfChecklists.rows[index] = rowOfChecklist;
  },
  SET_IS_LOADING_CALENDAR(state, isLoadingCalendar) {
    state.isLoadingCalendar = isLoadingCalendar;
  },
  PUSH_CHECKLIST_IMAGES(state, id) {
    state.checklistImages.push(id);
  },
  SET_IS_SHOW_MODAL_CANCEL_CHECKLIST(state, isShowModalCancelChecklist) {
    state.isShowModalCancelChecklist = isShowModalCancelChecklist;
  },
  SET_IS_SHOW_MODAL_ADD_REMARKS(state, isShowModalAddRemarks) {
    state.isShowModalAddRemarks = isShowModalAddRemarks;
  },
  SET_IS_SHOW_MODAL_SIGN_OFF(state, isShowModalSignOff) {
    state.isShowModalSignOff = isShowModalSignOff;
  },
  SET_IS_SHOW_UPDATE_STATUS(state, isShowModalUpdateStatus) {
    state.isShowModalUpdateStatus = isShowModalUpdateStatus;
  },
  SET_CHECKLIST_IDS(state, checklistIds) {
    state.checklistIds = checklistIds;
  },
  SET_SIGN_OFF_CHECKLISTS_AFTER_UPDATED(state, checklists_sign_off) {
    state.checklists_sign_off = checklists_sign_off;
  },
  SET_CHECKLISTS_AFTER_UPDATED(state, checklists) {
    state.checklists = checklists;
  },
  SET_IS_LINKED_FROM_METER(state, isLinkedFromMeter) {
    state.isLinkedFromMeter = isLinkedFromMeter;
  },
  SET_CURRENT_CHECKLIST_ID(state, checklistId) {
    state.currentChecklistId = checklistId;
  },
  SET_IS_LOADING_SELECTED_OPTIONS(state, isLoadingSelectedOptions) {
    state.isLoadingSelectedOptions = isLoadingSelectedOptions;
  },
  SET_IS_LOADING_BULK_DOWNLOAD_PDF(state, isLoadingBulkDownloadPdf) {
    state.isLoadingBulkDownloadPdf = isLoadingBulkDownloadPdf;
  },
  SET_FILTER_PAYLOAD(state, filterPayload) {
    state.filterPayload = filterPayload;
  },
  SET_MULTIPLE_SIGNOFF_STEP_STATUS(state, multiSignoffStepStatus) {
    state.multiSignoffStepStatus = multiSignoffStepStatus;
  },
  SET_CURRENT_FORM_DATA(state, currentFormData) {
    state.currentFormData = currentFormData;
  },
  SET_SCORE_TAG_RENDER_KEY(state, scoreTagRenderKey) {
    state.scoreTagRenderKey = scoreTagRenderKey;
  },
  SET_CHECKLIST_ROWS_RENDER_KEY(state, checklistRowsRenderKey) {
    state.checklistRowsRenderKey = checklistRowsRenderKey;
  },
  SET_ROW_METADATA(state, { index, rowIndex }) {
    state.rowMetadata[`${index}`] = rowIndex;
  },
  SET_ROW_VALUE(state, { rowIndex, blockValues }) {
    state.rowValues[`${rowIndex}`] = { ...blockValues };
  },
  SET_ROW_VALUES(state, rowValues) {
    state.rowValues = rowValues;
  },
  SET_CURRENT_ACTION(state, currentAction) {
    state.currentAction = currentAction;
  },

  SET_ROW_SCORING_GRID(state, { rowIndex, index, rowSubIndex, colIndex, score }) {
    let blocks = state.rowOfChecklists.rows[rowIndex].results.blocks;
    let rowData = blocks[index].content.rows[rowSubIndex].value;

    let colData = rowData[colIndex];
    colData = { ...colData, ...score };
    rowData[colIndex] = colData;
  },

  SET_OVERALL_SCORING_GRID(state, { rowIndex, index, score }) {
    let blocks = state.rowOfChecklists.rows[rowIndex].results.blocks;
    let rowData = blocks[index];

    rowData = { ...rowData, ...score };
    blocks[index] = rowData;
  },

  DUPLICATE_ROW_SCORING_GRID(state, { rowIndex, index, rowSubIndex }) {
    let blocks = state.rowOfChecklists.rows[rowIndex].results.blocks;
    let rowsData = blocks[index].content.rows;

    let dupRow = _.cloneDeep(rowsData[rowSubIndex]);

    dupRow = {
      ...dupRow,
      ...{
        kind: 'duplicated_row',
      },
    };

    rowsData.splice(rowSubIndex + 1, 0, dupRow);
  },

  DELETE_ROW_SCORING_GRID(state, { rowIndex, index, rowSubIndex }) {
    let blocks = state.rowOfChecklists.rows[rowIndex].results.blocks;
    let rowsData = blocks[index].content.rows;

    let newFilterRows = rowsData.filter((item, index) => index != rowSubIndex);
    blocks[index].content.rows = newFilterRows;
  },

  EDIT_ROW_TITLE_SCORING_GRID(state, { rowIndex, index, rowSubIndex, editValue }) {
    let blocks = state.rowOfChecklists.rows[rowIndex].results.blocks;
    let rowsData = blocks[index].content.rows;

    let editRowData = rowsData[rowSubIndex];
    let newEditRowData = { ...editRowData, ...{ title: editValue } };

    rowsData[rowSubIndex] = newEditRowData;
  },

  SET_SCORING_GRID_OVERALL_SCORE_RENDER_KEY(state, value) {
    let newVal = state.scoringGridOverallScoreRenderKey + 1;
    state.scoringGridOverallScoreRenderKey = newVal;
  },

  SET_SCORING_FORMULA_UPDATE_RENDER_KEY(state, value) {
    let newVal = state.scoringFormulaUpdateRenderKey + 1;
    state.scoringFormulaUpdateRenderKey = newVal;
  },

  UPDATE_CHECKLIST_IN_PENDING_CHECKLISTS(state, checklist) {
    state.checklists = state.checklists.map((item) => (item.id === checklist.id ? checklist : item));
  },

  UPDATE_CHECKLIST_IN_SIGNOFF_CHECKLISTS(state, checklist) {
    state.checklists_sign_off = state.checklists_sign_off.map((item) => (item.id === checklist.id ? checklist : item));
  },
};

export const actions = {
  async loadAll({ commit, getters }, payload = {}) {
    let response = await getData(`/api_v2/checklists/list_pending`, this.$axios, {
      ...payload,
    });

    commit('SET_CHECKLISTS', response.data);
    commit('SET_PAGE_INFORMATION', combinePageInformation(response));

    return response;
  },

  async loadAllStatusesChecklists({ commit, getters }, payload = {}) {
    let response = await getData(`/api_v2/checklists`, this.$axios, {
      ...payload,
    });

    commit('SET_CHECKLISTS', response.data);
    commit('SET_PAGE_INFORMATION', combinePageInformation(response));

    return response;
  },

  async loadChecklist({ commit, getters }, id) {
    let response = await getData(`/api/checklists/${id}`, this.$axios);

    commit('SET_CHECKLIST', response);
    return response;
  },

  async loadSignOff({ commit, getters }, payload = {}) {
    let response = await getData(`/api_v2/checklists/list_for_review`, this.$axios, {
      ...payload,
    });

    commit('SET_CHECKLISTS_SIGN_OFF', response.data);
    commit('SET_PAGE_INFORMATION_SIGN_OFF', combinePageInformation(response));

    return response;
  },

  async loadHistories({ commit }, id) {
    let response = await getData(`/api/checklists/${id}/histories`, this.$axios);
    commit('SET_HISTORIES', response.data.histories);
  },

  async loadCalendar({ commit }, payload = {}) {
    let response = await getData(`/api/checklists/calendar`, this.$axios, {
      ...payload,
    });

    commit('SET_CALENDAR', response);

    return response;
  },

  async loadPendingCalendar({ commit }, payload = {}) {
    let response = await getData(`/api/checklists/calendar/get_pending`, this.$axios, {
      ...payload,
    });

    commit('SET_PENDING_CALENDAR', response);

    return response;
  },

  async loadRowOfChecklists({ commit }, id) {
    let response = await getData(`/api/checklists/${id}/rows`, this.$axios);
    commit('SET_ROW_OF_CHECKLISTS', response.data);
    return response;
  },

  async updateChecklist({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/staff_remarks_checklist`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  async getPDFData({ commit }, payload = {}) {
    let response = await getData(
      `/api/checklists/pdf_checklists_datas?checklist_ids=[${payload.checklist_ids}]`,
      this.$axios,
    );

    return response;
  },

  async exportChecklist({}, { payload }) {
    let response = await postData(`/nodejs_services/pdf/checklist`, this.$axios, payload);

    return response;
  },

  async getCsvData({ commit }, payload = {}) {
    let response = await getData(
      `/api/checklists/get_datas_export_csv?ids=[${payload.ids}]&type=${payload.type}`,
      this.$axios,
    );

    return response;
  },

  async exportCsv_v2({ commit }, { payload }) {
    let response = await postData(`/file_services/csv/export/checklist`, this.$axios, payload, {
      headers: {
        'x-api-key': process.env.NUXT_ENV_FILE_SERVICES_API_KEY,
      },
    });

    return response;
  },

  async exportBulkChecklist({ commit }, checklists) {
    commit('SET_IS_LOADING_BULK_DOWNLOAD_PDF', true);
    let socket = new WebSocket(this.$config.websocketPdfUrl);
    let response;
    let _this = this;
    socket.onopen = function() {
      var message;
      console.log('[WEBSOCKET] Connected to server.');
      message = {
        type: 'compress_checklist',
        data: JSON.stringify(checklists),
      };
      return socket.send(JSON.stringify(message));
    };

    socket.onmessage = function(event) {
      response = JSON.parse(event.data);
      if (response?.file) {
        _this.$message.success('Bulk Download Successfully!');
        commit('SET_IS_LOADING_BULK_DOWNLOAD_PDF', false);
      }
      return socket.close();
    };

    socket.onclose = function() {
      return console.log('[WEBSOCKET] Disconnected from server.');
    };
  },

  async deleteChecklist({ commit }, id) {
    const response = await deleteData(`/api/checklists/${id}`, this.$axios);
    return response;
  },

  async saveAsDraft({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/save_as_draft`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  async updateStatusChecklist({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/update_status`, this.$axios, payload);

    return response;
  },

  async uploadChecklistPhoto({ commit }, payload) {
    let response = await postData(`/api/checklists/upload_checklist_photo`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    commit('PUSH_CHECKLIST_IMAGES', response.id);
    return response;
  },

  async sendEmail({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/staff_send_email_checklist`, this.$axios, payload);

    return response;
  },

  async createCorrectiveFault({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/create_corrective_fault`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  async assignStaff({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/assign_staffs_to_checklist`, this.$axios, payload);
    return response;
  },

  async assignManager({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/assign_managers_to_checklist`, this.$axios, payload);
    return response;
  },

  async linkAsset({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/link_asset_to_checklist`, this.$axios, payload);
    return response;
  },

  async linkAssets({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/link_multiple_assets_to_checklist`, this.$axios, payload);
    return response;
  },

  async unlinkAsset({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/unlink_asset_from_checklist`, this.$axios, payload);
    return response;
  },

  async addRemarksChecklists({ commit }, payload) {
    let response = await postData(`/api/checklists/bulk_add_sign_off_remarks`, this.$axios, payload);
    return response;
  },

  async signOffChecklists({ commit }, payload) {
    let response = await postData(`/api/checklists/bulk_update_sign_off_status`, this.$axios, payload);
    return response;
  },

  async linkLocationTags({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/link_location_to_checklist`, this.$axios, payload);
    return response;
  },

  async unlinkLocationTags({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/unlink_location_from_checklist`, this.$axios, payload);
    return response;
  },

  setIsShowDrawerChecklistPending({ commit }, status) {
    commit('SET_IS_SHOW_DRAWER_CHECKLIST_PENDING', status);
  },

  setIsShowDrawerChecklistReviewing({ commit }, status) {
    commit('SET_IS_SHOW_DRAWER_CHECKLIST_REVIEWING', status);
  },

  setIsLoadingCalendar({ commit }, status) {
    commit('SET_IS_LOADING_CALENDAR', status);
  },

  setCurrentDrawerChecklistPendingGutter({ commit }, key) {
    commit('SET_CURRENT_DRAWER_CHECKLIST_PENDING_GUTTER', key);
  },

  setCurrentDrawerChecklistReviewingGutter({ commit }, key) {
    commit('SET_CURRENT_DRAWER_CHECKLIST_REVIEWING_GUTTER', key);
  },

  pushChecklistImages({ commit }, id) {
    commit('PUSH_CHECKLIST_IMAGES', id);
  },

  setChecklists({ commit }, checklists) {
    commit('SET_CHECKLISTS', checklists);
  },

  setChecklistsSignOff({ commit }, checklists) {
    commit('SET_CHECKLISTS_SIGN_OFF', checklists);
  },

  setCurrentTab({ commit }, currentTab) {
    commit('SET_CURRENT_TAB', currentTab);
  },

  setIsShowModalCancelChecklist({ commit }, value) {
    commit('SET_IS_SHOW_MODAL_CANCEL_CHECKLIST', value);
  },

  setIsShowModalAddRemarks({ commit }, value) {
    commit('SET_IS_SHOW_MODAL_ADD_REMARKS', value);
  },

  setIsShowModalSignOff({ commit }, value) {
    commit('SET_IS_SHOW_MODAL_SIGN_OFF', value);
  },

  setIsShowModalUpdateStatus({ commit }, isShowModalUpdateStatus) {
    commit('SET_IS_SHOW_UPDATE_STATUS', isShowModalUpdateStatus);
  },

  setIsLinkedFromMeter({ commit }, isLinkedFromMeter) {
    commit('SET_IS_LINKED_FROM_METER', isLinkedFromMeter);
  },

  setCurrentChecklistId({ commit }, currentChecklistId) {
    commit('SET_CURRENT_CHECKLIST_ID', currentChecklistId);
  },

  setIsLoadingSelectedOptions({ commit }, isLoadingSelectedOptions) {
    commit('SET_IS_LOADING_SELECTED_OPTIONS', isLoadingSelectedOptions);
  },

  setRowOfChecklists({ commit }, rowOfChecklists) {
    commit('SET_ROW_OF_CHECKLISTS', rowOfChecklists);
  },

  setPendingCalendar({ commit }, checklists) {
    commit('SET_PENDING_CALENDAR', checklists);
  },

  async exportCsv({ commit }, payload) {
    let response = await getData(`/api/checklists/export_csv`, this.$axios, payload);

    return response;
  },

  async addRemarksChecklist({ commit }, { id, payload }) {
    let response = await postData(`/api/checklists/${id}/add_sign_off_remarks`, this.$axios, payload);
    return response;
  },

  async getLocationInformation({ commit }, payload) {
    let response = await getData(`/api/checklists/get_site_coordinate`, this.$axios, payload);

    return response;
  },

  setChecklistIds({ commit }, checklist_ids) {
    commit('SET_CHECKLIST_IDS', checklist_ids);
  },

  setSignOffChecklistsAfterUpdated({ commit, getters }, checklist) {
    const checklists = [...getters.getChecklistsSignOff];
    const _checklists = checklists.map((c) => (c.id == checklist.id ? { ...c, ...checklist } : c));
    commit('SET_SIGN_OFF_CHECKLISTS_AFTER_UPDATED', _checklists);
  },

  setChecklistsAfterUpdated({ commit, getters }, checklist) {
    const checklists = [...getters.getChecklists];
    const _checklists = checklists.map((c) => (c.id == checklist.id ? { ...c, ...checklist } : c));
    commit('SET_CHECKLISTS', _checklists);
  },

  setCurrentFormData({ commit, getters }, formData) {
    commit('SET_CURRENT_FORM_DATA', formData);
  },

  setScoreTagRenderKey({ commit, getters }) {
    let key = getters.getScoreTagRenderKey;
    commit('SET_SCORE_TAG_RENDER_KEY', key + 1);
  },

  setChecklistRowRenderKey({ commit, getters }) {
    let key = getters.getChecklistRowsRenderKey;
    commit('SET_CHECKLIST_ROWS_RENDER_KEY', key + 1);
  },

  setRowMetadata({ commit, getters }, { index, rowIndex }) {
    commit('SET_ROW_METADATA', { index, rowIndex });
  },

  setRowValue({ commit, getters }, { rowIndex, blockIndex, rowValue, blockType = undefined }) {
    let rowValues = getters.getRowValues;
    let indexValue = _.get(rowValues, `${rowIndex}`, {});
    let blockValue = _.get(indexValue, `${blockIndex}`);

    if (!_.isNil(blockType) && blockType == 'meter_reading') {
      indexValue = {
        ...indexValue,
        ...{
          [`${blockIndex}`]: { ...blockValue, ...rowValue, ...{ type: 'meter_reading' } },
        },
      };
    } else if (blockValue?.type != 'meter_reading') {
      indexValue = {
        ...indexValue,
        ...{
          [`${blockIndex}`]: rowValue,
        },
      };
    }

    commit('SET_ROW_VALUE', { rowIndex, blockValues: indexValue });
  },

  setRowValues({ commit }, rowValues) {
    commit('SET_ROW_VALUES', rowValues);
  },

  addRowValue({ commit }, { rowIndex, blockValues }) {
    commit('SET_ROW_VALUE', { rowIndex, blockValues });
  },

  async uploadChecklistPhotoV2({ commit }, { id, file }) {
    let response = await postData(`api_v2/checklists/${id}/upload_photo`, this.$axios, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response;
  },

  async removeChecklistPhotoV2({ commit }, { id, payload }) {
    let response = await deleteData(`api_v2/checklists/${id}/delete_photo`, this.$axios, payload);

    return response;
  },

  async bulkDeletePendingChecklists({ commit }, payload) {
    let response = await deleteData(`api/checklists/delete_bulk`, this.$axios, {
      ...payload,
    });
    return response;
  },

  async linkCustomers({ commit }, { id, payload }) {
    let response = await postData(`api/checklists/${id}/linking_the_customers`, this.$axios, payload);

    return response;
  },

  setFilterPayload({ commit }, payload) {
    commit('SET_FILTER_PAYLOAD', payload);
  },

  duplicateChecklistRow({ commit, getters, dispatch }, { rowIndex, newRow = {} }) {
    let checklist = getters.getChecklistRows;
    let checklistRows = [...checklist.rows];
    let newChecklistRow = _.cloneDeep(checklistRows[rowIndex]);

    if (_.isEmpty(newRow)) {
      let items = checklistRows.filter(
        (item) => item.parent_id === newChecklistRow.id && item.kind == 'duplicate_checklist',
      );

      let sub_id = _.isEmpty(items) ? 0 : items[0].sub_id;
      sub_id = sub_id + 1;

      newRow = {
        ...newChecklistRow,
        parent_id: newChecklistRow.id,
        sub_id: sub_id,
        id: newChecklistRow.id + sub_id,
        kind: 'duplicate_checklist',
      };
    }

    newChecklistRow = {
      ...newRow,
      ...{
        description: newRow.description,
        kind: 'duplicate_checklist',
      },
    };
    checklistRows.splice(rowIndex + 1, 0, newChecklistRow);

    checklist = {
      ...checklist,
      ...{
        rows: checklistRows,
      },
    };

    commit('SET_ROW_OF_CHECKLISTS', checklist);

    let rowValues = getters.getRowValues;
    let rowValue = _.get(rowValues, `${newRow.parent_id}`);

    dispatch('addRowValue', { rowIndex: newRow.id, blockValues: { ...rowValue } });
  },

  deleteChecklistRow({ commit, getters }, rowIndex) {
    let checklist = getters.getChecklistRows;
    let checklistRows = checklist.rows.filter((item, index) => index !== rowIndex);

    checklist = {
      ...checklist,
      ...{
        rows: checklistRows,
      },
    };
    commit('SET_ROW_OF_CHECKLISTS', checklist);
  },

  setCurrentAction({ commit }, currentAction) {
    commit('SET_CURRENT_ACTION', currentAction);
  },

  updateChecklistRowDescription({ commit, getters }, { index, description }) {
    let checklist = getters.getChecklistRows;
    let checklistRows = [...checklist.rows];
    let currentRow = { ...checklistRows[index] };

    (currentRow = {
      ...currentRow,
      ...{
        description: description,
      },
    }),
      commit('SET_ROW_OF_CHECKLIST', { index, rowOfChecklist: currentRow });
  },

  updateChecklistInPendingChecklists({ commit, getters }, checklist = {}) {
    commit('UPDATE_CHECKLIST_IN_PENDING_CHECKLISTS', checklist);
  },

  updateChecklistInSignOffChecklists({ commit }, checklist) {
    commit('UPDATE_CHECKLIST_IN_SIGNOFF_CHECKLISTS', checklist);
  },

  async createDuplicateChecklistRow({ commit, getters }, { id, payload }) {
    let response = await postData(`api/checklists/${id}/duplicate_row`, this.$axios, payload);

    return response;
  },

  async updateDuplicateChecklistRow({ commit, getters }, { id, payload }) {
    let response = await putData(`api/checklists/${id}/duplicate_row`, this.$axios, payload);

    return response;
  },

  async deleteDuplicateChecklistRow({ commit, getters }, { id, payload }) {
    let response = await deleteData(`api/checklists/${id}/duplicate_row`, this.$axios, payload);

    return response;
  },

  async exportChecklistCalendar({ commit }, payload) {
    let response = await postData(`/file_services/pdf/export/checklist-calendar`, this.$axios, payload);

    return response;
  },

  async uploadAnImage({ commit }, payload) {
    let response = await postData(`/api/internal_tools/upload_attachment`, this.$axios, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.url;
  },

  async loadMultipleSignOffStepStatus({ commit }) {
    let response = await getData(`api/checklist_process_flows/process_steps`, this.$axios);
    commit('SET_MULTIPLE_SIGNOFF_STEP_STATUS', response.data);
    return response;
  },

  async updateChecklistScoring({ commit }, { id, payload }) {
    let response = await putData(`/api/checklists/${id}/scoring`, this.$axios, payload);
    return response;
  },

  setRowScoringGrid({ commit }, payload) {
    commit('SET_ROW_SCORING_GRID', payload);
  },

  setOverallScoringGrid({ commit }, payload) {
    commit('SET_OVERALL_SCORING_GRID', payload);
  },

  duplicateRowScoringGrid({ commit }, payload) {
    commit('DUPLICATE_ROW_SCORING_GRID', payload);
  },

  deleteRowScoringGrid({ commit }, payload) {
    commit('DELETE_ROW_SCORING_GRID', payload);
  },

  editRowTitleScoringGrid({ commit }, payload) {
    commit('EDIT_ROW_TITLE_SCORING_GRID', payload);
  },

  setScoringGridOverallScoreRenderKey({ commit }) {
    commit('SET_SCORING_GRID_OVERALL_SCORE_RENDER_KEY');
  },

  setScoringFormulaUpdateRenderKey({ commit }) {
    commit('SET_SCORING_FORMULA_UPDATE_RENDER_KEY');
  },

  async setDueDateReminder({ commit }, { id, payload }) {
    let response = await postData(`api/checklists/${id}/set_due_date`, this.$axios, payload);

    return response;
  },
  async removeDueDateReminder({ commit }, id) {
    let response = await deleteData(`api/checklists/${id}/remove_due_date`, this.$axios);

    return response;
  },
};

export const getters = {
  getChecklists(state) {
    return state.checklists;
  },
  getChecklistsSignOff(state) {
    return state.checklists_sign_off;
  },
  getChecklistIds(state) {
    return state.checklistIds;
  },
  getScoreTagRenderKey(state) {
    return state.scoreTagRenderKey;
  },
  getChecklistRowsRenderKey(state) {
    return state.checklistRowsRenderKey;
  },
  getRowValues(state) {
    return state.rowValues;
  },
  getRowValue(state, rowIndex) {
    return state.rowValue[rowIndex];
  },
  getChecklistRows(state) {
    return state.rowOfChecklists;
  },
};
