import { findNavItemsByRole } from '~/utils/meta_data';

export const state = () => ({
  current_menu: {},
  nav_items: [],
  current_request_gutter: {},
  default_page_redirect: {
    manager: '/dashboard',
    admin: '/dashboard',
  },
  sider_width: 244,
  isCollapsedSubMenu: false,
});

export const mutations = {
  SET_CURRENT_MENU(state, current_menu) {
    state.current_menu = current_menu;
  },
  SET_NAV_ITEMS(state, nav_items) {
    state.nav_items = nav_items;
  },
  SET_CURRENT_REQUEST_GUTTER(state, current_request_gutter) {
    state.current_request_gutter = current_request_gutter;
  },
  SET_SIDER_WIDTH(state, sider_width) {
    state.sider_width = sider_width;
  },
  SET_IS_COLLAPSED_SUB_MENU(state, isCollapsedSubMenu) {
    state.isCollapsedSubMenu = isCollapsedSubMenu;
  },
};

export const actions = {
  setCurrentMenu({ commit }, current_menu) {
    commit('SET_CURRENT_MENU', current_menu);
  },
  findNavItems({ commit }, { role, is_custom_role, available_role_routes, agent_management, feature_config }) {
    commit(
      'SET_NAV_ITEMS',
      findNavItemsByRole(role, is_custom_role, available_role_routes, agent_management, feature_config),
    );
  },
  setCurrentRequestGutter({ commit }, current_request_gutter = {}) {
    commit('SET_CURRENT_REQUEST_GUTTER', current_request_gutter);
  },
  setSiderWidth({ commit }, is_collapsed) {
    const sider_width = is_collapsed ? 330 : 494;
    commit('SET_SIDER_WIDTH', sider_width);
  },
  setIsCollapsedSubMenu({ commit }, isCollapsedSubMenu) {
    commit('SET_IS_COLLAPSED_SUB_MENU', isCollapsedSubMenu);
  },
};
