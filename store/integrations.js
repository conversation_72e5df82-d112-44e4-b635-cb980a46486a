import { getData, postData, putData, deleteData, combinePageInformation } from '@/utils/store-utils';
import { TEAMS_SETUP_TYPE } from '../components/Integrations/Core/Teams/TeamsSetupType';

export const state = () => ({
  telegram_bot_info: {},
  line_bot_info: {},
  google_bot_info: {},
  workplace_bot_info: {},
  whatsapp_bot_info: {},
  facebook_bot_info: {},
  widget_bot_info: {},
  slack_bot_info: {},
  teams_bot_info: {
    setupOption: TEAMS_SETUP_TYPE.microsoft_teams.id,
  },
  viber_bot_info: {},
  email_info: {
    emailAddress: '',
    isLoaded: false,
    enable_smart_email_parsing: false,
    prompt: '',
  },
  jmm_info: {},
  jmm_logs: {
    data: [],
    page_information: {
      page: 1,
      per_page: 25,
    },
  },
  exceltec_logs: {
    data: [],
    page_information: {
      page: 1,
      per_page: 25,
    },
  },
  procore_info: {},
  procore_company_projects: [],
  procore_companies: [],
  procore_integrations_status: '',
  procore_login_link: '',
  stripe_account: {},
  stripe_connect_link: null,
  whatsapp_setup_method: 'use_own_number',
  current_whatsapp_tab: 'whatsapp_setting',
  sap_info: {},
  exceltecConfig: {
    email: undefined,
    password: undefined,
    site_id: undefined,
    assign_to_manager_ids: undefined,
  },
  exceltecMappingData: [],
  exceltecLocations: [],
  exceltecCategories: [],
  microsoft365_info: {},
});

export const mutations = {
  SET_TELEGRAM_BOT_INFO(state, telegram_bot_info) {
    state.telegram_bot_info = telegram_bot_info;
  },
  SET_LINE_BOT_INFO(state, line_bot_info) {
    state.line_bot_info = line_bot_info;
  },
  SET_GOOGLE_BOT_INFO(state, google_bot_info) {
    state.google_bot_info = google_bot_info;
  },
  SET_WORKPLACE_BOT_INFO(state, workplace_bot_info) {
    state.workplace_bot_info = workplace_bot_info;
  },
  SET_WHATSAPP_BOT_INFO(state, whatsapp_bot_info) {
    state.whatsapp_bot_info = whatsapp_bot_info;
  },
  SET_FACEBOOK_BOT_INFO(state, facebook_bot_info) {
    state.facebook_bot_info = facebook_bot_info;
  },
  SET_WIDGET_BOT_INFO(state, widget_bot_info) {
    state.widget_bot_info = widget_bot_info;
  },
  SET_SLACK_BOT_INFO(state, slack_bot_info) {
    state.slack_bot_info = slack_bot_info;
  },
  SET_TEAMS_BOT_INFO(state, teams_bot_info) {
    state.teams_bot_info = teams_bot_info;
  },
  SET_VIBER_BOT_INFO(state, viber_bot_info) {
    state.viber_bot_info = viber_bot_info;
  },
  SET_EMAIL_INFO(state, email_info) {
    state.email_info = { ...state.email_info, ...email_info };
  },
  SET_JMM_INFO(state, jmm_info) {
    state.jmm_info = { ...state.jmm_info, ...jmm_info };
  },
  SET_JMM_LOGS(state, jmm_logs) {
    state.jmm_logs = jmm_logs;
  },
  SET_PROCORE_INFO(state, procore_info) {
    state.procore_info = procore_info;
  },
  SET_PROCORE_COMPANY_PROJECTS(state, projects) {
    state.procore_company_projects = projects;
  },
  SET_PROCORE_COMPANIES(state, companies) {
    state.procore_companies = companies;
  },
  SET_PROCORE_INTEGRATIONS_STATUS(state, status) {
    state.procore_integrations_status = status;
  },
  SET_PROCORE_LOGIN_LINK(state, link) {
    state.procore_login_link = link;
  },
  CLEAR_PROCORE_PROJECTS(state) {
    state.procore_company_projects = [];
  },
  SET_STRIPE_ACCOUNT(state, payload) {
    state.stripe_account = payload;
  },
  SET_STRIPE_CONNECT_LINK(state, payload) {
    state.stripe_connect_link = payload;
  },
  SET_WHATS_APP_SETUP_METHOD(state, whatsapp_setup_method) {
    state.whatsapp_setup_method = whatsapp_setup_method;
  },
  SET_CURRENT_WHATSAPP_TAB(state, current_whatsapp_tab) {
    state.current_whatsapp_tab = current_whatsapp_tab;
  },
  SET_SAP_INFO(state, sap_info) {
    state.sap_info = sap_info;
  },
  SET_EXCELTEC_CONFIG(state, exceltecConfig) {
    state.exceltecConfig = exceltecConfig;
  },
  SET_EXCELTEC_MAPPING_DATA(state, exceltecMappingData) {
    state.exceltecMappingData = exceltecMappingData;
  },
  ADD_EXCELTEC_MAPPING_ITEM(state, item) {
    state.exceltecMappingData.unshift(item);
  },
  UPDATE_EXCELTEC_MAPPING_ITEM(state, item) {
    const index = state.exceltecMappingData.findIndex((mapping) => mapping.id === item.id);
    if (index !== -1) {
      state.exceltecMappingData.splice(index, 1, item);
    }
  },
  DELETE_EXCELTEC_MAPPING_ITEM(state, id) {
    const index = state.exceltecMappingData.findIndex((mapping) => mapping.id === id);
    if (index !== -1) {
      state.exceltecMappingData.splice(index, 1);
    }
  },
  SET_EXCELTEC_LOCATIONS(state, exceltecLocations) {
    state.exceltecLocations = exceltecLocations;
  },
  SET_EXCELTEC_CATEGORIES(state, exceltecCategories) {
    state.exceltecCategories = exceltecCategories;
  },
  SET_EXCELTEC_LOGS(state, exceltec_logs) {
    state.exceltec_logs = exceltec_logs;
  },
  SET_MICROSOFT365_INFO(state, microsoft365_info) {
    state.microsoft365_info = microsoft365_info;
  },
};

export const actions = {
  async loadTelegramInfo({ commit }) {
    let response = await getData(`/api/integrations/telegram_bot_info`, this.$axios);

    commit('SET_TELEGRAM_BOT_INFO', response);

    return response;
  },
  async loadViberInfo({ commit }) {
    let response = await getData(`/api/integrations/viber_bot_info`, this.$axios);

    commit('SET_VIBER_BOT_INFO', response);

    return response;
  },
  async linkTelegramBot({ commit }, payload = {}) {
    let response = await postData(`/api/integrations/setup_telegram_bot`, this.$axios, payload);

    return response;
  },
  async unlinkTelegramBot({ commit }) {
    let response = await postData(`/api/integrations/unlink_telegram_bot`, this.$axios);
    commit('SET_TELEGRAM_BOT_INFO', response);

    return response;
  },
  async loadLineInfo({ commit }) {
    let response = await getData(`/api/integrations/line_bot_info`, this.$axios);
    commit('SET_LINE_BOT_INFO', response);

    return response;
  },
  async updateLineBot({ commit, getters }, payload = {}) {
    let response = await postData(`/api/integrations/setup_line_bot`, this.$axios, payload);

    return response;
  },
  async loadGoogleInfo({ commit }) {
    let response = await getData(`/api/integrations/google_bot_info`, this.$axios);
    commit('SET_GOOGLE_BOT_INFO', response);

    return response;
  },
  async updateGoogleBot({ commit, getters }, payload = {}) {
    let response = await postData(`/api/integrations/setup_google_bot`, this.$axios, payload);

    return response;
  },
  async loadWorkplaceInfo({ commit }) {
    let response = await getData(`/api/integrations/workplace_bot_info`, this.$axios);
    commit('SET_WORKPLACE_BOT_INFO', response);

    return response;
  },
  async loadWhatsappInfo({ commit }) {
    let response = await getData(`/api/integrations/whatsapp_bot_info`, this.$axios);
    commit('SET_WHATSAPP_BOT_INFO', response);

    return response;
  },
  async updateWhatsappBot({ commit, getters }, payload = {}) {
    let response = await postData(`/api/integrations/setup_whatsapp_bot`, this.$axios, payload);

    return response;
  },
  async loadFacebookInfo({ commit }) {
    let response = await getData(`/api/integrations/facebook_bot_info`, this.$axios);
    commit('SET_FACEBOOK_BOT_INFO', response);

    return response;
  },
  async facebookUnlinkAccount({ commit }) {
    let response = await postData(`/api/integrations/facebook_bot_unlink_account`, this.$axios);
    commit('SET_FACEBOOK_BOT_INFO', {});

    return response;
  },
  async facebookLinkAccount({ commit }, payload = {}) {
    let response = await postData(`/api/integrations/setup_facebook_bot`, this.$axios, { ...payload });
    commit('SET_FACEBOOK_BOT_INFO', response);

    return response;
  },
  async facebookLinkPage({ commit }, payload = {}) {
    let response = await postData(`/api/integrations/facebook_bot_link_page`, this.$axios, { ...payload });
    commit('SET_FACEBOOK_BOT_INFO', response);

    return response;
  },
  async facebookUnlinkPage({ commit }) {
    let response = await postData(`/api/integrations/facebook_bot_unlink_page`, this.$axios);
    commit('SET_FACEBOOK_BOT_INFO', response);

    return response;
  },
  async loadWidgetInfo({ commit, dispatch }) {
    let response = await getData(`/api/integrations/widget_info`, this.$axios);
    commit('SET_WIDGET_BOT_INFO', response);
    const blocks = Object.keys(response.meta_data).length ? response.meta_data.blocks : [];
    dispatch('meta_data/setMetaData', blocks, { root: true });

    return response;
  },
  async updateWidgetCustomize({ commit }, payload = {}) {
    let response = await putData(`/api/integrations/customize_widget`, this.$axios, payload);
    commit('SET_WIDGET_BOT_INFO', response);

    return response;
  },
  async updatePreChatForm({ commit }, payload = {}) {
    let response = await putData(`/api/integrations/customize_pre_chat_form`, this.$axios, payload);
    commit('SET_WIDGET_BOT_INFO', response);

    return response;
  },
  async loadSlackInfo({ commit }) {
    const response = await getData(`/api/integrations/slack_bot_info`, this.$axios);

    commit('SET_SLACK_BOT_INFO', response);

    return response;
  },
  async unlinkSlackBot({ commit }) {
    const response = await postData(`/api/integrations/unlink_slack_bot`, this.$axios);

    commit('SET_SLACK_BOT_INFO', response);

    return response;
  },
  setTeamsBotInfo({ commit }, payload = {}) {
    commit('SET_TEAMS_BOT_INFO', payload);
  },
  async loadTeamsInfo({ commit, state }) {
    const response = await getData(`/api/integrations/teams_bot_info`, this.$axios);

    commit('SET_TEAMS_BOT_INFO', {
      ...response,
      setupOption: state.teams_bot_info.setupOption || TEAMS_SETUP_TYPE['microsoft_teams'].id,
    });

    return response;
  },
  async updateTeamsBot({ commit }, payload = {}) {
    const response = await postData(`/api/integrations/setup_teams_bot`, this.$axios, payload);

    return response;
  },
  async linkViberBot({ commit }, payload = {}) {
    const response = await postData(`/api/integrations/setup_viber_bot`, this.$axios, payload);

    return response;
  },
  async unlinkViberBot({ commit }) {
    const response = await postData(`/api/integrations/unlink_viber_bot`, this.$axios);

    commit('SET_VIBER_BOT_INFO', response);

    return response;
  },
  async unlinkTeamsBot({ commit }, tenant_id) {
    const response = await postData(`/api/integrations/unlink_teams_bot`, this.$axios, {
      tenant_id,
    });

    commit('SET_TEAMS_BOT_INFO', response);

    return response;
  },

  async linkTeamsToBot({ commit }, payload = {}) {
    const response = await postData(`/api/integrations/sign_in_teams_bot`, this.$axios, payload);

    return response;
  },

  async unlinkGoogleChat({ commit }) {
    const response = await postData(`/api/integrations/unlink_google_bot`, this.$axios);

    return response;
  },

  async unlinkWorkplaceChat({ commit }) {
    const response = await postData(`/api/integrations/unlink_workplace_bot`, this.$axios);

    return response;
  },

  async loadEmailAddress({ commit }, payload = {}) {
    commit('SET_EMAIL_INFO', {
      isLoaded: false,
    });

    const response = await getData(`/api/integrations/get_request_gmail`, this.$axios, payload);

    commit('SET_EMAIL_INFO', {
      isLoaded: true,
      emailAddress: response.data.gmail,
      enable_smart_email_parsing: response.data.enable_smart_email_parsing,
      prompt: response.data.smart_email_parsing_prompt,
    });
  },

  async setEmailAddress({ commit }, payload = {}) {
    let response = await putData(`/api/integrations/set_request_gmail`, this.$axios, payload);

    return response;
  },

  async toggleSmartEmailParsing({ commit, getters }, payload = {}) {
    let response = await putData(`/api/integrations/toggle_smart_email_parsing`, this.$axios, payload);

    commit('SET_EMAIL_INFO', {
      ...getters.getEmailInfo,
      enable_smart_email_parsing: payload.is_enable_smart_email_parsing,
    });

    return response;
  },

  async updateEmailPrompt({ commit, getters }, payload = {}) {
    let response = await putData(`/api/integrations/smart_email_parsing_prompt`, this.$axios, payload);

    commit('SET_EMAIL_INFO', { ...getters.getEmailInfo, prompt: payload.smart_email_parsing_prompt });

    return response;
  },

  async loadJmmInfo({ commit }, payload = {}) {
    let response = await getData(`/api/integrations/jmm_config_info`, this.$axios, payload);

    commit('SET_JMM_INFO', response);
    return response;
  },

  async sendJmmInfo({ commit }, payload = {}) {
    let response = await postData(`/api/integrations/setup_jmm_config`, this.$axios, payload);

    return response;
  },

  async unlinkJmm({ commit }) {
    let response = await postData(`/api/integrations/unlink_jmm_bot`, this.$axios);
    return response;
  },

  async loadJmmLogs({ commit }, payload = {}) {
    let response = await getData(`/api/api_logs`, this.$axios, payload);

    const data = response.data;
    const pageInformation = combinePageInformation(response);
    commit('SET_JMM_LOGS', {
      data: data,
      page_information: pageInformation,
    });

    return response;
  },

  async loadProcoreInfo({ commit }, payload = {}) {
    let response = await getData(`api/integrations/procore/config_info`, this.$axios, payload);

    if (response.login_link) {
      commit('SET_PROCORE_INTEGRATIONS_STATUS', 'UNLINKED');
      commit('SET_PROCORE_LOGIN_LINK', response.login_link);
    } else if (response.companies) {
      commit('SET_PROCORE_INTEGRATIONS_STATUS', 'LINKING');
      commit('SET_PROCORE_COMPANIES', response.companies);
    } else if (response.company_id && response.company_name && response.project_id && response.project_name) {
      commit('SET_PROCORE_INTEGRATIONS_STATUS', 'LINKED');
    }

    commit('SET_PROCORE_INFO', response);
    return response;
  },

  async setupProcoreBot({ commit }, payload = {}) {
    let response = await postData(`api/integrations/procore/setup`, this.$axios, payload).then(() => {
      commit('SET_PROCORE_INTEGRATIONS_STATUS', 'LINKING');
    });

    return response;
  },

  async getProcoreProjects({ commit }, payload = {}) {
    let response = await getData(`api/integrations/procore/projects`, this.$axios, payload);

    commit('SET_PROCORE_COMPANY_PROJECTS', response.projects);
    return response;
  },

  async connectToProcore({ commit }, payload = {}) {
    let response = await postData(`api/integrations/procore/connect`, this.$axios, payload);

    if (response.code === 200) {
      commit('SET_PROCORE_INTEGRATIONS_STATUS', 'LINKED');
    }

    return response;
  },

  async unlinkProcore({ commit }) {
    let response = await deleteData(`api/integrations/procore/unlink`, this.$axios);

    commit('SET_PROCORE_INTEGRATIONS_STATUS', 'UNLINKED');

    return response;
  },

  async loadStripeInfo({ commit }, payload = {}) {
    let response = await getData(`/api/integrations/stripe/config_info`, this.$axios);

    commit('SET_STRIPE_ACCOUNT', response.account || {});
    commit('SET_STRIPE_CONNECT_LINK', response.link_integration);

    return response;
  },

  async unlinkStripe({ commit }) {
    let response = await deleteData(`api/integrations/stripe/unlink`, this.$axios);

    commit('SET_STRIPE_CONNECT_LINK', null);

    return response;
  },

  async unlinkWhatsapp({ commit }) {
    await postData('api/integrations/unlink_whatsapp_bot', this.$axios);
  },

  clearProcoreProject({ commit }) {
    commit('CLEAR_PROCORE_PROJECTS');
  },

  setWhatsAppSetUpMethod({ commit }, whatsapp_setup_method) {
    commit('SET_WHATS_APP_SETUP_METHOD', whatsapp_setup_method);
  },

  setCurrentWhatsAppTab({ commit }, current_whatsapp_tab) {
    commit('SET_CURRENT_WHATSAPP_TAB', current_whatsapp_tab);
  },

  async loadSapInfo({ commit }) {
    let response = await getData(`/api/sap_configs`, this.$axios);

    commit('SET_SAP_INFO', response);
    return response;
  },

  async setupSap({ commit }, payload = {}) {
    let response = await postData(`/api/sap_configs`, this.$axios, payload);
    commit('SET_SAP_INFO', response);
    return response;
  },

  async unlinkSap({ commit }, id) {
    let response = await deleteData(`/api/sap_configs/${id}`, this.$axios);
    commit('SET_SAP_INFO', {});
    return response;
  },

  async syncSap({ commit }) {
    let response = await postData(`/api/sap_configs/sync_orders`, this.$axios);
    return response;
  },

  async updateSap({ commit }, payload = {}) {
    let response = await putData(`/api/sap_configs/${payload.id}`, this.$axios, payload);
    commit('SET_SAP_INFO', response);
    return response;
  },

  async loadExceltecConfig({ commit }) {
    let response = await getData(`/api/integrations/exceltec/config_info`, this.$axios);

    commit('SET_EXCELTEC_CONFIG', response);

    return response;
  },
  async updateExceltecConfig({ commit }, payload = {}) {
    let response = await postData(`/api/integrations/exceltec/setup`, this.$axios, payload);

    return response;
  },
  async loadExceltecMappingData({ commit }, payload = {}) {
    let response = await getData(`/api/fault_type_mappings`, this.$axios, payload);

    commit('SET_EXCELTEC_MAPPING_DATA', response.data);

    return response;
  },
  async addExceltecMappingItem({ commit }, payload = {}) {
    let response = await postData(`/api/fault_type_mappings`, this.$axios, payload);

    commit('ADD_EXCELTEC_MAPPING_ITEM', response.data);

    return response;
  },
  async updateExceltecMappingItem({ commit }, payload = {}) {
    let response = await putData(`/api/fault_type_mappings/${payload.id}`, this.$axios, payload);

    commit('UPDATE_EXCELTEC_MAPPING_ITEM', response.data);

    return response;
  },
  async deleteExceltecMappingItem({ commit }, id) {
    let response = await deleteData(`/api/fault_type_mappings/${id}`, this.$axios);

    commit('DELETE_EXCELTEC_MAPPING_ITEM', id);

    return response;
  },
  async loadExceltecLocations({ commit }, payload = {}) {
    let response = await getData(`/api/integrations/exceltec/locations`, this.$axios, payload);

    commit('SET_EXCELTEC_LOCATIONS', response.data.data.error ? [] : response.data.data);

    return response;
  },
  async loadExceltecCategories({ commit }, payload = {}) {
    let response = await getData(`/api/integrations/exceltec/categories`, this.$axios, payload);

    commit('SET_EXCELTEC_CATEGORIES', response.data.data);

    return response;
  },
  async loadExceltecLogs({ commit }, payload = {}) {
    let response = await getData(`/api/api_logs`, this.$axios, payload);

    const data = response.data;
    const pageInformation = combinePageInformation(response);
    commit('SET_EXCELTEC_LOGS', {
      data: data,
      page_information: pageInformation,
    });

    return response;
  },

  async loadMicrosoft365Info({ commit }, payload = {}) {
    let response = await getData(`/api/dynamics365_configs`, this.$axios, payload);

    commit('SET_MICROSOFT365_INFO', response.data);

    return response;
  },

  async sendMicrosoft365Info({ commit }, payload = {}) {
    let response = await postData(`/api/dynamics365_configs`, this.$axios, payload);

    return response;
  },

  async updateMicrosoft365Info({ commit }, payload = {}) {
    let response = await putData(`/api/dynamics365_configs/${payload.id}`, this.$axios, payload);

    return response;
  },

  async unlinkMicrosoft365({ commit }, id) {
    let response = await deleteData(`/api/dynamics365_configs/${id}`, this.$axios);

    commit('SET_MICROSOFT365_INFO', {});

    return response;
  },
};

export const getters = {
  getEmailInfo: (state) => state.email_info,
};
