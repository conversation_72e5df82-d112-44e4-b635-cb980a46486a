import { getData, deleteData, postData, putData, combinePageInformation } from '@/utils/store-utils';
import {
  TEXT_BLOCK,
  RADIO_BLOCK,
  SIGNATURE_BLOCK,
  IMAGE_BLOCK,
  MULTIPLE_IMAGES_BLOCK,
  DATETIME_BLOCK,
  TOGGLE_BLOCK,
  CHECKBOX_BLOCK,
  FORM_FIELD_OPTION_BLOCK,
  PDF_BLOCK,
  METER_READING_BLOCK,
  SINGLE_CHOICE_DROPDOWN_BLOCK,
  MULTIPLE_CHOICE_DROPDOWN_BLOCK,
  GEOLOCATION_BLOCK,
  PARTS_BLOCK,
  MU<PERSON><PERSON><PERSON>_CHOICE_GRID_BLOCK,
  TICK_BOX_GRID,
  SCORING_GRID,
  SCORING_FORMULA_BLOCK,
  SECTION_BLOCK,
} from '@/utils/meta_data_block_v2.js';
import { updateBlocksInItem } from '@/utils/scoring-helper.js';
import _ from 'lodash';

export const state = () => ({
  checklist_rows: [],
  is_dragging_check: false,
  is_show_edit_block_score: false,
  blockEditing: {},
  maxScoreRenderKey: 0,
  errors: null,
  validateRenderKey: 0,
});

export const mutations = {
  SET_ROWS(state, rows) {
    state.checklist_rows = rows;
  },
  ADD_ROW(state, row) {
    state.checklist_rows.push(row);
  },
  ADD_ROW_CHECK_ITEM(state, payload) {
    switch (payload.key) {
      case 'datetime':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...DATETIME_BLOCK });
        break;
      case 'single_choice_dropdown':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...SINGLE_CHOICE_DROPDOWN_BLOCK });
        break;
      case 'radio':
        const content = payload.options.map((opt) => {
          return {
            title: opt.title,
            value: false,
            score: opt.score,
          };
        });
        const _payload = {
          ...RADIO_BLOCK,
          content: content,
        };
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push(_payload);
        break;
      case 'multiple_choice_dropdown':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...MULTIPLE_CHOICE_DROPDOWN_BLOCK });
        break;
      case 'toggle':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...TOGGLE_BLOCK });
        break;
      case 'image':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...IMAGE_BLOCK });
        break;
      case 'multiple_images':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...MULTIPLE_IMAGES_BLOCK });
        break;
      case 'pdf':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...PDF_BLOCK });
        break;
      case 'checkbox':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push(JSON.parse(JSON.stringify(CHECKBOX_BLOCK)));
        break;
      case 'text':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...TEXT_BLOCK });
        break;
      case 'signature':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...SIGNATURE_BLOCK });
        break;
      case 'geolocation':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({ ...GEOLOCATION_BLOCK });
        break;
      case 'meter_reading':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ...METER_READING_BLOCK,
          options: payload.meterInput,
        });
        break;
      case 'part':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ...PARTS_BLOCK,
          options: payload.partsInput,
        });
        break;
      case 'multiple_choice_grid':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ...MULTIPLE_CHOICE_GRID_BLOCK,
        });
        break;
      case 'tick_box_grid':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ...TICK_BOX_GRID,
        });
        break;
      case 'scoring_grid':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ..._.cloneDeep(SCORING_GRID),
        });
        break;
      case 'scoring_formula':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ...SCORING_FORMULA_BLOCK,
        });
        break;
      case 'section':
        state.checklist_rows[payload.rowIndex].meta_data.blocks.push({
          ...SECTION_BLOCK,
        });
      default:
        return;
    }
  },
  EDIT_CHECK_ITEM_TITLE(state, payload) {
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].title = payload.title;
  },
  EDIT_CHECK_ITEM_COMPULSORY(state, payload) {
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].compulsory = payload.compulsory;
  },
  EDIT_CHECK_ITEM_DUPLICATE(state, payload) {
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].allow_duplicate = payload.value;
  },
  UPDATE_CHECK_ITEM_VALIDATOR(state, { index, rowIndex, validatorData }) {
    state.checklist_rows[rowIndex].meta_data.blocks[index].validator = validatorData;
  },
  DELETE_CHECK_ITEM(state, payload) {
    state.checklist_rows[payload.rowIndex].meta_data.blocks.splice(payload.index, 1);
  },
  DUPLICATE_CHECK_ITEM(state, payload) {
    const duplicatedCheckItem = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index]);
    state.checklist_rows[payload.rowIndex].meta_data.blocks.splice(payload.index + 1, 0, duplicatedCheckItem);
  },
  UPDATE_CHECK_ITEM_OPTION(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    newContent[payload.optIndex].title = payload.value;
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = [...newContent];
  },
  UPDATE_CHECK_ITEM_OPTION_GRID_RESPONSE(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    if (payload.type === 'multiple_choice_grid_column' || payload.type === 'tick_box_grid_column') {
      newContent.columns[payload.optIndex].title = payload.value;
    } else {
      newContent.rows[payload.optIndex].title = payload.value;
    }
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = { ...newContent };
  },
  REMOVE_CHECK_ITEM_OPTION(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    newContent.splice(payload.optIndex, 1);
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = [...newContent];
  },
  ADD_CHECK_ITEM_OPTION(state, payload) {
    let counter = state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content.length + 1;
    const option = {
      title: 'Option ' + counter,
      value: false,
      //default new option score = 0
      score: 0,
    };
    const newContent = [
      ..._.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content),
      option,
    ];
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = [...newContent];
  },
  UPDATE_CHECKLIST_SETTING_ROW_DESCRIPTION(state, payload) {
    state.checklist_rows[payload.rowIndex].description = payload.description;
  },
  UPDATE_CHECKLIST_SETTING_BLOCKS(state, payload) {
    state.checklist_rows[payload.rowIndex].meta_data.blocks = payload.blocks;
  },
  UPDATE_CHECKLIST_SETTING_BLOCK(state, payload) {
    const blocks = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks);
    blocks[payload.index] = payload.block;
    state.checklist_rows[payload.rowIndex].meta_data.blocks = blocks;
  },
  SET_IS_DRAGGING_CHECK(state, value) {
    state.is_dragging_check = value;
  },
  ADD_CHECK_ITEM_OPTION_GRID_ROW(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    const newRow = {
      title: 'Row ' + (newContent.rows.length + 1),
      value: newContent.columns.map((col) => {
        return { value: false };
      }),
    };
    newContent.rows.push(newRow);
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = { ...newContent };
  },
  ADD_CHECK_ITEM_OPTION_SCORING_GRID_ROW(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    const newRow = {
      title: 'Row ' + newContent.rows.length,
      row_type: 'value',
      value: newContent.columns.map((col) => {
        return { value: false };
      }),
    };

    newContent.rows.splice(newContent.rows.length - 1, 0, newRow);
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = { ...newContent };
  },
  ADD_CHECK_ITEM_OPTION_GRID_COLUMN(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    const newColumn = {
      title: 'Column ' + (newContent.columns.length + 1),
    };
    newContent.columns.push(newColumn);
    newContent.rows.forEach((row) => {
      row.value.push({ value: false });
    });
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = { ...newContent };
  },
  ADD_CHECK_ITEM_OPTION_SCORING_GRID_COLUMN(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    const newColumn = {
      title: 'Column ' + (newContent.columns.length + 1),
      max_score: 1,
    };
    newContent.columns.push(newColumn);
    newContent.rows.forEach((row) => {
      row.value.push({ value: false });
    });
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = { ...newContent };
  },
  REMOVE_CHECK_ITEM_OPTION_GRID_RESPONSE(state, payload) {
    const newContent = _.cloneDeep(state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content);
    if (payload.type === 'multiple_choice_grid_column' || payload.type === 'tick_box_grid_column') {
      newContent.columns.splice(payload.optIndex, 1);
    } else {
      newContent.rows.splice(payload.optIndex, 1);
    }
    state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index].content = { ...newContent };
  },
  SET_BLOCK_EDITING(state, payload) {
    if (payload.isShowEditBlockScore) {
      state.blockEditing = {
        ...state.checklist_rows[payload.rowIndex].meta_data.blocks[payload.index],
        ...payload,
      };
      state.is_show_edit_block_score = payload.isShowEditBlockScore;
    } else {
      state.is_show_edit_block_score = false;
      state.blockEditing = {};
    }
  },
  UPDATE_CHECKLIST_SETTING_ROW(state, { index, row }) {
    state.checklist_rows[index] = row;
  },

  SET_MAX_SCORE_COLUMN_SCORING_GRID(state, { rowIndex, index, colIndex, maxScoreValue }) {
    let colData = state.checklist_rows[rowIndex].meta_data.blocks[index].content.columns[colIndex];
    Object.assign(colData, { max_score: maxScoreValue });
  },

  SET_CONFIG_SCORING_GRID(state, { rowIndex, index, config }) {
    let rowData = state.checklist_rows[rowIndex].meta_data.blocks[index];
    Object.assign(rowData, config);
  },

  SET_MAX_SCORE_RENDER_KEY(state) {
    let value = state.maxScoreRenderKey;
    state.maxScoreRenderKey = value + 1;
  },
  SET_ERRORS(state, errors) {
    state.errors = errors;
  },
  SET_VALIDATE_RENDER_KEY(state) {
    state.validateRenderKey++;
  },
};

export const actions = {
  setRows({ commit }, row) {
    commit('SET_ROWS', row);
  },
  addRow({ commit, getters }, row) {
    const rows = getters.getRows;
    const lastRow = rows[rows.length - 1];
    let lastId = lastRow ? lastRow.side_note + 1 : 1;
    commit('ADD_ROW', { ...row, side_note: lastId });
  },
  deleteRow({ commit, getters }, { key, value }) {
    const rows = getters.getRows.filter((r) => r[key] !== value);
    commit('SET_ROWS', rows);
  },
  addRowCheckItem({ commit }, payload) {
    commit('ADD_ROW_CHECK_ITEM', payload);
  },
  editCheckItemTitle({ commit }, payload) {
    commit('EDIT_CHECK_ITEM_TITLE', payload);
  },
  editCheckItemCompulsory({ commit }, payload) {
    commit('EDIT_CHECK_ITEM_COMPULSORY', payload);
  },
  editCheckItemDuplicate({ commit }, payload) {
    commit('EDIT_CHECK_ITEM_DUPLICATE', payload);
  },
  updateCheckItemValidator({ commit }, payload) {
    commit('UPDATE_CHECK_ITEM_VALIDATOR', payload);
  },
  deleteCheckItem({ commit }, payload) {
    commit('DELETE_CHECK_ITEM', payload);
  },
  duplicateCheckItem({ commit }, payload) {
    commit('DUPLICATE_CHECK_ITEM', payload);
  },
  updateCheckItemOption({ commit }, payload) {
    commit('UPDATE_CHECK_ITEM_OPTION', payload);
  },
  updateCheckItemOptionGridResponse({ commit }, payload) {
    commit('UPDATE_CHECK_ITEM_OPTION_GRID_RESPONSE', payload);
  },
  removeCheckItemOption({ commit }, payload) {
    commit('REMOVE_CHECK_ITEM_OPTION', payload);
  },
  removeCheckItemOptionGridResponse({ commit }, payload) {
    commit('REMOVE_CHECK_ITEM_OPTION_GRID_RESPONSE', payload);
  },
  addCheckItemOption({ commit }, payload) {
    commit('ADD_CHECK_ITEM_OPTION', payload);
  },
  addCheckItemOptionGridRow({ commit }, payload) {
    commit('ADD_CHECK_ITEM_OPTION_GRID_ROW', payload);
  },
  addCheckItemOptionScoringGridRow({ commit }, payload) {
    commit('ADD_CHECK_ITEM_OPTION_SCORING_GRID_ROW', payload);
  },
  addCheckItemOptionGridColumn({ commit }, payload) {
    commit('ADD_CHECK_ITEM_OPTION_GRID_COLUMN', payload);
  },
  addCheckItemOptionScoringGridColumn({ commit }, payload) {
    commit('ADD_CHECK_ITEM_OPTION_SCORING_GRID_COLUMN', payload);
  },
  updateChecklistSettingRowDescription({ commit }, payload) {
    commit('UPDATE_CHECKLIST_SETTING_ROW_DESCRIPTION', payload);
  },
  updateChecklistSettingBlocks({ commit }, payload) {
    commit('UPDATE_CHECKLIST_SETTING_BLOCKS', payload);
  },
  updateChecklistSettingBlock({ commit }, payload) {
    commit('UPDATE_CHECKLIST_SETTING_BLOCK', payload);
  },
  duplicateRow({ commit, getters }, { key, value }) {
    const rows = getters.getRows;
    const row = rows.find((r) => r[key] === value);
    const __deepRow = _.cloneDeep(_.pick(row, ['description', 'meta_data']));
    const _row = { ...__deepRow, side_note: row.side_note + 1 };
    const rowIndex = rows.findIndex((r) => r[key] === value);
    const _rows = [
      ...rows.slice(0, rowIndex + 1),
      _row,
      ...rows.slice(rowIndex + 1).map((r) => ({ ...r, side_note: r.side_note + 1 })),
    ];
    commit('SET_ROWS', _rows);
  },
  setIsDraggingCheck({ commit }, value) {
    commit('SET_IS_DRAGGING_CHECK', value);
  },
  setIsShowEditBlockScore({ commit }, payload) {
    commit('SET_BLOCK_EDITING', payload);
  },

  allow_scoring({ commit, getters }) {
    const rows = getters.getRows;
    rows.map((element) => {
      return updateBlocksInItem(element);
    });
    commit('SET_ROWS', rows);
  },

  updateChecklistSettingAllowDuplicate({ commit, getters }, { index, payload }) {
    const rows = getters.getRows;
    let currentRow = { ...rows[index] };
    currentRow = {
      ...currentRow,
      ...payload,
    };

    commit('UPDATE_CHECKLIST_SETTING_ROW', { index, row: currentRow });
  },

  setMaxScoreScoringGrid({ commit }, payload) {
    commit('SET_MAX_SCORE_COLUMN_SCORING_GRID', payload);
  },

  setConfigScoringGrid({ commit }, payload) {
    commit('SET_CONFIG_SCORING_GRID', payload);
  },

  setMaxScoreRenderKey({ commit }) {
    commit('SET_MAX_SCORE_RENDER_KEY');
  },
  setErrors({ commit }, errors) {
    commit('SET_ERRORS', errors);
  },
  setValidateRenderKey({ commit }) {
    commit('SET_VALIDATE_RENDER_KEY');
  },
};

export const getters = {
  getRows(state) {
    return state.checklist_rows;
  },
};
